const Tour = require("../models/tourModel");
const TourDetail = require("../models/tourDetailModel");
const Category = require("../models/categoriesModel");
const Departure = require("../models/departureModel");
const Destination = require("../models/destinationModel");
const Transportation = require("../models/transportationModel");
const { recalculateAndUpdateTourPrice, calculateMinPrice, calculateMaxPrice, } = require("../utils/priceCalculator");
const { hasPermission } = require("../constants/roles");
const { checkPermission } = require("../middleware/permissionMiddleware");

exports.list = async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 3; // Allow dynamic limit from query
    const skip = (page - 1) * limit;
    const filter = { deleted: false };
    let sort = { createdAt: 1 }; // Sort from old to new (new records at bottom)

    // Basic filters
    if (req.query.status) filter.status = req.query.status === "true";
    if (req.query.highlight) filter.highlight = req.query.highlight === "true";
    if (req.query.category) filter.category = req.query.category;
    if (req.query.departure) filter.departure = req.query.departure;
    if (req.query.destination) filter.destination = req.query.destination;
    if (req.query.transportation)
        filter.transportation = req.query.transportation;
    if (req.query.q) filter.title = { $regex: req.query.q, $options: "i" };

    // Date filters
    if (req.query.startDate) {
        filter.startDate = { $gte: new Date(req.query.startDate) };
    }
    if (req.query.endDate) {
        filter.endDate = { $lte: new Date(req.query.endDate) };
    }

    // Advanced date filters for quick filter buttons
    const today = new Date();
    const thirtyDaysFromNow = new Date(
        today.getTime() + 30 * 24 * 60 * 60 * 1000
    );

    // Handle quick filter scenarios
    if (req.query.quickFilter) {
        switch (req.query.quickFilter) {
            case "expiring":
                // Tours expiring within 30 days
                filter.endDate = {
                    $gte: today,
                    $lte: thirtyDaysFromNow,
                };
                filter.status = true;
                break;
            case "expired":
                // Tours that have already ended
                filter.endDate = { $lt: today };
                break;
            case "featured":
                // Featured tours
                filter.highlight = true;
                break;
            case "active":
                // Active tours
                filter.status = true;
                break;
        }
    }

    // Handle "expired" filter from quick filter button
    if (req.query.expired === "true") {
        filter.endDate = { $lt: today };
    }

    // Price sorting
    if (req.query.sortPrice) {
        sort = { totalPrice: req.query.sortPrice === "asc" ? 1 : -1 };
    }

    // Price range filter (if needed in future)
    if (req.query.minPrice || req.query.maxPrice) {
        filter.totalPrice = {};
        if (req.query.minPrice)
            filter.totalPrice.$gte = parseInt(req.query.minPrice);
        if (req.query.maxPrice)
            filter.totalPrice.$lte = parseInt(req.query.maxPrice);
    }

    try {
        const [
            tours,
            total,
            categories,
            departures,
            destinations,
            transportation,
        ] = await Promise.all([
            Tour.find(filter)
                .populate(
                    "category departure destination transportation createdBy updatedBy"
                )
                .sort(sort)
                .skip(skip)
                .limit(limit),
            Tour.countDocuments(filter),
            Category.find({ deleted: false }).sort({ name: 1 }),
            Departure.find({ deleted: false }).sort({ name: 1 }),
            Destination.find({ deleted: false }).sort({ name: 1 }),
            Transportation.find({ deleted: false }).sort({ name: 1 }),
        ]);

        // Load tour details for each tour and calculate pricing info
        const toursWithPricing = await Promise.all(
            tours.map(async (tour) => {
                const tourDetails = await TourDetail.find({
                    tourId: tour._id,
                }).lean();
                const minPrice = calculateMinPrice(tourDetails);
                const maxPrice = calculateMaxPrice(tourDetails);

                return {
                    ...tour.toObject(),
                    tourDetails,
                    minPrice,
                    maxPrice,
                    hasMultiplePrices: tourDetails.length > 1,
                    priceRange:
                        minPrice !== maxPrice
                            ? `${minPrice.toLocaleString()} - ${maxPrice.toLocaleString()}`
                            : minPrice.toLocaleString(),
                };
            })
        );

        const totalPages = Math.ceil(total / limit);

        res.render("tour", {
            tours: toursWithPricing,
            currentPage: page,
            totalPages,
            limit,
            total,
            query: req.query,
            categories,
            departures,
            destinations,
            transportations: transportation,
            userPermissions: res.locals.userPermissions, // Đảm bảo luôn truyền userPermissions
        });
    } catch (error) {
        console.error("Error in tour list:", error);
        res.render("tour", {
            tours: [],
            currentPage: 1,
            totalPages: 1,
            limit,
            total: 0,
            query: req.query,
            categories: [],
            departures: [],
            destinations: [],
            transportations: [],
            userPermissions: res.locals.userPermissions, // Đảm bảo luôn truyền userPermissions
        });
    }
};

// Hiển thị form thêm mới
exports.showAddForm = async (req, res) => {
    const [categories, departures, destinations, transportations] =
        await Promise.all([
            Category.find({ deleted: false }).sort({ name: 1 }),
            Departure.find({ deleted: false }).sort({ name: 1 }),
            Destination.find({ deleted: false }).sort({ name: 1 }),
            Transportation.find({ deleted: false }).sort({ name: 1 }),
        ]);
    res.render("tour/add", {
        categories,
        departures,
        destinations,
        transportations,
        userPermissions: res.locals.userPermissions, // Đảm bảo luôn truyền userPermissions
    });
};

// Xử lý thêm mới
exports.create = async (req, res) => {
    try {
        const {
            title,
            code,
            category,
            departure,
            destination,
            transportation,
            attractions,
            cuisine,
            suitableTime,
            suitableObject,
            vehicleInfo,
            promotion,
            itinerary,
            tourDetails,
        } = req.body;

        // Kiểm tra mã tour đã tồn tại
        if (code) {
            const existingTour = await Tour.findOne({
                code: code,
                deleted: false,
            });

            if (existingTour) {
                const [categories, departures, destinations, transportations] =
                    await Promise.all([
                        Category.find({ deleted: false }).sort({ name: 1 }),
                        Departure.find({ deleted: false }).sort({ name: 1 }),
                        Destination.find({ deleted: false }).sort({ name: 1 }),
                        Transportation.find({ deleted: false }).sort({
                            name: 1,
                        }),
                    ]);

                return res.render("tour/add", {
                    categories,
                    departures,
                    destinations,
                    transportations,
                    error: ["Mã tour đã tồn tại, vui lòng chọn mã khác"],
                    formData: req.body, // Giữ lại dữ liệu form
                    userPermissions: res.locals.userPermissions, // Đảm bảo luôn truyền userPermissions
                });
            }
        }

        let image = "";
        let images = [];

        if (req.files && req.files.length > 0) {
            // Process multiple images - use file.path for Cloudinary or construct path for local storage
            images = req.files.map((file) => {
                // If using Cloudinary, file.path contains the full URL
                // If using local storage, we need to construct the path
                return file.path || "/uploads/" + file.filename;
            });
            // Set the first image as the main image for backward compatibility
            image = images[0];
        }

        // Process itinerary data
        let processedItinerary = [];
        if (itinerary && Array.isArray(itinerary)) {
            processedItinerary = itinerary
                .map((item, index) => {
                    // Handle different possible formats of the day field
                    let dayValue;
                    if (
                        item.day !== undefined &&
                        item.day !== null &&
                        item.day !== ""
                    ) {
                        dayValue = parseInt(item.day);
                    } else {
                        dayValue = index + 1; // Default to sequential numbering
                    }

                    // Ensure day is a valid number, default to index + 1 if invalid
                    const validDay =
                        !isNaN(dayValue) && dayValue > 0 ? dayValue : index + 1;

                    return {
                        day: validDay,
                        title: item.title || "",
                        details: item.details || "",
                    };
                })
                .filter((item) => item.title && item.title.trim() !== ""); // Remove empty itinerary items
        }

        // Determine start and end dates from tour details
        let startDate = null;
        let endDate = null;

        if (
            tourDetails &&
            Array.isArray(tourDetails) &&
            tourDetails.length > 0
        ) {
            // Find earliest start date and latest end date
            const validDetails = tourDetails.filter(
                (detail) => detail.dayStart && detail.dayReturn
            );
            if (validDetails.length > 0) {
                const startDates = validDetails.map(
                    (detail) => new Date(detail.dayStart)
                );
                const endDates = validDetails.map(
                    (detail) => new Date(detail.dayReturn)
                );

                startDate = new Date(Math.min(...startDates));
                endDate = new Date(Math.max(...endDates));
            }
        }

        // Create tour
        const tourData = {
            title,
            code,
            category,
            departure,
            destination,
            transportation,
            attractions,
            cuisine,
            suitableTime,
            suitableObject,
            vehicleInfo,
            promotion,
            itinerary: processedItinerary,
            status: true, 
            highlight: true, 
            image,
            images,
            price: 0, 
            startDate, 
            endDate, 
            createdBy: req.session?.user?.fullName || "System",
        };

        const tour = await Tour.create(tourData);

        // Create tour details (price blocks)
        let tourDetailsCreated = false;
        if (tourDetails && Array.isArray(tourDetails)) {
            try {
                const tourDetailPromises = tourDetails.map((detail) => {
                    return TourDetail.create({
                        tourId: tour._id,
                        adultPrice: parseFloat(detail.adultPrice) || 0,
                        childrenPrice: parseFloat(detail.childrenPrice) || 0,
                        childPrice: parseFloat(detail.childPrice) || 0,
                        babyPrice: parseFloat(detail.babyPrice) || 0,
                        singleRoomSupplementPrice:
                            parseFloat(detail.singleRoomSupplementPrice) || 0,
                        discount: parseFloat(detail.discount) || 0,
                        stock: parseInt(detail.stock) || 0,
                        dayStart: new Date(detail.dayStart),
                        dayReturn: new Date(detail.dayReturn),
                    });
                });
                await Promise.all(tourDetailPromises);
                tourDetailsCreated = true;

                // Tính toán lại tổng giá sau khi tạo tour details
                try {
                    await recalculateAndUpdateTourPrice(tour._id);
                } catch (priceError) {
                    console.error(
                        "Warning: Failed to recalculate tour price:",
                        priceError.message
                    );
                }
            } catch (detailError) {
                console.error(
                    "Warning: Failed to create tour details:",
                    detailError.message
                );
            }
        }

        // Check if request expects JSON (AJAX) or HTML (form submission)
        if (
            req.headers.accept &&
            req.headers.accept.includes("application/json")
        ) {
            // Return JSON response for AJAX requests
            return res.status(201).json({
                success: true,
                message: "Thêm tour thành công!",
                tour: {
                    id: tour._id,
                    title: tour.title,
                    code: tour.code,
                    status: tour.status,
                    highlight: tour.highlight,
                    images: tour.images,
                    createdAt: tour.createdAt,
                    hasDetails: tourDetailsCreated,
                },
            });
        } else {
            // Return redirect response for form submissions
            req.flash("success", "Thêm tour mới thành công!");
            return res.redirect("/tour");
        }
    } catch (error) {
        console.error("Error creating tour:", error.message);
        console.error("Full error stack:", error.stack);

        // Check if request expects JSON or HTML
        if (
            req.headers.accept &&
            req.headers.accept.includes("application/json")
        ) {
            // Return JSON response for AJAX requests
            if (error.name === "ValidationError") {
                const errors = Object.values(error.errors).map(
                    (err) => err.message
                );
                return res.status(400).json({
                    success: false,
                    message: `Lỗi xác thực: ${errors.join(", ")}`,
                });
            } else if (error.code === 11000) {
                return res.status(400).json({
                    success: false,
                    message: "Mã tour đã tồn tại, vui lòng chọn mã khác!",
                });
            } else {
                return res.status(500).json({
                    success: false,
                    message: "Có lỗi xảy ra khi thêm tour: " + error.message,
                });
            }
        } else {
            // Return redirect response for form submissions
            if (error.name === "ValidationError") {
                const errors = Object.values(error.errors).map(
                    (err) => err.message
                );
                req.flash("error", `Lỗi xác thực: ${errors.join(", ")}`);
            } else if (error.code === 11000) {
                req.flash(
                    "error",
                    "Mã tour đã tồn tại, vui lòng chọn mã khác!"
                );
            } else {
                req.flash(
                    "error",
                    "Có lỗi xảy ra khi thêm tour: " + error.message
                );
            }
            return res.redirect("/tour/add");
        }
    }
};

// Hiển thị form sửa
exports.showEditForm = async (req, res) => {
    try {
        const tour = await Tour.findById(req.params.id);
        const tourDetails = await TourDetail.find({ tourId: req.params.id });
        const [categories, departures, destinations, transportations] =
            await Promise.all([
                Category.find({ deleted: false }).sort({ name: 1 }),
                Departure.find({ deleted: false }).sort({ name: 1 }),
                Destination.find({ deleted: false }).sort({ name: 1 }),
                Transportation.find({ deleted: false }).sort({ name: 1 }),
            ]);
        res.render("tour/edit", {
            tour,
            tourDetails,
            categories,
            departures,
            destinations,
            transportations,
            userPermissions: res.locals.userPermissions, // Đảm bảo luôn truyền userPermissions
        });
    } catch (error) {
        console.error("Error showing edit form:", error);
        req.flash("error", "Có lỗi xảy ra khi tải form chỉnh sửa!");
        res.redirect("/tour");
    }
};

// Xử lý cập nhật
exports.update = async (req, res) => {
    try {
        const {
            title,
            code,
            category,
            departure,
            destination,
            transportation,
            attractions,
            cuisine,
            suitableTime,
            suitableObject,
            vehicleInfo,
            promotion,
            itinerary,
            tourDetails,
            status,
            highlight,
        } = req.body;

        // Kiểm tra mã tour đã tồn tại (ngoại trừ tour hiện tại)
        if (code) {
            const existingTour = await Tour.findOne({
                code: code,
                deleted: false,
                _id: { $ne: req.params.id }, // Loại trừ tour hiện tại
            });

            if (existingTour) {
                const [
                    tour,
                    tourDetails,
                    categories,
                    departures,
                    destinations,
                    transportations,
                ] = await Promise.all([
                    Tour.findById(req.params.id),
                    TourDetail.find({ tourId: req.params.id }),
                    Category.find({ deleted: false }).sort({ name: 1 }),
                    Departure.find({ deleted: false }).sort({ name: 1 }),
                    Destination.find({ deleted: false }).sort({ name: 1 }),
                    Transportation.find({ deleted: false }).sort({ name: 1 }),
                ]);

                return res.render("tour/edit", {
                    tour,
                    tourDetails,
                    categories,
                    departures,
                    destinations,
                    transportations,
                    error: ["Mã tour đã tồn tại, vui lòng chọn mã khác"],
                    formData: req.body, // Giữ lại dữ liệu form
                    userPermissions: res.locals.userPermissions, // Đảm bảo luôn truyền userPermissions
                });
            }
        }

        // Get current tour to preserve status and highlight
        const currentTour = await Tour.findById(req.params.id);
        if (!currentTour) {
            return res.status(404).json({
                success: false,
                message: "Tour không tồn tại!",
            });
        }

        let updateData = {
            title,
            code,
            category,
            departure,
            destination,
            transportation,
            attractions,
            cuisine,
            suitableTime,
            suitableObject,
            vehicleInfo,
            promotion,
            status: currentTour.status,
            highlight: currentTour.highlight,
            updatedBy: req.session?.user?.fullName || "System",
        };

        // Process itinerary data
        if (itinerary && Array.isArray(itinerary)) {
            updateData.itinerary = itinerary
                .map((item, index) => {
                    // Handle different possible formats of the day field
                    let dayValue;
                    if (
                        item.day !== undefined &&
                        item.day !== null &&
                        item.day !== ""
                    ) {
                        dayValue = parseInt(item.day);
                    } else {
                        dayValue = index + 1; // Default to sequential numbering
                    }

                    // Ensure day is a valid number, default to index + 1 if invalid
                    const validDay =
                        !isNaN(dayValue) && dayValue > 0 ? dayValue : index + 1;

                    return {
                        day: validDay,
                        title: item.title || "",
                        details: item.details || "",
                    };
                })
                .filter((item) => item.title && item.title.trim() !== ""); // Remove empty itinerary items
        }

        // Determine start and end dates from tour details
        let startDate = null;
        let endDate = null;

        if (
            tourDetails &&
            Array.isArray(tourDetails) &&
            tourDetails.length > 0
        ) {
            // Find earliest start date and latest end date
            const validDetails = tourDetails.filter(
                (detail) => detail.dayStart && detail.dayReturn
            );
            if (validDetails.length > 0) {
                const startDates = validDetails.map(
                    (detail) => new Date(detail.dayStart)
                );
                const endDates = validDetails.map(
                    (detail) => new Date(detail.dayReturn)
                );

                startDate = new Date(Math.min(...startDates));
                endDate = new Date(Math.max(...endDates));

                // Update tour dates
                updateData.startDate = startDate;
                updateData.endDate = endDate;
            }
        }

        // Handle multiple images
        if (req.files && req.files.length > 0) {
            const images = req.files.map((file) => {
                // If using Cloudinary, file.path contains the full URL
                // If using local storage, we need to construct the path
                return file.path || "/uploads/" + file.filename;
            });
            updateData.images = images;
            updateData.image = images[0]; // Set first image as main image
        }

        // Update tour
        const updatedTour = await Tour.findByIdAndUpdate(
            req.params.id,
            updateData,
            { new: true }
        );

        if (!updatedTour) {
            return res.status(404).json({
                success: false,
                message: "Tour không tồn tại!",
            });
        }

        // Delete existing tour details and create new ones
        let tourDetailsUpdated = false;
        try {
            await TourDetail.deleteMany({ tourId: req.params.id });

            if (tourDetails && Array.isArray(tourDetails)) {
                const tourDetailPromises = tourDetails.map((detail) => {
                    return TourDetail.create({
                        tourId: req.params.id,
                        adultPrice: parseFloat(detail.adultPrice) || 0,
                        childrenPrice: parseFloat(detail.childrenPrice) || 0,
                        childPrice: parseFloat(detail.childPrice) || 0,
                        babyPrice: parseFloat(detail.babyPrice) || 0,
                        singleRoomSupplementPrice:
                            parseFloat(detail.singleRoomSupplementPrice) || 0,
                        discount: parseFloat(detail.discount) || 0,
                        stock: parseInt(detail.stock) || 0,
                        dayStart: new Date(detail.dayStart),
                        dayReturn: new Date(detail.dayReturn),
                    });
                });
                await Promise.all(tourDetailPromises);
                tourDetailsUpdated = true;

                // Tính toán lại tổng giá sau khi cập nhật tour details
                try {
                    await recalculateAndUpdateTourPrice(req.params.id);
                } catch (priceError) {
                    console.error(
                        "Warning: Failed to recalculate tour price:",
                        priceError.message
                    );
                    // Continue anyway - tour was updated successfully
                }
            }
        } catch (detailError) {
            console.error(
                "Warning: Failed to update tour details:",
                detailError.message
            );
            // Continue anyway - tour was updated successfully
        }

        // Check if request expects JSON (AJAX) or HTML (form submission)
        if (
            req.headers.accept &&
            req.headers.accept.includes("application/json")
        ) {
            // Return JSON response for AJAX requests
            return res.status(200).json({
                success: true,
                message: "Cập nhật tour thành công!",
                tour: {
                    id: updatedTour._id,
                    title: updatedTour.title,
                    code: updatedTour.code,
                    status: updatedTour.status,
                    highlight: updatedTour.highlight,
                    images: updatedTour.images,
                    updatedAt: updatedTour.updatedAt,
                    hasDetails: tourDetailsUpdated,
                },
            });
        } else {
            // Return redirect response for form submissions
            req.flash("success", "Cập nhật tour thành công!");
            return res.redirect("/tour");
        }
    } catch (error) {
        console.error("Error updating tour:", error.message);
        console.error("Full error stack:", error.stack);

        // Check if request expects JSON or HTML
        if (
            req.headers.accept &&
            req.headers.accept.includes("application/json")
        ) {
            // Return JSON response for AJAX requests
            if (error.name === "ValidationError") {
                const errors = Object.values(error.errors).map(
                    (err) => err.message
                );
                return res.status(400).json({
                    success: false,
                    message: `Lỗi xác thực: ${errors.join(", ")}`,
                });
            } else if (error.code === 11000) {
                return res.status(400).json({
                    success: false,
                    message: "Mã tour đã tồn tại, vui lòng chọn mã khác!",
                });
            } else {
                return res.status(500).json({
                    success: false,
                    message:
                        "Có lỗi xảy ra khi cập nhật tour: " + error.message,
                });
            }
        } else {
            // Return redirect response for form submissions
            if (error.name === "ValidationError") {
                const errors = Object.values(error.errors).map(
                    (err) => err.message
                );
                req.flash("error", `Lỗi xác thực: ${errors.join(", ")}`);
            } else if (error.code === 11000) {
                req.flash(
                    "error",
                    "Mã tour đã tồn tại, vui lòng chọn mã khác!"
                );
            } else {
                req.flash(
                    "error",
                    "Có lỗi xảy ra khi cập nhật tour: " + error.message
                );
            }
            return res.redirect(`/tour/edit/${req.params.id}`);
        }
    }
};

// Xử lý xóa (soft delete)
exports.delete = async (req, res) => {
    try {
        const deletedTour = await Tour.findByIdAndUpdate(
            req.params.id,
            { deleted: true, deletedBy: req.session?.user?._id },
            { new: true }
        );

        if (!deletedTour) {
            if (
                req.headers.accept &&
                req.headers.accept.includes("application/json")
            ) {
                return res.status(404).json({
                    success: false,
                    message: "Tour không tồn tại!",
                });
            } else {
                req.flash("error", "Tour không tồn tại!");
                return res.redirect("/tour");
            }
        }

        // Check if request expects JSON (AJAX) or HTML (form submission)
        if (
            req.headers.accept &&
            req.headers.accept.includes("application/json")
        ) {
            // Return JSON response for AJAX requests
            return res.status(200).json({
                success: true,
                message: "Xóa tour thành công!",
                tour: {
                    id: deletedTour._id,
                    title: deletedTour.title,
                    deleted: deletedTour.deleted,
                },
            });
        } else {
            // Return redirect response for form submissions
            req.flash("success", "Xóa tour thành công!");
            return res.redirect("/tour");
        }
    } catch (error) {
        console.error("Error deleting tour:", error.message);

        // Check if request expects JSON or HTML
        if (
            req.headers.accept &&
            req.headers.accept.includes("application/json")
        ) {
            return res.status(500).json({
                success: false,
                message: "Có lỗi xảy ra khi xóa tour!",
            });
        } else {
            req.flash("error", "Có lỗi xảy ra khi xóa tour!");
            return res.redirect("/tour");
        }
    }
};

// Toggle status
exports.toggleStatus = async (req, res) => {
    try {
        const tour = await Tour.findById(req.params.id);
        if (!tour) {
            return res.status(404).json({
                success: false,
                message: "Tour không tồn tại",
            });
        }

        tour.status = !tour.status;
        await tour.save();

        return res.status(200).json({
            success: true,
            message: `Tour đã được ${tour.status ? "kích hoạt" : "tạm dừng"}`,
            tour: {
                id: tour._id,
                title: tour.title,
                status: tour.status,
            },
        });
    } catch (error) {
        console.error("Error toggling tour status:", error.message);
        return res.status(500).json({
            success: false,
            message: "Có lỗi xảy ra khi cập nhật trạng thái",
        });
    }
};

// Toggle highlight
exports.toggleHighlight = async (req, res) => {
    try {
        const tour = await Tour.findById(req.params.id);
        if (!tour) {
            return res.status(404).json({
                success: false,
                message: "Tour không tồn tại",
            });
        }

        tour.highlight = !tour.highlight;
        await tour.save();

        return res.status(200).json({
            success: true,
            message: `Tour đã được ${
                tour.highlight ? "đánh dấu nổi bật" : "bỏ đánh dấu nổi bật"
            }`,
            tour: {
                id: tour._id,
                title: tour.title,
                highlight: tour.highlight,
            },
        });
    } catch (error) {
        console.error("Error toggling tour highlight:", error.message);
        return res.status(500).json({
            success: false,
            message: "Có lỗi xảy ra khi cập nhật nổi bật",
        });
    }
};

// Handle multiple tour deletion
exports.deleteMultiple = async (req, res) => {
    try {
        const { ids } = req.body;

        if (!ids || !Array.isArray(ids) || ids.length === 0) {
            return res.status(400).json({
                success: false,
                message: "Vui lòng chọn ít nhất một tour để xóa",
            });
        }

        // Update multiple tours to mark as deleted
        const result = await Tour.updateMany(
            { _id: { $in: ids } },
            {
                deleted: true,
                deletedBy: req.session?.user?._id,
                deletedAt: new Date(),
            }
        );

        if (result.modifiedCount > 0) {
            return res.status(200).json({
                success: true,
                message: `Đã xóa ${result.modifiedCount} tour thành công`,
                deletedCount: result.modifiedCount,
            });
        } else {
            return res.status(400).json({
                success: false,
                message: "Không có tour nào được xóa",
            });
        }
    } catch (error) {
        console.error("Error deleting multiple tours:", error.message);
        return res.status(500).json({
            success: false,
            message: "Có lỗi xảy ra khi xóa tour",
        });
    }
};

// Hàm tạo mã tour tự động
function generateTourCode(title) {
    // Tạo mã từ tên tour
    const prefix = title
        .toLowerCase()
        .trim()
        .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, "a")
        .replace(/[èéẹẻẽêềếệểễ]/g, "e")
        .replace(/[ìíịỉĩ]/g, "i")
        .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, "o")
        .replace(/[ùúụủũưừứựửữ]/g, "u")
        .replace(/[ỳýỵỷỹ]/g, "y")
        .replace(/đ/g, "d")
        .replace(/[^a-z0-9\s]/g, "")
        .replace(/\s+/g, "-")
        .replace(/-+/g, "-")
        .replace(/^-|-$/g, "")
        .split("-")
        .map((word) => word.charAt(0))
        .join("")
        .toUpperCase()
        .substring(0, 3);

    // Thêm số ngẫu nhiên và timestamp
    const timestamp = Date.now().toString().slice(-4);
    const randomNum = Math.floor(Math.random() * 99)
        .toString()
        .padStart(2, "0");

    return `${prefix}-${timestamp}${randomNum}`;
}

// API tạo mã tour tự động
exports.generateCode = async (req, res) => {
    try {
        const { title } = req.body;

        if (!title) {
            return res.json({
                success: false,
                message: "Vui lòng nhập tên tour",
            });
        }

        let code = generateTourCode(title);
        let counter = 1;

        // Kiểm tra mã đã tồn tại và tạo mã mới nếu cần
        while (true) {
            const existingTour = await Tour.findOne({
                code: code,
                deleted: false,
            });

            if (!existingTour) {
                break;
            }

            // Tạo mã mới với số thứ tự
            const baseCode = code.split("-")[0];
            const newSuffix = (Date.now() + counter).toString().slice(-6);
            code = `${baseCode}-${newSuffix}`;
            counter++;
        }

        return res.status(200).json({
            success: true,
            message: "Tạo mã tour thành công",
            code: code,
        });
    } catch (error) {
        console.error("Error generating tour code:", error.message);
        return res.status(500).json({
            success: false,
            message: "Có lỗi xảy ra khi tạo mã tour",
        });
    }
};

// API kiểm tra mã tour
exports.checkCode = async (req, res) => {
    try {
        const { code } = req.body;

        if (!code) {
            return res.json({
                success: false,
                message: "Vui lòng nhập mã tour",
            });
        }

        const existingTour = await Tour.findOne({
            code: code,
            deleted: false,
        });

        if (existingTour) {
            return res.status(400).json({
                success: false,
                exists: true,
                message: "Mã tour đã tồn tại, vui lòng chọn mã khác",
            });
        }

        return res.status(200).json({
            success: true,
            exists: false,
            message: "Mã tour có thể sử dụng",
        });
    } catch (error) {
        console.error("Error checking tour code:", error.message);
        return res.status(500).json({
            success: false,
            message: "Có lỗi xảy ra khi kiểm tra mã tour",
        });
    }
};

// Hiển thị chi tiết tour
exports.detail = async (req, res) => {
    try {
        const tour = await Tour.findById(req.params.id);
        if (!tour) {
            req.flash("error", "Tour không tồn tại!");
            return res.redirect("/tour");
        }

        // Lấy thông tin liên quan song song để tối ưu hiệu suất
        const [tourDetail, departure, destination, transportation, category] = await Promise.all([
            TourDetail.find({ tourId: tour._id }).sort({ dayStart: 1 }),
            Departure.findById(tour.departure),
            Destination.findById(tour.destination),
            Transportation.findById(tour.transportation),
            Category.findById(tour.category)
        ]);

        res.render("tour/detail", {
            tour,
            tourDetail: tourDetail || [],
            departure,
            destination,
            transportation,
            category,
            userPermissions: res.locals.userPermissions,
        });
    } catch (error) {
        console.error("Error loading tour detail:", error);
        req.flash("error", "Có lỗi xảy ra khi tải chi tiết tour!");
        res.redirect("/tour");
    }
};

//-- API Methods --

// Lấy danh sách tour API
exports.getAllTours = async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const skip = (page - 1) * limit;
        const filter = { deleted: false };
        let sort = { createdAt: 1 }; // Sort from old to new (new records at bottom)

        // Basic filters
        if (req.query.status) filter.status = req.query.status === "true";
        if (req.query.highlight) filter.highlight = req.query.highlight === "true";
        if (req.query.category) filter.category = req.query.category;
        if (req.query.departure) filter.departure = req.query.departure;
        if (req.query.destination) filter.destination = req.query.destination;
        if (req.query.transportation) filter.transportation = req.query.transportation;
        if (req.query.q) filter.title = { $regex: req.query.q, $options: "i" };

        // Date filters
        if (req.query.startDate) {
            filter.startDate = { $gte: new Date(req.query.startDate) };
        }
        if (req.query.endDate) {
            filter.endDate = { $lte: new Date(req.query.endDate) };
        }

        // Advanced date filters for quick filter buttons
        const today = new Date();
        const thirtyDaysFromNow = new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000);

        // Handle quick filter scenarios
        if (req.query.quickFilter) {
            switch (req.query.quickFilter) {
                case "expiring":
                    // Tours expiring within 30 days
                    filter.endDate = {
                        $gte: today,
                        $lte: thirtyDaysFromNow,
                    };
                    filter.status = true;
                    break;
                case "expired":
                    // Tours that have already ended
                    filter.endDate = { $lt: today };
                    break;
                case "featured":
                    // Featured tours
                    filter.highlight = true;
                    break;
                case "active":
                    // Active tours
                    filter.status = true;
                    break;
            }
        }

        // Price sorting
        if (req.query.sortPrice) {
            sort = { totalPrice: req.query.sortPrice === "asc" ? 1 : -1 };
        }

        // Price range filter
        if (req.query.minPrice || req.query.maxPrice) {
            filter.totalPrice = {};
            if (req.query.minPrice) filter.totalPrice.$gte = parseInt(req.query.minPrice);
            if (req.query.maxPrice) filter.totalPrice.$lte = parseInt(req.query.maxPrice);
        }

        const [tours, total] = await Promise.all([
            Tour.find(filter)
                .populate("category departure destination transportation createdBy updatedBy")
                .sort(sort)
                .skip(skip)
                .limit(limit),
            Tour.countDocuments(filter),
        ]);

        // Load tour details for each tour and calculate pricing info
        const toursWithPricing = await Promise.all(
            tours.map(async (tour) => {
                const tourDetails = await TourDetail.find({ tourId: tour._id }).lean();
                const minPrice = calculateMinPrice(tourDetails);
                const maxPrice = calculateMaxPrice(tourDetails);

                return {
                    ...tour.toObject(),
                    tourDetails,
                    minPrice,
                    maxPrice,
                    hasMultiplePrices: tourDetails.length > 1,
                    priceRange: minPrice !== maxPrice
                        ? `${minPrice.toLocaleString()} - ${maxPrice.toLocaleString()}`
                        : minPrice.toLocaleString(),
                };
            })
        );

        const totalPages = Math.ceil(total / limit);

        res.status(200).json({
            success: true,
            data: toursWithPricing,
            pagination: {
                current: page,
                total: totalPages,
                limit,
                totalItems: total,
            },
        });
    } catch (error) {
        console.error("Error in getAllTours API:", error);
        res.status(500).json({
            success: false,
            message: "Lỗi khi tải danh sách tour",
        });
    }
};

// Lấy thông tin chi tiết tour theo ID API
exports.getTourById = async (req, res) => {
    try {
        const tour = await Tour.findById(req.params.id)
            .populate("category departure destination transportation createdBy updatedBy");
        
        if (!tour) {
            return res.status(404).json({
                success: false,
                message: "Không tìm thấy tour",
            });
        }

        // Get tour details
        const tourDetails = await TourDetail.find({ tourId: tour._id }).sort({ dayStart: 1 });

        // Calculate pricing info
        const minPrice = calculateMinPrice(tourDetails);
        const maxPrice = calculateMaxPrice(tourDetails);

        const tourData = {
            ...tour.toObject(),
            tourDetails,
            minPrice,
            maxPrice,
            hasMultiplePrices: tourDetails.length > 1,
            priceRange: minPrice !== maxPrice
                ? `${minPrice.toLocaleString()} - ${maxPrice.toLocaleString()}`
                : minPrice.toLocaleString(),
        };

        res.status(200).json({
            success: true,
            data: tourData,
        });
    } catch (error) {
        console.error("Error in getTourById API:", error);
        res.status(500).json({
            success: false,
            message: "Lỗi khi tải thông tin tour",
        });
    }
};

// Tạo tour mới qua API
exports.apiCreate = async (req, res) => {
    try {
        const {
            title,
            code,
            category,
            departure,
            destination,
            transportation,
            attractions,
            cuisine,
            suitableTime,
            suitableObject,
            vehicleInfo,
            promotion,
            itinerary,
            tourDetails,
        } = req.body;

        // Kiểm tra mã tour đã tồn tại
        if (code) {
            const existingTour = await Tour.findOne({
                code: code,
                deleted: false,
            });

            if (existingTour) {
                return res.status(400).json({
                    success: false,
                    message: "Mã tour đã tồn tại, vui lòng chọn mã khác",
                });
            }
        }

        let image = "";
        let images = [];

        if (req.files && req.files.length > 0) {
            images = req.files.map((file) => file.path);
            image = images[0];
        }

        // Process itinerary data
        let processedItinerary = [];
        if (itinerary && Array.isArray(itinerary)) {
            processedItinerary = itinerary
                .map((item, index) => {
                    // Handle different possible formats of the day field
                    let dayValue;
                    if (item.day !== undefined && item.day !== null && item.day !== "") {
                        dayValue = parseInt(item.day);
                    } else {
                        dayValue = index + 1; // Default to sequential numbering
                    }

                    // Ensure day is a valid number, default to index + 1 if invalid
                    const validDay = !isNaN(dayValue) && dayValue > 0 ? dayValue : index + 1;

                    return {
                        day: validDay,
                        title: item.title || "",
                        details: item.details || "",
                    };
                })
                .filter((item) => item.title && item.title.trim() !== ""); // Remove empty itinerary items
        }

        // Determine start and end dates from tour details
        let startDate = null;
        let endDate = null;

        if (tourDetails && Array.isArray(tourDetails) && tourDetails.length > 0) {
            // Find earliest start date and latest end date
            const validDetails = tourDetails.filter(
                (detail) => detail.dayStart && detail.dayReturn
            );
            if (validDetails.length > 0) {
                const startDates = validDetails.map(
                    (detail) => new Date(detail.dayStart)
                );
                const endDates = validDetails.map(
                    (detail) => new Date(detail.dayReturn)
                );

                startDate = new Date(Math.min(...startDates));
                endDate = new Date(Math.max(...endDates));
            }
        }

        // Create tour
        const tourData = {
            title,
            code,
            category,
            departure,
            destination,
            transportation,
            attractions,
            cuisine,
            suitableTime,
            suitableObject,
            vehicleInfo,
            promotion,
            itinerary: processedItinerary,
            status: true,
            highlight: true,
            image,
            images,
            price: 0,
            startDate,
            endDate,
            createdBy: req.session?.user?.fullName || "System",
        };

        const tour = await Tour.create(tourData);

        // Create tour details (price blocks)
        let tourDetailsCreated = false;
        if (tourDetails && Array.isArray(tourDetails)) {
            try {
                const tourDetailPromises = tourDetails.map((detail) => {
                    return TourDetail.create({
                        tourId: tour._id,
                        adultPrice: parseFloat(detail.adultPrice) || 0,
                        childrenPrice: parseFloat(detail.childrenPrice) || 0,
                        childPrice: parseFloat(detail.childPrice) || 0,
                        babyPrice: parseFloat(detail.babyPrice) || 0,
                        singleRoomSupplementPrice: parseFloat(detail.singleRoomSupplementPrice) || 0,
                        discount: parseFloat(detail.discount) || 0,
                        stock: parseInt(detail.stock) || 0,
                        dayStart: new Date(detail.dayStart),
                        dayReturn: new Date(detail.dayReturn),
                    });
                });
                await Promise.all(tourDetailPromises);
                tourDetailsCreated = true;

                // Tính toán lại tổng giá sau khi tạo tour details
                try {
                    await recalculateAndUpdateTourPrice(tour._id);
                } catch (priceError) {
                    console.error("Warning: Failed to recalculate tour price:", priceError.message);
                }
            } catch (detailError) {
                console.error("Warning: Failed to create tour details:", detailError.message);
            }
        }

        return res.status(201).json({
            success: true,
            message: "Thêm tour thành công!",
            tour: {
                id: tour._id,
                title: tour.title,
                code: tour.code,
                status: tour.status,
                highlight: tour.highlight,
                images: tour.images,
                createdAt: tour.createdAt,
                hasDetails: tourDetailsCreated,
            },
        });
    } catch (error) {
        console.error("Error in apiCreate tour:", error);
        
        if (error.name === "ValidationError") {
            const errors = Object.values(error.errors).map((err) => err.message);
            return res.status(400).json({
                success: false,
                message: `Lỗi xác thực: ${errors.join(", ")}`,
            });
        } else if (error.code === 11000) {
            return res.status(400).json({
                success: false,
                message: "Mã tour đã tồn tại, vui lòng chọn mã khác!",
            });
        } else {
            return res.status(500).json({
                success: false,
                message: "Có lỗi xảy ra khi thêm tour: " + error.message,
            });
        }
    }
};

// Cập nhật tour qua API
exports.apiUpdate = async (req, res) => {
    try {
        const {
            title,
            code,
            category,
            departure,
            destination,
            transportation,
            attractions,
            cuisine,
            suitableTime,
            suitableObject,
            vehicleInfo,
            promotion,
            itinerary,
            tourDetails,
            status,
            highlight,
        } = req.body;

        // Kiểm tra mã tour đã tồn tại (ngoại trừ tour hiện tại)
        if (code) {
            const existingTour = await Tour.findOne({
                code: code,
                deleted: false,
                _id: { $ne: req.params.id }, // Loại trừ tour hiện tại
            });

            if (existingTour) {
                return res.status(400).json({
                    success: false,
                    message: "Mã tour đã tồn tại, vui lòng chọn mã khác",
                });
            }
        }

        // Get current tour
        const currentTour = await Tour.findById(req.params.id);
        if (!currentTour) {
            return res.status(404).json({
                success: false,
                message: "Tour không tồn tại!",
            });
        }

        let updateData = {
            title,
            code,
            category,
            departure,
            destination,
            transportation,
            attractions,
            cuisine,
            suitableTime,
            suitableObject,
            vehicleInfo,
            promotion,
            status: status !== undefined ? status : currentTour.status,
            highlight: highlight !== undefined ? highlight : currentTour.highlight,
            updatedBy: req.session?.user?.fullName || "System",
        };

        // Process itinerary data
        if (itinerary && Array.isArray(itinerary)) {
            updateData.itinerary = itinerary
                .map((item, index) => {
                    // Handle different possible formats of the day field
                    let dayValue;
                    if (item.day !== undefined && item.day !== null && item.day !== "") {
                        dayValue = parseInt(item.day);
                    } else {
                        dayValue = index + 1; // Default to sequential numbering
                    }

                    // Ensure day is a valid number, default to index + 1 if invalid
                    const validDay = !isNaN(dayValue) && dayValue > 0 ? dayValue : index + 1;

                    return {
                        day: validDay,
                        title: item.title || "",
                        details: item.details || "",
                    };
                })
                .filter((item) => item.title && item.title.trim() !== ""); // Remove empty itinerary items
        }

        // Determine start and end dates from tour details
        if (tourDetails && Array.isArray(tourDetails) && tourDetails.length > 0) {
            // Find earliest start date and latest end date
            const validDetails = tourDetails.filter(
                (detail) => detail.dayStart && detail.dayReturn
            );
            if (validDetails.length > 0) {
                const startDates = validDetails.map(
                    (detail) => new Date(detail.dayStart)
                );
                const endDates = validDetails.map(
                    (detail) => new Date(detail.dayReturn)
                );

                updateData.startDate = new Date(Math.min(...startDates));
                updateData.endDate = new Date(Math.max(...endDates));
            }
        }

        // Handle multiple images
        if (req.files && req.files.length > 0) {
            updateData.images = req.files.map((file) => file.path);
            updateData.image = updateData.images[0]; // Set first image as main image
        }

        // Update tour
        const updatedTour = await Tour.findByIdAndUpdate(
            req.params.id,
            updateData,
            { new: true }
        );

        // Delete existing tour details and create new ones
        let tourDetailsUpdated = false;
        try {
            await TourDetail.deleteMany({ tourId: req.params.id });

            if (tourDetails && Array.isArray(tourDetails)) {
                const tourDetailPromises = tourDetails.map((detail) => {
                    return TourDetail.create({
                        tourId: req.params.id,
                        adultPrice: parseFloat(detail.adultPrice) || 0,
                        childrenPrice: parseFloat(detail.childrenPrice) || 0,
                        childPrice: parseFloat(detail.childPrice) || 0,
                        babyPrice: parseFloat(detail.babyPrice) || 0,
                        singleRoomSupplementPrice: parseFloat(detail.singleRoomSupplementPrice) || 0,
                        discount: parseFloat(detail.discount) || 0,
                        stock: parseInt(detail.stock) || 0,
                        dayStart: new Date(detail.dayStart),
                        dayReturn: new Date(detail.dayReturn),
                    });
                });
                await Promise.all(tourDetailPromises);
                tourDetailsUpdated = true;

                // Tính toán lại tổng giá sau khi cập nhật tour details
                try {
                    await recalculateAndUpdateTourPrice(req.params.id);
                } catch (priceError) {
                    console.error("Warning: Failed to recalculate tour price:", priceError.message);
                }
            }
        } catch (detailError) {
            console.error("Warning: Failed to update tour details:", detailError.message);
        }

        return res.status(200).json({
            success: true,
            message: "Cập nhật tour thành công!",
            tour: {
                id: updatedTour._id,
                title: updatedTour.title,
                code: updatedTour.code,
                status: updatedTour.status,
                highlight: updatedTour.highlight,
                images: updatedTour.images,
                updatedAt: updatedTour.updatedAt,
                hasDetails: tourDetailsUpdated,
            },
        });
    } catch (error) {
        console.error("Error in apiUpdate tour:", error);
        
        if (error.name === "ValidationError") {
            const errors = Object.values(error.errors).map((err) => err.message);
            return res.status(400).json({
                success: false,
                message: `Lỗi xác thực: ${errors.join(", ")}`,
            });
        } else if (error.code === 11000) {
            return res.status(400).json({
                success: false,
                message: "Mã tour đã tồn tại, vui lòng chọn mã khác!",
            });
        } else {
            return res.status(500).json({
                success: false,
                message: "Có lỗi xảy ra khi cập nhật tour: " + error.message,
            });
        }
    }
};

// Xóa tour qua API
exports.apiDelete = async (req, res) => {
    try {
        const deletedTour = await Tour.findByIdAndUpdate(
            req.params.id,
            { deleted: true, deletedBy: req.session?.user?._id },
            { new: true }
        );

        if (!deletedTour) {
            return res.status(404).json({
                success: false,
                message: "Tour không tồn tại!",
            });
        }

        return res.status(200).json({
            success: true,
            message: "Xóa tour thành công!",
            tour: {
                id: deletedTour._id,
                title: deletedTour.title,
                deleted: deletedTour.deleted,
            },
        });
    } catch (error) {
        console.error("Error in apiDelete tour:", error);
        return res.status(500).json({
            success: false,
            message: "Có lỗi xảy ra khi xóa tour!",
        });
    }
};
