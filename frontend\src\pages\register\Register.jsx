import { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import ReCAPTC<PERSON> from 'react-google-recaptcha'
import { useAuth } from '../../hooks/useAuth'
import { EyeIcon, EyeSlashIcon, UserIcon, EnvelopeIcon, PhoneIcon, CheckCircleIcon } from '@heroicons/react/24/outline'
import Loading from '../../components/common/Loading/Loading'
import logoImg from '../../assets/images/vtv-logo.png'


const Register = () => {
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: ''
  })
  const [errors, setErrors] = useState({})
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [recaptchaValue, setRecaptchaValue] = useState(null)
  const [acceptTerms, setAcceptTerms] = useState(false)
  const [hasSubmitted, setHasSubmitted] = useState(false)
  
  // Email verification states
  const [emailVerificationSent, setEmailVerificationSent] = useState(false)
  const [emailVerificationCode, setEmailVerificationCode] = useState('')
  const [showVerificationInput, setShowVerificationInput] = useState(false)
  const [isEmailVerified, setIsEmailVerified] = useState(false)
  
  const { register } = useAuth()
  const navigate = useNavigate()

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target
    
    if (type === 'checkbox') {
      setAcceptTerms(checked)
      // Clear terms error when user checks the box (only if form has been submitted)
      if (hasSubmitted && errors.terms && checked) {
        setErrors(prev => ({ ...prev, terms: '' }))
      }
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }))
      
      // Clear errors when user starts typing (only if form has been submitted)
      if (hasSubmitted && errors[name]) {
        setErrors(prev => ({ ...prev, [name]: '' }))
      }
      
      // Reset email verification if email changes
      if (name === 'email') {
        setIsEmailVerified(false)
        setShowVerificationInput(false)
        setEmailVerificationSent(false)
        setEmailVerificationCode('')

        // Real-time email validation
        if (value && !validateEmail(value)) {
          setErrors(prev => ({ ...prev, email: 'Email không đúng định dạng' }))
        }
      }
    }
  }

  // Helper function to validate email
  const validateEmail = (email) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
  }

  // Function to check password strength
  const getPasswordStrength = (password) => {
    if (!password || password.length < 6) {
      return {
        level: 'weak',
        text: 'Mật khẩu yếu',
        emoji: '🔴',
        barColor: 'bg-red-500',
        width: 'w-1/3',
        suggestions: 'chữ hoa, số, ký tự đặc biệt'
      }
    }

    const hasLowerCase = /[a-z]/.test(password)
    const hasUpperCase = /[A-Z]/.test(password)
    const hasNumbers = /\d/.test(password)
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password)

    // Mạnh: ≥ 8 ký tự, có chữ hoa + số + ký tự đặc biệt
    if (password.length >= 8 && hasUpperCase && hasNumbers && hasSpecialChar) {
      return {
        level: 'strong',
        text: 'Mật khẩu mạnh',
        emoji: '🟢',
        barColor: 'bg-green-500',
        width: 'w-full',
        suggestions: ''
      }
    }

    // Trung bình: ≥ 6 ký tự, có cả chữ thường + số
    if (password.length >= 6 && hasLowerCase && hasNumbers) {
      const missing = []
      if (password.length < 8) missing.push('thêm ký tự')
      if (!hasUpperCase) missing.push('chữ hoa')
      if (!hasSpecialChar) missing.push('ký tự đặc biệt')

      return {
        level: 'medium',
        text: 'Mật khẩu trung bình',
        emoji: '🟡',
        barColor: 'bg-orange-500',
        width: 'w-2/3',
        suggestions: missing.join(', ')
      }
    }

    // Yếu: chỉ số hoặc chỉ chữ thường
    return {
      level: 'weak',
      text: 'Mật khẩu yếu',
      emoji: '🔴',
      barColor: 'bg-red-500',
      width: 'w-1/3',
      suggestions: 'chữ hoa, số, ký tự đặc biệt'
    }
  }

  // Send email verification code
  const sendEmailVerification = async () => {
    if (!formData.email || !validateEmail(formData.email)) {
      setErrors({ email: 'Vui lòng nhập email hợp lệ trước' })
      return
    }

    try {
      setIsLoading(true)
      const response = await fetch(`${import.meta.env.VITE_API_URL || 'http://localhost:3000'}/api/email/otp/send`, {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({ email: formData.email.trim().toLowerCase() })
      })

      if (response.ok) {
        setEmailVerificationSent(true)
        setShowVerificationInput(true)
        setErrors(prev => ({ ...prev, submit: '' }))
        // Show success message
        setErrors({ submit: '✅ Mã xác thực đã được gửi đến email của bạn!' })
      } else {
        const errorData = await response.json()
        setErrors({ submit: errorData.message || 'Không thể gửi mã xác thực' })
      }
    } catch (error) {
      setErrors({ submit: 'Lỗi kết nối. Vui lòng thử lại!' })
    } finally {
      setIsLoading(false)
    }
  }

  // Verify email code
  const verifyEmailCode = async () => {
    if (!emailVerificationCode || emailVerificationCode.length !== 6) {
      setErrors({ submit: 'Vui lòng nhập mã xác thực 6 chữ số' })
      return
    }

    try {
      setIsLoading(true)
      const response = await fetch(`${import.meta.env.VITE_API_URL || 'http://localhost:3000'}/api/email/otp/verify`, {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({
          email: formData.email.trim().toLowerCase(),
          code: emailVerificationCode
        })
      })

      const result = await response.json()
      
      if (response.ok && result.success) {
        setIsEmailVerified(true)
        setShowVerificationInput(false)
        setErrors({ submit: '✅ Email đã được xác thực thành công!' })
      } else {
        setErrors({ submit: result.message || 'Mã xác thực không đúng' })
      }
    } catch (error) {
      setErrors({ submit: 'Lỗi xác thực. Vui lòng thử lại!' })
    } finally {
      setIsLoading(false)
    }
  }

  const validateForm = () => {
    const newErrors = {}
    
    // Validate full name
    if (!formData.fullName.trim()) {
      newErrors.fullName = 'Vui lòng nhập họ và tên'
    } else if (formData.fullName.trim().length < 2) {
      newErrors.fullName = 'Họ tên phải có ít nhất 2 ký tự'
    }
    
    // Validate email
    if (!formData.email.trim()) {
      newErrors.email = 'Vui lòng nhập email'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Email không hợp lệ'
    }
    
    // Validate email verification
    if (!isEmailVerified) {
      newErrors.email = 'Vui lòng xác thực email trước khi đăng ký'
    }
    
    // Validate phone
    if (!formData.phone.trim()) {
      newErrors.phone = 'Vui lòng nhập số điện thoại'
    } else if (!/^[0-9]{10,11}$/.test(formData.phone)) {
      newErrors.phone = 'Số điện thoại phải có 10-11 chữ số'
    }
    
    // Validate password
    if (!formData.password) {
      newErrors.password = 'Vui lòng nhập mật khẩu'
    } else {
      const strength = getPasswordStrength(formData.password)
      if (strength.level === 'weak') {
        newErrors.password = 'Mật khẩu quá yếu. Vui lòng tạo mật khẩu mạnh hơn'
      }
    }
    
    // Validate confirm password
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Vui lòng xác nhận mật khẩu'
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Mật khẩu xác nhận không khớp'
    }
    
    // Address is optional - no validation needed

    // Validate terms
    if (!acceptTerms) {
      newErrors.terms = 'Vui lòng đồng ý với điều khoản sử dụng'
    }

    // Validate reCAPTCHA
    if (!recaptchaValue) {
      newErrors.recaptcha = 'Vui lòng xác thực reCAPTCHA'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setHasSubmitted(true)

    // Validate form and show errors
    if (!validateForm()) {
      return
    }

    setIsLoading(true)
    try {
      const registerData = {
        fullName: formData.fullName.trim(),
        email: formData.email.trim().toLowerCase(),
        username: formData.email.trim().toLowerCase(), // Sử dụng email làm username
        phone: formData.phone.trim(),
        password: formData.password,
        confirmPassword: formData.confirmPassword,
        recaptcha: recaptchaValue
      }

      await register(registerData)

      navigate('/login', {
        state: {
          message: 'Đăng ký thành công! Vui lòng đăng nhập.'
        }
      })

    } catch (error) {
      setErrors({
        submit: error.response?.data?.message || error.message || 'Đăng ký thất bại. Vui lòng thử lại.'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSocialLogin = (provider) => {
    window.location.href = `${import.meta.env.VITE_API_URL}/auth/${provider}`
  }

  const onRecaptchaChange = (value) => {
    setRecaptchaValue(value)
    // Clear recaptcha error when user completes it (only if form has been submitted)
    if (hasSubmitted && errors.recaptcha && value) {
      setErrors(prev => ({ ...prev, recaptcha: '' }))
    }
  }

  // Check if form is ready for submission
  const isFormValid = () => {
    return (
      formData.fullName.trim().length >= 2 &&
      formData.email.trim() &&
      validateEmail(formData.email) &&
      isEmailVerified &&
      formData.phone.trim() &&
      /^[0-9]{10,11}$/.test(formData.phone) &&
      formData.password.length >= 6 &&
      formData.confirmPassword === formData.password &&
      acceptTerms &&
      recaptchaValue
    )
  }

  if (isLoading) {
    return <Loading text="Đang xử lý..." />
  }

  return (
    <>
    {/* Logo Vietravel */}
    <div className="text-center">
      <div className="flex items-center justify-center">
        <img className='w-80 h-auto mt-4' src={logoImg} alt="Viet travel" />
      </div>
    </div>
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4 mb-16">
      <div className="bg-white rounded-2xl shadow-xl w-full max-w-2xl p-10 animate-fade-in">
        {/* Title */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-sky-800 mb-2">Đăng ký tài khoản</h1>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {errors.submit && (
            <div className={`border px-4 py-3 rounded-lg ${
              errors.submit.startsWith('✅') 
                ? 'bg-green-50 border-green-200 text-green-700' 
                : 'bg-red-50 border-red-200 text-red-700'
            }`}>
              {errors.submit}
            </div>
          )}

          {/* Full Name and Email Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Full Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Họ và tên <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <input
                  type="text"
                  name="fullName"
                  value={formData.fullName}
                  onChange={handleChange}
                  className={`w-full px-4 py-3 pr-10 bg-gray-50 border-0 border-b-2 border-gray-200 focus:border-blue-500 focus:bg-white focus:outline-none transition-all duration-300 ${
                    errors.fullName ? 'border-red-400 bg-red-50' : ''
                  }`}
                  placeholder="Nhập họ và tên"
                />
                <UserIcon className="absolute right-3 top-3 h-5 w-5 text-gray-400" />
              </div>
              {hasSubmitted && errors.fullName && (
                <p className="text-red-500 text-sm mt-1 font-medium">{errors.fullName}</p>
              )}
              {hasSubmitted && !errors.fullName && formData.fullName && formData.fullName.trim().length >= 2 && (
                <p className="text-green-600 text-sm mt-1 font-medium">✓ Họ tên hợp lệ</p>
              )}
            </div>

            {/* Email */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email <span className="text-red-500">*</span>
                {isEmailVerified && <CheckCircleIcon className="inline h-4 w-4 text-green-500 ml-1" />}
              </label>
              <div className="relative">
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  className={`w-full px-4 py-3 pr-10 bg-gray-50 border-0 border-b-2 border-gray-200 focus:border-blue-500 focus:bg-white focus:outline-none transition-all duration-300 ${
                    errors.email ? 'border-red-400 bg-red-50' : isEmailVerified ? 'border-green-400 bg-green-50' : ''
                  }`}
                  placeholder="Nhập email"
                />
                <EnvelopeIcon className={`absolute right-3 top-3 h-5 w-5 ${isEmailVerified ? 'text-green-500' : 'text-gray-400'}`} />
              </div>
              {hasSubmitted && errors.email && (
                <p className="text-red-500 text-sm mt-1 font-medium">{errors.email}</p>
              )}
              {!errors.email && formData.email && validateEmail(formData.email) && !isEmailVerified && (
                <p className="text-blue-600 text-sm mt-1 font-medium">✓ Email hợp lệ - Cần xác thực</p>
              )}
              {!errors.email && isEmailVerified && (
                <p className="text-green-600 text-sm mt-1 font-medium">✓ Email đã được xác thực</p>
              )}
              
              {/* Email Verification Button */}
              {!isEmailVerified && formData.email && validateEmail(formData.email) && (
                <button
                  type="button"
                  onClick={sendEmailVerification}
                  disabled={isLoading}
                  className="mt-2 text-blue-600 hover:text-blue-800 font-medium underline text-sm disabled:opacity-50"
                >
                  {emailVerificationSent ? 'Gửi lại mã xác thực' : 'Gửi mã xác thực email'}
                </button>
              )}
            </div>
          </div>

          {/* Email Verification Section */}
          {showVerificationInput && (
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Mã xác thực email <span className="text-red-500">*</span>
              </label>
              <div className="flex gap-2">
                <input
                  type="text"
                  value={emailVerificationCode}
                  onChange={(e) => setEmailVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                  className="flex-1 px-4 py-3 bg-white border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:outline-none text-center text-lg font-mono"
                  placeholder="000000"
                  maxLength="6"
                />
                <button
                  type="button"
                  onClick={verifyEmailCode}
                  disabled={isLoading || emailVerificationCode.length !== 6}
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Xác thực
                </button>
              </div>
              <p className="text-xs text-gray-600 mt-2">
                Nhập mã 6 chữ số đã được gửi đến email {formData.email}
              </p>
            </div>
          )}

          {/* Phone */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Số điện thoại <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <input
                type="tel"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                className={`w-full px-4 py-3 pr-10 bg-gray-50 border-0 border-b-2 border-gray-200 focus:border-blue-500 focus:bg-white focus:outline-none transition-all duration-300 ${
                  errors.phone ? 'border-red-400 bg-red-50' : ''
                }`}
                placeholder="Nhập số điện thoại"
              />
              <PhoneIcon className="absolute right-3 top-3 h-5 w-5 text-gray-400" />
            </div>
            {hasSubmitted && errors.phone && (
              <p className="text-red-500 text-sm mt-1 font-medium">{errors.phone}</p>
            )}
            {hasSubmitted && !errors.phone && formData.phone && /^[0-9]{10,11}$/.test(formData.phone) && (
              <p className="text-green-600 text-sm mt-1 font-medium">✓ Số điện thoại hợp lệ</p>
            )}
            {hasSubmitted && !errors.phone && formData.phone && formData.phone.length > 0 && !/^[0-9]{10,11}$/.test(formData.phone) && (
              <p className="text-red-500 text-sm mt-1 font-medium animate-pulse">❌ Số điện thoại phải có 10-11 chữ số</p>
            )}
          </div>

          {/* Password and Confirm Password Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Password */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Mật khẩu <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  className={`w-full px-4 py-3 pr-10 bg-gray-50 border-0 border-b-2 border-gray-200 focus:border-blue-500 focus:bg-white focus:outline-none transition-all duration-300 ${
                    errors.password ? 'border-red-400 bg-red-50' : ''
                  }`}
                  placeholder="Nhập mật khẩu"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-3 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  {showPassword ? (
                    <EyeSlashIcon className="h-5 w-5" />
                  ) : (
                    <EyeIcon className="h-5 w-5" />
                  )}
                </button>
              </div>
              {/* Password strength indicator - Single line layout */}
              {formData.password && (
                <div className="mt-2 mb-2">
                  {(() => {
                    const strength = getPasswordStrength(formData.password)
                    return (
                      <>
                        {/* Progress bar - compact width */}
                        <div className=" bg-gray-200 rounded-full" style={{height: '4px'}}>
                          <div
                            className={`rounded-full transition-all duration-300 ${strength.barColor} ${strength.width}`}
                            style={{height: '4px'}}
                          ></div>
                        </div>
                      <div className="flex items-center gap-1 mt-0.5">
                        {/* Status label - compact */}
                        <span className="text-xs font-medium text-gray-700 whitespace-nowrap" style={{fontSize: '13px'}}>
                          <span style={{ fontSize: '8px' }}>{strength.emoji}</span> {strength.text}
                        </span>

                        {/* Tooltip icon for suggestions */}
                        {strength.suggestions && (
                          <div className="relative group">
                            <span className="text-gray-400 hover:text-gray-600 cursor-help text-sm">❓</span>
                            <div className="absolute bottom-full right-0 mb-2 hidden group-hover:block bg-gray-800 text-white text-xs rounded py-1 px-2 whitespace-nowrap z-10">
                              Gợi ý: {strength.suggestions}
                              <div className="absolute top-full right-2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
                            </div>
                          </div>
                        )}
                      </div>
                      </>
                    )
                  })()}
                </div>
              )}
              {/* Password validation errors (only after submit) */}
              {hasSubmitted && errors.password && (
                <p className="text-red-500 text-sm mt-1 font-medium">{errors.password}</p>
              )}
            </div>

            {/* Confirm Password */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Xác nhận mật khẩu <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <input
                  type={showConfirmPassword ? 'text' : 'password'}
                  name="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  className={`w-full px-4 py-3 pr-10 bg-gray-50 border-0 border-b-2 border-gray-200 focus:border-blue-500 focus:bg-white focus:outline-none transition-all duration-300 ${
                    errors.confirmPassword ? 'border-red-400 bg-red-50' : ''
                  }`}
                  placeholder="Nhập lại mật khẩu"
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-3 top-3 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  {showConfirmPassword ? (
                    <EyeSlashIcon className="h-5 w-5" />
                  ) : (
                    <EyeIcon className="h-5 w-5" />
                  )}
                </button>
              </div>
              {/* Confirm password validation messages */}
              {hasSubmitted && errors.confirmPassword && (
                <p className="text-red-500 text-sm mt-1 font-medium">{errors.confirmPassword}</p>
              )}
              {hasSubmitted && !errors.confirmPassword && formData.confirmPassword && formData.password && formData.confirmPassword === formData.password && formData.password.length >= 6 && (
                <p className="text-green-600 text-sm mt-1 font-medium">✓ Mật khẩu khớp</p>
              )}
              {hasSubmitted && !errors.confirmPassword && formData.confirmPassword && formData.password && formData.confirmPassword !== formData.password && (
                <p className="text-red-500 text-sm mt-1 font-medium animate-pulse">❌ Mật khẩu xác nhận không khớp</p>
              )}
            </div>
          </div>



          {/* Terms and Conditions */}
          <div>
            <div className="flex items-start space-x-3">
              <input 
                type="checkbox" 
                id="terms"
                checked={acceptTerms}
                onChange={handleChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1"
              />
              <label htmlFor="terms" className="text-sm text-gray-600 leading-relaxed">
                Tôi đồng ý với{' '}
                <Link to="/terms" className="text-blue-600 hover:text-blue-800 font-medium underline">
                  Điều khoản
                </Link>{' '}
                và{' '}
                <Link to="/privacy" className="text-blue-600 hover:text-blue-800 font-medium underline">
                  Chính sách bảo mật
                </Link>
              </label>
            </div>
            {hasSubmitted && errors.terms && (
              <p className="text-red-500 text-sm mt-1 ml-7 font-medium">{errors.terms}</p>
            )}
          </div>

          {/* reCAPTCHA */}
          <div className="flex justify-center">
            <ReCAPTCHA
              sitekey={import.meta.env.VITE_RECAPTCHA_SITE_KEY || "6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI"}
              onChange={onRecaptchaChange}
              theme="light"
            />
          </div>
          {hasSubmitted && errors.recaptcha && (
            <p className="text-red-500 text-sm text-center font-medium">{errors.recaptcha}</p>
          )}

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isLoading}
            className="relative left-1/2 -translate-x-1/2 w-full max-w-3xs bg-red-600 text-white font-bold py-3 px-4 rounded-2xl"
          >
            {isLoading ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                ĐANG ĐĂNG KÝ...
              </div>
            ) : (
              'ĐĂNG KÝ'
            )}
          </button>

          {/* Divider */}
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white text-gray-500">Hoặc đăng ký với</span>
            </div>
          </div>

          {/* Social Login Buttons */}
          <div className="space-y-3">
            {/* Facebook Login */}
            <button
              type="button"
              onClick={() => handleSocialLogin('facebook')}
              className="relative left-1/2 -translate-x-1/2 w-full max-w-3xs bg-sky-700 hover:bg-sky-800 text-white font-medium py-3 px-4 rounded-2xl flex items-center justify-center space-x-3"
            >
              <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
              </svg>
              <span>Đăng ký với Facebook</span>
            </button>

            {/* Google Login */}
            <button
              type="button"
              onClick={() => handleSocialLogin('google')}
              className="relative left-1/2 -translate-x-1/2 w-full max-w-3xs bg-white hover:bg-gray-50 text-gray-700 font-medium py-3 px-4 rounded-2xl border border-gray-300 flex items-center justify-center space-x-3"
            >
              <svg className="w-5 h-5" viewBox="0 0 24 24">
                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              <span>Đăng ký với Google</span>
            </button>
          </div>

          {/* Login Link */}
          <div className="text-center pt-4 border-t border-gray-200">
            <p className="text-sm">
              Đã có tài khoản?{' '}
              <Link to="/login" className="text-sky-800 underline font-medium italic">
                Đăng nhập ngay
              </Link>
            </p>
          </div>
        </form>
      </div>
    </div>
    </>
  )
}

export default Register