@use "../abstracts/" as *;

body {
    background: #fff;
    min-height: 100vh;
    overflow: auto;
}

.login {
    &-page {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 90vh;
    }

    &-header {
        text-align: center;
        margin-bottom: 3rem;

        img {
            max-width: 400px;
            height: auto;
        }
    }
}

.login-form {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;

    &__wrapper {
        width: clamp(320px, 90vw, 900px);
        background: #fff;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 20px 90px rgba(0, 0, 0, 0.1);
        display: flex;
        min-height: 500px;
    }

    &__left {
        flex: 1;
        background: $primary-color;
        padding: 3rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: white;
        text-align: center;

        h3 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        p {
            font-size: 1.2rem;
            opacity: 0.9;
            line-height: 1.6;
        }
    }

    &__right {
        flex: 1;
        padding: 3rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    h2 {
        font-weight: 700;
        font-size: 2.5rem;
        color: #333;
        margin-bottom: 1.5rem;
        text-align: center;
    }
}

// Form styling
.form-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;

    i {
        color: #667eea;
        width: 16px;
    }

    .required {
        color: #dc3545;
        margin-left: 3px;
    }
}

.form-control {
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    padding: 12px 16px;
    font-size: 1.2rem;
    transition: all 0.3s ease;

    &:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.15);
    }

    &::placeholder {
        color: #a0a6b1;
        font-size: 1.2rem;
    }
}

.btn-login {
    background: $primary-color;
    border: none;
    border-radius: 10px;
    padding: 14px 20px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    margin-top: 1rem;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }

    &:disabled {
        opacity: 0.7;
        transform: none;
    }
}

// Alert styling
.alert {
    border-radius: 10px;
    border: none;
    margin-bottom: 1.5rem;
    padding: 12px 16px;
    font-size: 0.9rem;

    &.alert-danger {
        background-color: #f8d7da;
        color: #721c24;
        border-left: 4px solid #dc3545;
    }

    &.alert-success {
        background-color: #d1edff;
        color: #0c5460;
        border-left: 4px solid #17a2b8;
    }

    i {
        margin-right: 8px;
    }
}

.password-toggle {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #666;
    transition: color 0.3s ease;

    &:hover {
        color: #667eea;
    }
}

// Responsive design
@media (max-width: 768px) {
    .login-form {
        &__wrapper {
            flex-direction: column;
            min-height: auto;
        }

        &__left {
            padding: 2rem;

            h3 {
                font-size: 1.5rem;
            }

            p {
                font-size: 1rem;
            }
        }

        &__right {
            padding: 2rem;
        }
    }

    .login-page {
        padding: 1rem;
    }
}
