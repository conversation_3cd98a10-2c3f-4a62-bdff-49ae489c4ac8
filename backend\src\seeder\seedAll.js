const mongoose = require("mongoose");
require("dotenv").config();

// Import các seeder
const seedUsers = require("./userSeeder");
const seedRoles = require("./roleSeeder");
const { seedDepartures } = require("./departureSeeder");
const { seedSettings } = require("./settingSeeder");
const seedPermissions = require("./permissionSeeder");
const seedRolePermissions = require("./rolePermissionSeeder");
const createOrders = require("./orderSeeder");

const seedAll = async () => {
    try {
        console.log("🌱 Bắt đầu seed dữ liệu...\n");

        // Seed theo thứ tự phụ thuộc
        console.log("1️⃣ Seeding Permissions...");
        await seedPermissions();
        console.log("");

        // console.log('2️⃣ Seeding Roles...');
        // await seedRoles();
        // console.log('');

        // console.log('3️⃣ Seeding Role Permissions...');
        // await seedRolePermissions();
        // console.log('');

        // console.log('4️⃣ Seeding Users...');
        // await seedUsers();
        // console.log('');

        // console.log('5️⃣ Seeding Departures...');
        // await seedDepartures();
        // console.log('');

        // console.log('6️⃣ Seeding Settings...');
        // await seedSettings();
        // console.log('');

        console.log('7️⃣ Seeding Orders...');
        await createOrders();
        console.log('');

        console.log("🎉 Hoàn thành seed tất cả dữ liệu!");
        console.log("🚀 Hệ thống RBAC đã được khởi tạo thành công!");
    } catch (error) {
        console.error("❌ Lỗi khi seed:", error);
        throw error;
    }
};

// Chạy nếu file được gọi trực tiếp
if (require.main === module) {
    // Load environment variables
    require("dotenv").config();

    // Kết nối database với cùng config như main app
    const options = {
        user: process.env.DB_USER,
        pass: process.env.DB_PASSWORD,
        dbName: process.env.DB_NAME,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
        family: 4,
    };

    mongoose
        .connect(process.env.DB_HOST, options)
        .then(() => {
            console.log("✅ Đã kết nối database");
            return seedAll();
        })
        .then(() => {
            mongoose.disconnect();
            console.log("🔌 Đã ngắt kết nối database");
        })
        .catch((error) => {
            console.error("❌ Lỗi:", error);
            mongoose.disconnect();
        });
}

module.exports = seedAll;
