const User = require('../models/userModel');
const Role = require('../models/roleModel');

const seedUsers = async () => {
    try {
        console.log('🚀 Bắt đầu seed dữ liệu users...');
        
        // Lấy roles để gán cho users
        const roles = await Role.find({});
        const roleMap = {};
        roles.forEach(role => {
            roleMap[role.name] = role._id;
        });
        
        // Xóa tất cả user cũ
        await User.deleteMany({});
        console.log('Đã xóa tất cả user cũ');
        
        // Tạo users mẫu
        const users = [
            {
                fullName: 'Super Administrator',
                email: '<EMAIL>',
                password: '123456',
                role: roleMap['Super Admin'],
                status: 'Hoạt động',
                user_type: 'admin'
            },
            {
                fullName: 'Administrator',
                email: '<EMAIL>',
                password: '123456',
                role: roleMap['Admin'],
                status: '<PERSON><PERSON><PERSON> động',
                user_type: 'admin'
            },
            {
                fullName: 'Manager User',
                email: '<EMAIL>',
                password: '123456',
                role: roleMap['Manager'],
                status: '<PERSON><PERSON><PERSON> động',
                user_type: 'staff'
            },
            {
                fullName: 'Viewer User',
                email: '<EMAIL>',
                password: '123456',
                role: roleMap['Viewer'],
                status: 'Hoạt động',
                user_type: 'staff'
            },
            {
                fullName: 'Customer Example',
                email: '<EMAIL>',
                password: '123456',
                status: 'Hoạt động',
                user_type: 'customer'
            }
        ];
        
        for (const userData of users) {
            const user = new User(userData);
            await user.save();
            
            // Lấy role name cho log
            const roleName = userData.role ? 
                (roles.find(r => r._id.toString() === userData.role.toString())?.name || 'Unknown Role') :
                'No Role (Customer)';
                
            console.log(`Đã tạo user: ${userData.email} - ${roleName} (${userData.user_type})`);
        }
        
        console.log('✅ Seed users thành công!');
        console.log('📧 Thông tin đăng nhập:');
        console.log('🔥 Super Admin: <EMAIL> / 123456');
        console.log('👑 Admin: <EMAIL> / 123456');
        console.log('👤 Manager: <EMAIL> / 123456');
        console.log('👁️ Viewer: <EMAIL> / 123456');
        console.log('🛒 Customer: <EMAIL> / 123456');
        
    } catch (error) {
        console.error('❌ Lỗi khi seed users:', error);
        throw error;
    }
};

module.exports = seedUsers;
