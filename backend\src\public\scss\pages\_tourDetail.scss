@use "../abstracts" as *;

// Tour Detail Management Styles
.tour-detail {
    margin-left: 240px;
    padding: 2rem;
    height: 100%;
    min-width: calc(100% - 260px);
    
    // Buttons
    &__btn {
        padding: 0.75rem 2.5rem;
        border: 1px solid transparent;
        outline: none;
        border-radius: 5px;
        font-weight: 500;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 1.2rem;
        line-height: 1.5;

        &:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            text-decoration: none;
        }

        &--primary {
            background: $primary-color;
            color: white;

            &:hover {
                background: #f8f9fa;
                color: $primary-darker;
                border-color: $primary-color;
            }
        }

        &--secondary {
            background: #6c757d;
            color: white;

            &:hover {
                background: #5a6268;
                color: white;
            }
        }

        &--warning {
            border: 1px solid #ccc;
            background: #ffc107;
            color: #212529;

            &:hover {
                background: #ffffff;
                color: #959595;
            }
        }

        &--danger {
            border: 1px solid #e53935;
            color: #e53935;

            &:hover {
                background: #ff5252;
                color: white;
            }
        }

        &--sm {
            padding: 0.8rem 1rem;
            font-size: 0.8rem;
        }

        &--add {
            padding: 0.75rem 2.5rem;
            font-size: 1.2rem;
        }
    }
    
    // Table Wrapper
    &__table--wrapper {
        background: white;
        box-shadow: 0px 0px 30px 9px rgba(0,0,0,0.1);
        overflow: hidden;
        border-radius: 10px;
        margin-bottom: 2rem;
    }

    // Table
    &__table {
        width: 100%;
        margin: 0;
        background: white;

        th {
            background: $primary-color;
            color: white;
            padding: 2rem;
            text-align: center;
            font-weight: 600;
            font-size: 1.2rem;
        }

        td {
            padding: 1.5rem;
            border-bottom: 1px solid #e9ecef;
            vertical-align: middle;
            font-size: 1.2rem;
            text-align: center;
            background: white;

            &:nth-child(3) {
                text-align: center;
            }
        }
    }

    // Section Titles
    &__section-title {
        color: #2c3e50;
        font-weight: 600;
        font-size: 2rem;
        margin-bottom: 30px;
        margin-top: 45px;
        text-align: center;
    }

    // Badge Component
    &__badge {
        display: inline-block;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        
        &--active {
            background-color: #d4edda;
            color: #155724;
        }
        
        &--inactive {
            background-color: #f8d7da;
            color: #721c24;
        }
    }
    
    // Additional Info Section
    &__additional-info {
        margin-bottom: 50px;
    }
    
    &__info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 25px;
        margin-top: 30px;
    }
    
    &__info-card {
        display: flex;
        align-items: center;
        padding: 1rem;
        background: #ffffff;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        border-left: 5px solid $primary-color;
        position: relative;
        overflow: hidden;
        
        // Gradient overlay khi hover
        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 123, 255, 0.03) 0%, rgba(0, 86, 179, 0.03) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1;
        }
        
        // Đảm bảo nội dung ở trên overlay
        > * {
            position: relative;
            z-index: 2;
        }
    }
    
    &__info-card-icon {
        flex-shrink: 0;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: $primary-color;
        border-radius: 50%;
        margin-right: 20px;
        transition: all 0.3s ease;
        
        i {
            color: white;
            font-size: 1.4rem;
            transition: transform 0.3s ease;
        }
    }
    
    &__info-card-content {
        flex: 1;
    }
    
    &__info-card-title {
        color: #2c3e50;
        font-weight: 600;
        font-size: 1.4rem;
        margin-bottom: 5px;
    }
    
    &__info-card-desc {
        color: #6c757d;
        margin: 0;
        font-size: 14px;
        line-height: 1.5;
        
        // Placeholder styling cho thông tin không có
        .text-muted {
            color: #94a3b8 !important;
            font-style: italic;
            
            i {
                color: #cbd5e1;
                margin-right: 6px;
            }
            
            // Hover effect cho placeholder
            &:hover {
                color: #64748b !important;
                
                i {
                    color: #94a3b8;
                }
            }
        }
    }
    
    // Itinerary Section
    &__itinerary {
        margin-bottom: 50px;
    }
    
    &__itinerary-list {
        margin-top: 30px;
    }
    
    &__itinerary-item {
        margin-bottom: 20px;
        background: #ffffff;
        border-radius: 12px;
        border: 1px solid #e9ecef;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        overflow: hidden;
        
        &:hover {
            box-shadow: 0 15px 25px rgba(0, 0, 0, 0.13);
        }
    }
    
    &__itinerary-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 25px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        cursor: pointer;
        transition: all 0.3s ease;
        border-bottom: 1px solid #e9ecef;
        
        &:hover {
            background: linear-gradient(135deg, rgba($primary-color, 0.1) 0%, rgba($primary-darker, 0.1) 100%);
        }
        
        &.active {
            background: $primary-color;
            color: white;
            
            .tour-detail__itinerary-day {
                color: white;
                
                &::before {
                    color: white;
                }
            }
            
            .tour-detail__itinerary-icon {
                color: white;
            }
        }
    }
    
    &__itinerary-day {
        font-weight: 600;
        font-size: 1.4rem;
        color: $primary-darker;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 10px;
        transition: color 0.3s ease;
        
        &::before {
            content: "\f017";
            font-family: "Font Awesome 6 Free";
            font-weight: 900;
            font-size: 1.4rem;
            color: $primary-color;
            transition: color 0.3s ease;
        }
    }
    
    &__itinerary-icon {
        font-size: 1.2rem;
        color: $primary-color;
        transition: all 0.3s ease;
        
        i {
            transition: transform 0.3s ease;
        }
    }
    
    &__itinerary-content {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
        
        &.active {
            max-height: 500px;
        }
    }
    
    &__itinerary-body {
        padding: 25px;
        background: #ffffff;
        border-top: 1px solid #f1f3f4;
    }
    
    &__itinerary-desc {
        color: #6c757d;
        line-height: 1.6;
        margin: 0;
        font-size: 1.2rem;
    }
    
    // Images Section
    &__images {
        margin-bottom: 50px;
    }
    
    &__images-grid {
        display: grid;
        grid-template-columns: repeat(2, 40%);
        justify-content: space-around;
        margin-top: 30px;
    }
    
    &__image-item {
        overflow: hidden;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    &__image {
        width: 100%;
        height: 300px;
        object-fit: cover;
        transition: transform 0.3s ease;
    }
    
    // Pricing Section
    &__pricing {
        margin-bottom: 30px;
    }
    
    // Empty State
    &__empty-state {
        text-align: center;
        padding: 60px 20px;
        background: #f8f9fa;
        border-radius: 12px;
        border: 2px dashed #dee2e6;
        
        i {
            font-size: 48px;
            color: #6c757d;
            margin-bottom: 20px;
            display: block;
        }
        
        h3 {
            color: #495057;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        p {
            color: #6c757d;
            margin: 0;
            font-size: 14px;
        }
    }
    
    // Table Styles
    &__table-wrapper {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        overflow: hidden;
    }
    
    &__table {
        width: 100%;
        border-collapse: collapse;
        margin: 0;
    }
    
    &__table-head {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }
    
    &__table-th {
        padding: 18px 15px;
        text-align: left;
        font-weight: 600;
        color: #495057;
        font-size: 13px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border: none;
        border-bottom: 2px solid #dee2e6;
    }

    &__table-row {
        transition: all 0.3s ease;
        
        &:hover {
            background-color: #f8f9fa;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        &:not(:last-child) {
            border-bottom: 1px solid #f1f3f4;
        }
    }
    
    &__table-td {
        padding: 18px 15px;
        vertical-align: middle;
        border: none;
        color: #495057;
        font-size: 14px;
    }
    
    &__table-actions {
        display: flex;
        gap: 8px;
    }
    
    // Button Component
    &__btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 12px 20px;
        border: none;
        border-radius: 8px;
        font-weight: 500;
        font-size: 14px;
        text-decoration: none;
        cursor: pointer;
        transition: all 0.3s ease;
        line-height: 1;
        
        i {
            margin-right: 8px;
            font-size: 12px;
        }
        
        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            text-decoration: none;
        }
        
        &--primary {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            
            &:hover {
                color: white;
            }
        }
        
        &--secondary {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            color: white;
            
            &:hover {
                color: white;
            }
        }
        
        &--warning {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #212529;
            
            &:hover {
                color: #212529;
            }
        }
        
        &--success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            
            &:hover {
                color: white;
            }
        }
        
        &--danger {
            background: linear-gradient(135deg, #dc3545 0%, #bd2130 100%);
            color: white;
            
            &:hover {
                color: white;
            }
        }
        
        &--sm {
            padding: 8px 12px;
            font-size: 12px;
            
            i {
                margin-right: 4px;
                font-size: 10px;
            }
        }
    }
    
    // Modal Styles
    &__modal {
        .modal-content {
            border: none;
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
        }
        
        .modal-header {
            border-bottom: 1px solid #f1f3f4;
            padding: 20px 25px;
            
            .modal-title {
                font-weight: 600;
                color: #2c3e50;
            }
        }
        
        .modal-body {
            padding: 25px;
        }
        
        .modal-footer {
            border-top: 1px solid #f1f3f4;
            padding: 20px 25px;
        }
    }
    
    &__modal-image {
        max-width: 100%;
        height: auto;
        border-radius: 8px;
    }
    
    // Form Controls
    &__form-control {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 1rem;
        font-size: 1.2rem;
        transition: all 0.3s ease;
        
        &:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            outline: none;
        }
    }
    
    &__form-label {
        font-weight: 600;
        color: #2c3e50;
        margin: 1rem 0;
        display: block;
    }

    // Responsive Design
    @media (max-width: 768px) {
        margin-left: 0 !important;
        padding: 1rem;

        &__header {
            flex-direction: column;
            gap: 1rem;
            text-align: center;
            padding: 1rem;
        }

        &__title {
            font-size: 1.5rem;
        }

        &__table--wrapper {
            overflow-x: auto;
        }

        &__table {
            min-width: 600px;

            thead th,
            tbody td {
                padding: 0.75rem;
                font-size: 0.8rem;
            }
        }

        &__btn {
            padding: 0.5rem 1rem;
            font-size: 0.8rem;

            &--sm {
                padding: 0.4rem 0.8rem;
                font-size: 0.75rem;
            }

            &--add {
                padding: 0.5rem 1rem;
                font-size: 0.8rem;
            }
        }

        &__info-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }

        &__images-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }

        &__pricing-header {
            padding: 1rem;
        }
        
        // Itinerary responsive
        &__itinerary-header {
            padding: 15px 20px;
            flex-direction: column;
            gap: 10px;
            text-align: center;
        }
        
        &__itinerary-day {
            font-size: 1rem;
        }
        
        &__itinerary-body {
            padding: 20px;
        }
        
        &__itinerary-desc {
            font-size: 0.9rem;
        }
    }
    
    // Alert Component
    &__alert {
        animation: tour-detail-slideInRight 0.3s ease-out;
    }
    
    // Toast Notifications
    &__toast {
        border-width: 2px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        
        .toast-header {
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            
            strong {
                font-weight: 600;
            }
            
            i {
                font-size: 16px;
            }
        }
        
        .toast-body {
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.98);
            font-weight: 500;
            color: #495057;
        }
        
        &.border-success {
            border-color: #28a745;
            
            .toast-header {
                background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(255, 255, 255, 0.95) 100%);
            }
        }
        
        &.border-danger {
            border-color: #dc3545;
            
            .toast-header {
                background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(255, 255, 255, 0.95) 100%);
            }
        }
        
        &.border-warning {
            border-color: #ffc107;
            
            .toast-header {
                background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 255, 255, 0.95) 100%);
            }
        }
        
        &.border-info {
            border-color: #17a2b8;
            
            .toast-header {
                background: linear-gradient(135deg, rgba(23, 162, 184, 0.1) 0%, rgba(255, 255, 255, 0.95) 100%);
            }
        }
    }
}

// Responsive Design
@media (max-width: 768px) {
    .tour-detail {
        margin-left: 0 !important;
        padding: 15px;
        
        &__info-row {
            grid-template-columns: 1fr;
            gap: 15px;
            padding: 20px;
        }
        
        &__info-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }
        
        &__images-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }
        
        &__table-wrapper {
            overflow-x: auto;
        }
        
        &__table {
            min-width: 800px;
        }
        
        &__table-th,
        &__table-td {
            padding: 12px 10px;
            font-size: 12px;
        }
    }
}

// Animation Keyframes
@keyframes tour-detail-slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes tour-detail-expandContent {
    from {
        max-height: 0;
        opacity: 0;
    }
    to {
        max-height: 500px;
        opacity: 1;
    }
}

@keyframes tour-detail-collapseContent {
    from {
        max-height: 500px;
        opacity: 1;
    }
    to {
        max-height: 0;
        opacity: 0;
    }
}

// Tour Detail Sidebar Items
.tour-detail-sidebar-item {
    margin-bottom: 20px;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    border-left: 5px solid #007bff;
    box-shadow: 0 2px 10px rgba(0, 123, 255, 0.1);
    transition: all 0.3s ease;
    
    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0, 123, 255, 0.15);
    }
    
    h6 {
        color: #2c3e50;
        margin-bottom: 12px;
        font-weight: 600;
        font-size: 14px;
        
        i {
            color: #007bff;
            margin-right: 8px;
        }
    }
    
    p {
        color: #6c757d;
        margin: 0;
        font-size: 14px;
        line-height: 1.5;
    }
}

// Tour Detail Sticky Sidebar Navigation
.tour-detail-sidebar-nav {
    position: sticky;
    top: 20px;
    max-height: calc(100vh - 40px);
    overflow-y: auto;
    
    .nav-link {
        color: #6c757d;
        padding: 12px 20px;
        border-radius: 8px;
        margin-bottom: 8px;
        transition: all 0.3s ease;
        border: 1px solid transparent;
        
        &:hover {
            background-color: #e3f2fd;
            color: #1976d2;
            border-color: #bbdefb;
        }
        
        &.active {
            background-color: #007bff;
            color: white;
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
        }
    }
}

// Tour Detail Images
.tour-detail-image {
    &.cursor-pointer {
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: 8px;
        overflow: hidden;
        
        &:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
    }
}

.tour-detail-image-item {
    overflow: hidden;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    
    img {
        width: 100%;
        height: 200px;
        object-fit: cover;
        transition: transform 0.3s ease;
    }
    
    &:hover img {
        transform: scale(1.1);
    }
}

// Tour Detail Cards
.tour-detail-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    
    &:hover {
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    }
    
    .card-header {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        border-radius: 12px 12px 0 0 !important;
        border: none;
        padding: 20px;
        
        h5 {
            margin: 0;
            font-weight: 600;
            
            i {
                margin-right: 10px;
            }
        }
    }
    
    .card-body {
        padding: 25px;
    }
}

// Tour Detail Table Styles
.tour-detail-table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    
    thead {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        
        th {
            border: none;
            color: #495057;
            font-weight: 600;
            padding: 15px 12px;
            font-size: 13px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
    }
    
    tbody {
        tr {
            transition: all 0.3s ease;
            
            &:hover {
                background-color: #f8f9fa;
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }
            
            td {
                border: none;
                padding: 15px 12px;
                vertical-align: middle;
                border-bottom: 1px solid #f1f3f4;
            }
        }
    }
}

// Tour Detail Buttons
.tour-detail-btn {
    border-radius: 8px;
    padding: 8px 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    
    &.btn-sm {
        padding: 6px 12px;
        font-size: 12px;
    }
    
    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    
    &.btn-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
    }
    
    &.btn-warning {
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
        border: none;
        color: #212529;
    }
    
    &.btn-danger {
        background: linear-gradient(135deg, #dc3545 0%, #bd2130 100%);
        border: none;
    }
}

// Tour Detail Badges
.tour-detail-badge {
    padding: 8px 12px;
    border-radius: 20px;
    font-weight: 500;
    font-size: 11px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

// Tour Detail Modal Enhancements
.tour-detail-modal {
    .modal-content {
        border: none;
        border-radius: 12px;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    }
    
    .modal-header {
        border-bottom: 1px solid #f1f3f4;
        padding: 20px 25px;
        
        .modal-title {
            font-weight: 600;
            color: #2c3e50;
        }
    }
    
    .modal-body {
        padding: 25px;
    }
    
    .modal-footer {
        border-top: 1px solid #f1f3f4;
        padding: 20px 25px;
    }
}

// Tour Detail Form Controls
.tour-detail-form-control {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 15px;
    transition: all 0.3s ease;
    
    &:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
}

.tour-detail-form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 8px;
}

// Tour Detail Itinerary Styles
.tour-detail-itinerary-day {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #28a745;
    
    h6 {
        color: #28a745;
        font-weight: 600;
        margin-bottom: 10px;
        
        i {
            margin-right: 8px;
        }
    }
    
    p {
        color: #6c757d;
        line-height: 1.6;
        margin: 0;
    }
}



