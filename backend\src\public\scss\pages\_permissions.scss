@use "../abstracts" as *;

// permissions Management Styles
.permissions {
    margin-left: 240px;
    padding: 2rem;
    height: 100%;
    min-width: calc(100% - 260px);
    overflow: auto;

    // Header Section
    &__header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        background: white;
        padding: 1.5rem 2rem;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border: 1px solid #e9ecef;
    }

    &__title {
        font-size: 1.8rem;
        font-weight: 600;
        color: #2c3e50;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    &__actions {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    &__btn--delete-selected {
        animation: slideIn 0.3s ease-out;
    }

    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateX(20px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    // Buttons
    &__btn {
        padding: 0.75rem 2.5rem;
        border: 1px solid transparent;
        outline: none;
        border-radius: 5px;
        font-weight: 500;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 1.2rem;
        line-height: 1.5;

        &:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        &--primary {
            background: $primary-color;
            color: white;

            &:hover {
                background: #f8f9fa;
                color: $primary-darker;
                border-color: $primary-color;
            }
        }

        &--secondary {
            background: #6c757d;
            color: white;

            &:hover {
                background: #5a6268;
                color: white;

            }
        }

        &--warning {
            border: 1px solid #ccc;

            &:hover {
                background: #ffffff;
                color: #959595;
            }
        }

        &--danger {
            border: 1px solid #e53935;
            color: #e53935;

            &:hover {
                background: #ff5252;
                color: white;
            }
        }

        &--sm {
            padding: 0.8rem 1rem;
            font-size: 0.8rem;
        }
    }

    // Table
    &__table--wrapper {
        width: 100%;
        margin: 0;
        background: white;
        overflow: hidden;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        border: 1px solid #e9ecef;
        transition: box-shadow 0.3s ease;

        &:hover {
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        th {
            background: $primary-color;
            color: white;
            padding: 2rem;
            font-weight: 600;
            font-size: 1.4rem;
            white-space: nowrap;
            border: none;
        }

        td {
            padding: 1.5rem;
            border-bottom: 1px solid #e9ecef;
            vertical-align: middle;
            font-size: 1.2rem;
            background: white;
            letter-spacing: 0.01rem;
            border-left: none;
            border-right: none;
            background: none !important;

            &:first-child {
                font-weight: 600;
            }

            &:nth-child(3) {
                text-align: center;
            }
        }
    }

    // Module Groups Styling
    &__table {
        .module-cell {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            font-weight: 700;
            color: #495057;
            vertical-align: middle;
            border-right: 3px solid #dee2e6 !important;
            position: relative;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);

            &::after {
                content: '';
                position: absolute;
                right: -2px;
                top: 0;
                bottom: 0;
                width: 3px;
                background: linear-gradient(135deg, #6c5ce7, #a29bfe);
                box-shadow: 0 0 8px rgba(108, 92, 231, 0.3);
            }
        }

        .module-group-start {
            border-top: 3px solid #6c5ce7 !important;
            box-shadow: 0 -2px 8px rgba(108, 92, 231, 0.1);

            td {
                border-top: 3px solid #6c5ce7 !important;
            }
        }

        // Alternating row colors within modules
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        tr:hover {
            background-color: #e3f2fd;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
            transition: all 0.2s ease;
        }

        // Checkbox styling
        input[type="checkbox"] {
            width: 18px;
            height: 18px;
            border: 2px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;

            &:checked {
                background-color: #6c5ce7;
                border-color: #6c5ce7;
                box-shadow: 0 0 10px rgba(108, 92, 231, 0.3);
            }

            &:hover {
                border-color: #6c5ce7;
                box-shadow: 0 0 5px rgba(108, 92, 231, 0.2);
            }
        }
    }
}

