const Permission = require("../models/permissonsModel");

const permissionsData = [
    // TOUR Module
    {
        name: "CREATE_TOUR",
        description: "Cho phép tạo tour mới",
        module: "TOUR",
    },
    {
        name: "READ_TOUR",
        description: "Cho phép xem thông tin tour",
        module: "TOUR",
    },
    {
        name: "UPDATE_TOUR",
        description: "Cho phép cập nhật thông tin tour",
        module: "TOUR",
    },
    { name: "DELETE_TOUR", description: "Cho phép xóa tour", module: "TOUR" },

    // CATEGORY Module
    {
        name: "CREATE_CATEGORY",
        description: "Cho phép tạo danh mục mới",
        module: "CATEGORY",
    },
    {
        name: "READ_CATEGORY",
        description: "Cho phép xem thông tin danh mục",
        module: "CATEGORY",
    },
    {
        name: "UPDATE_CATEGORY",
        description: "Cho phép cập nhật thông tin danh mục",
        module: "CATEGORY",
    },
    {
        name: "DELETE_CATEGORY",
        description: "Cho phép xóa danh mục",
        module: "CATEGORY",
    },

    // DEPARTURE Module
    {
        name: "CREATE_DEPARTURE",
        description: "Cho phép tạo điểm khởi hành mới",
        module: "DEPARTURE",
    },
    {
        name: "READ_DEPARTURE",
        description: "Cho phép xem thông tin điểm khởi hành",
        module: "DEPARTURE",
    },
    {
        name: "UPDATE_DEPARTURE",
        description: "Cho phép cập nhật thông tin điểm khởi hành",
        module: "DEPARTURE",
    },
    {
        name: "DELETE_DEPARTURE",
        description: "Cho phép xóa điểm khởi hành",
        module: "DEPARTURE",
    },

    // DESTINATION Module
    {
        name: "CREATE_DESTINATION",
        description: "Cho phép tạo điểm đến mới",
        module: "DESTINATION",
    },
    {
        name: "READ_DESTINATION",
        description: "Cho phép xem thông tin điểm đến",
        module: "DESTINATION",
    },
    {
        name: "UPDATE_DESTINATION",
        description: "Cho phép cập nhật thông tin điểm đến",
        module: "DESTINATION",
    },
    {
        name: "DELETE_DESTINATION",
        description: "Cho phép xóa điểm đến",
        module: "DESTINATION",
    },

    // TRANSPORTATION Module
    {
        name: "CREATE_TRANSPORTATION",
        description: "Cho phép tạo phương tiện vận chuyển mới",
        module: "TRANSPORTATION",
    },
    {
        name: "READ_TRANSPORTATION",
        description: "Cho phép xem thông tin phương tiện vận chuyển",
        module: "TRANSPORTATION",
    },
    {
        name: "UPDATE_TRANSPORTATION",
        description: "Cho phép cập nhật thông tin phương tiện vận chuyển",
        module: "TRANSPORTATION",
    },
    {
        name: "DELETE_TRANSPORTATION",
        description: "Cho phép xóa phương tiện vận chuyển",
        module: "TRANSPORTATION",
    },

    // ORDER Module
    {
        name: "READ_ORDER",
        description: "Cho phép xem thông tin đơn hàng",
        module: "ORDER",
    },
    {
        name: "UPDATE_ORDER",
        description: "Cho phép cập nhật thông tin đơn hàng",
        module: "ORDER",
    },
    {
        name: "DELETE_ORDER",
        description: "Cho phép xóa đơn hàng",
        module: "ORDER",
    },

    // ROLES Module
    {
        name: "CREATE_ROLES",
        description: "Cho phép tạo vai trò",
        module: "ROLES",
    },
    {
        name: "READ_ROLES",
        description: "Cho phép xem vai trò",
        module: "ROLES",
    },
    {
        name: "UPDATE_ROLES",
        description: "Cho phép cập nhật vai trò",
        module: "ROLES",
    },
    {
        name: "DELETE_ROLES",
        description: "Cho phép xóa vai trò",
        module: "ROLES",
    },

    // PERMISSIONS Module
    {
        name: "READ_PERMISSIONS",
        description: "Cho phép xem quyền hạn",
        module: "PERMISSIONS",
    },
    {
        name: "UPDATE_PERMISSIONS",
        description: "Cho phép cập nhật quyền hạn",
        module: "PERMISSIONS",
    },

    // USERS/ACCOUNT Module
    {
        name: "CREATE_USERS",
        description: "Cho phép tạo tài khoản người dùng",
        module: "USERS",
    },
    {
        name: "READ_USERS",
        description: "Cho phép xem tài khoản người dùng",
        module: "USERS",
    },
    {
        name: "UPDATE_USERS",
        description: "Cho phép cập nhật tài khoản người dùng",
        module: "USERS",
    },
    {
        name: "DELETE_USERS",
        description: "Cho phép xóa tài khoản người dùng",
        module: "USERS",
    },

    // SETTINGS Module
    {
        name: "READ_SETTINGS",
        description: "Cho phép xem cài đặt hệ thống",
        module: "SETTINGS",
    },
    {
        name: "UPDATE_SETTINGS",
        description: "Cho phép cập nhật cài đặt hệ thống",
        module: "SETTINGS",
    },
];

const seedPermissions = async () => {
    try {
        console.log("🚀 Bắt đầu seed dữ liệu permissions...");

        // Xóa dữ liệu cũ
        await Permission.deleteMany({});
        console.log("✅ Đã xóa dữ liệu permissions cũ");

        // Thêm dữ liệu mới
        const permissions = await Permission.insertMany(permissionsData);
        console.log(`✅ Đã tạo ${permissions.length} permissions thành công`);

        // In ra danh sách permissions
        console.log("📋 Danh sách permissions:");
        permissions.forEach((permission) => {
            console.log(`   - ${permission.name}: ${permission.description}`);
        });

        return permissions;
    } catch (error) {
        console.error("❌ Lỗi khi seed permissions:", error);
        throw error;
    }
};

module.exports = seedPermissions;
