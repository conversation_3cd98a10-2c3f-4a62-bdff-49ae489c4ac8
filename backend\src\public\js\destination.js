// Global function to handle limit change
function handleLimitChange(selectElement) {
    console.log('Limit changed to:', selectElement.value);
    
    // Get current URL params
    const urlParams = new URLSearchParams(window.location.search);
    
    // Update limit param
    urlParams.set('limit', selectElement.value);
    
    // Reset to page 1 when changing limit
    urlParams.set('page', '1');
    
    // Create new URL
    const newUrl = window.location.pathname + '?' + urlParams.toString();
    
    console.log('Redirecting to:', newUrl);
    
    // Redirect to new URL
    window.location.href = newUrl;
}

// Destination Management JavaScript - Simple Version
document.addEventListener("DOMContentLoaded", function () {
    console.log("DOM loaded, setting up handlers...");

    // Check if we just redirected from AJAX form submission
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('added') === 'true' || urlParams.get('updated') === 'true') {
        // Clear the parameter from URL without reloading page
        window.history.replaceState({}, '', window.location.pathname);
        
        // Hide any server-side flash messages since we already showed toast
        const flashMessages = document.querySelectorAll('.modal-notify--active');
        flashMessages.forEach(msg => {
            msg.style.display = 'none';
        });
    }

    // Setup delete handlers
    setupDeleteHandlers();
    
    // Setup edit form handler
    setupEditFormHandler();
    
    // Setup add form handler
    setupAddFormHandler();
    
    // Setup search functionality
    setupSearchHandler();
    
    // Setup items per page functionality
    setupItemsPerPageHandler();
    
    // Setup pagination
    setupPaginationHandler();
    
    // Setup keyboard shortcuts
    setupKeyboardShortcuts();
});

function setupDeleteHandlers() {
    const deleteButtons = document.querySelectorAll(".destination__form--delete");
    console.log("Found delete forms:", deleteButtons.length);

    deleteButtons.forEach((form, index) => {
        // Remove existing listeners to avoid duplicates
        if (form.hasAttribute("data-delete-setup")) {
            return;
        }
        form.setAttribute("data-delete-setup", "true");

        console.log(`Setting up delete handler for form ${index}:`, form);

        // Add event listener to form
        form.addEventListener("submit", function (e) {
            e.preventDefault();
            console.log("Delete form submitted, preventing default");

            // Get destination name
            const row = form.closest("tr");
            const nameCell = row.querySelector("td:nth-child(2) strong");
            const destinationName = nameCell
                ? nameCell.textContent.trim()
                : "danh mục này";

            console.log("destination name:", destinationName);
            console.log("Form action:", form.action);

            // Show modal instead of alert
            showDeleteModal(destinationName, form, row);
        });

        // Also add click handler to the button itself as backup
        const deleteBtn = form.querySelector('button[type="submit"]');
        if (deleteBtn) {
            deleteBtn.addEventListener("click", function (e) {
                console.log("Delete button clicked");
                // Let the form submit handler take over
            });
        }
    });
}

// Show delete confirmation modal
function showDeleteModal(destinationName, form, row) {
    // Set destination name in modal
    document.getElementById('destinationNameToDelete').textContent = destinationName;
    
    // Get modal instance
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    
    // Show modal
    deleteModal.show();
    
    // Handle confirm delete button
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    
    // Remove existing listeners to avoid duplicates
    const newConfirmBtn = confirmDeleteBtn.cloneNode(true);
    confirmDeleteBtn.parentNode.replaceChild(newConfirmBtn, confirmDeleteBtn);
    
    // Add new listener
    newConfirmBtn.addEventListener('click', function() {
        console.log("User confirmed delete, submitting form...");
        
        // Hide modal
        deleteModal.hide();
        
        // Show loading state
        const submitBtn = form.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            submitBtn.disabled = true;
        }

        // Add loading class to row
        row.classList.add("destination-row--deleting");

        // Submit via AJAX to show toast notification
        fetch(form.action, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            // Check if response is JSON
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return response.json().then(data => {
                    if (!response.ok) {
                        throw new Error(data.message || `HTTP ${response.status}: ${response.statusText}`);
                    }
                    return data;
                });
            } else {
                // If not JSON, treat as successful form submission
                if (response.ok) {
                    return { success: true, message: 'Xóa điểm đến thành công!' };
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            }
        })
        .then(data => {
            if (data.success) {
                // Show success toast
                showToastNotification(data.message, 'success');
                // Reload page after short delay to get updated data from server
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                throw new Error(data.message || 'Xóa điểm đến thất bại');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToastNotification(error.message || 'Có lỗi xảy ra khi xóa điểm đến!', 'error');
            // Remove loading state
            row.classList.remove("destination-row--deleting");
            // Reset button state
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-trash"></i>';
                submitBtn.disabled = false;
            }
        });
    });
}

// Toggle Status Function
function toggleStatus(button) {
    const destinationId = button.getAttribute("data-id");
    const currentStatus = button.getAttribute("data-status");

    // Set fixed width to prevent table jumping
    if (!button.style.minWidth) {
        button.style.minWidth = button.offsetWidth + 'px';
    }

    // Disable button during request
    button.disabled = true;
    button.style.opacity = "0.6";

    fetch(`/destination/toggle-status/${destinationId}`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "X-CSRF-Token": window.csrfToken
        },
        body: JSON.stringify({
            _csrf: window.csrfToken
        })
    })
        .then((response) => response.json())
        .then((data) => {
            if (data.success) {
                // Update button attributes
                button.setAttribute("data-status", data.status);

                // Update button classes and text with shorter labels
                if (data.status === "Hoạt động") {
                    button.className =
                        "destination__badge destination__badge--toggle destination__badge--success";
                    button.innerHTML = 'Hoạt động';
                } else {
                    button.className =
                        "destination__badge destination__badge--toggle destination__badge--inactive";
                    button.innerHTML = 'Tạm dừng';
                }

                // Show success notification
                showToastNotification(data.message, "success");
            } else {
                showToastNotification(data.message, "error");
            }
        })
        .catch((error) => {
            console.error("Error:", error);
            showToastNotification("Có lỗi xảy ra khi thay đổi trạng thái", "error");
        })
        .finally(() => {
            // Re-enable button
            button.disabled = false;
            button.style.opacity = "1";
        });
}

// Setup edit form handler
function setupEditFormHandler() {
    const editForm = document.querySelector('form[action*="/destination/edit/"]');
    if (!editForm) return;

    editForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalBtnText = submitBtn.innerHTML;
        
        // Validate form client-side
        const nameInput = this.querySelector('input[name="name"]');
        if (!nameInput.value.trim()) {
            showToastNotification('Tên điểm đến không được để trống', 'error');
            nameInput.focus();
            return;
        }
        
        // Show loading state
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Đang cập nhật...';
        submitBtn.disabled = true;
        
        // Use FormData for file upload support
        const editFormData = new FormData(this);
        
        // Submit via AJAX
        fetch(this.action, {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: editFormData
        })
        .then(response => {
            // Check if response is JSON
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return response.json().then(data => {
                    if (!response.ok) {
                        throw new Error(data.message || `HTTP ${response.status}: ${response.statusText}`);
                    }
                    return data;
                });
            } else {
                // If not JSON, treat as successful form submission
                if (response.ok) {
                    return { success: true, message: 'Cập nhật điểm đến thành công!' };
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            }
        })
        .then(data => {
            if (data.success) {
                // Show success toast
                showToastNotification(data.message, 'success');
                // Redirect to destination list after a longer delay to see the toast
                setTimeout(() => {
                    window.location.href = '/destination?updated=true';
                }, 2000);
            } else {
                throw new Error(data.message || 'Có lỗi xảy ra');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToastNotification(error.message || 'Có lỗi xảy ra khi cập nhật điểm đến!', 'error');
            // Reset button state
            submitBtn.innerHTML = originalBtnText;
            submitBtn.disabled = false;
        });
    });
}

// Setup add form handler
function setupAddFormHandler() {
    const addForm = document.querySelector('form[action="/destination/add"]');
    if (!addForm) return;

    addForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Get form data manually
        const nameInput = this.querySelector('input[name="name"]');
        const infoInput = this.querySelector('textarea[name="info"]');
        
        const name = nameInput ? nameInput.value.trim() : '';
        const info = infoInput ? infoInput.value.trim() : '';
        
        // Client-side validation
        if (!name) {
            showToastNotification('Tên điểm đến không được để trống', 'error');
            nameInput.focus();
            return;
        }
        
        if (name.length < 2) {
            showToastNotification('Tên điểm đến phải có ít nhất 2 ký tự', 'error');
            nameInput.focus();
            return;
        }
        
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalBtnText = submitBtn.innerHTML;
        
        // Show loading state
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Đang thêm...';
        submitBtn.disabled = true;
        
        // Create FormData to handle file upload
        const addFormData = new FormData(this);
        
        // Submit via AJAX
        fetch(this.action, {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: addFormData
        })
        .then(response => {
            // Check if response is JSON
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return response.json().then(data => {
                    if (!response.ok) {
                        throw new Error(data.message || `HTTP ${response.status}: ${response.statusText}`);
                    }
                    return data;
                });
            } else {
                // If not JSON, treat as successful form submission
                if (response.ok) {
                    return { success: true, message: 'Thêm điểm đến thành công!' };
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            }
        })
        .then(data => {
            if (data.success) {
                // Show success toast
                showToastNotification(data.message || 'Thêm điểm đến thành công', 'success');
                // Reset form
                this.reset();
                // Redirect to destination list after a longer delay to see the toast
                setTimeout(() => {
                    window.location.href = '/destination?added=true';
                }, 2000);
            } else {
                throw new Error(data.message || 'Thêm điểm đến thất bại');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            // Show error toast
            showToastNotification(error.message || 'Có lỗi xảy ra khi thêm điểm đến', 'error');
            // Reset button state
            submitBtn.innerHTML = originalBtnText;
            submitBtn.disabled = false;
        });
    });
}

// Setup items per page functionality
function setupItemsPerPageHandler() {
    
    const itemsPerPageSelect = document.querySelector('#itemsPerPage');
    
    if (!itemsPerPageSelect) {
        console.log('Items per page select not found');
        return;
    }
    
    // Handle form submission when select changes
    itemsPerPageSelect.addEventListener('change', function() {
        
        // Get current URL params
        const urlParams = new URLSearchParams(window.location.search);
        
        // Update limit param
        urlParams.set('limit', this.value);
        
        // Reset to page 1 when changing limit
        urlParams.set('page', '1');
        
        // Create new URL
        const newUrl = window.location.pathname + '?' + urlParams.toString();
        
        // Redirect to new URL
        window.location.href = newUrl;
    });
}

// Setup search functionality
function setupSearchHandler() {
    const searchForm = document.querySelector('.destination__search-form');
    const searchInput = searchForm?.querySelector('input[name="search"]');
    
    if (!searchForm || !searchInput) return;
    
    // Auto-submit search after user stops typing
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            if (this.value.trim() !== this.getAttribute('data-original-value')) {
                searchForm.submit();
            }
        }, 500);
    });
    
    // Store original value
    searchInput.setAttribute('data-original-value', searchInput.value);
    
    // Handle search form submission
    searchForm.addEventListener('submit', function(e) {
        const searchValue = searchInput.value.trim();
        
        // If search is empty, redirect to main page
        if (!searchValue) {
            e.preventDefault();
            window.location.href = '/destination';
            return;
        }
        
        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        if (submitBtn) {
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang tìm...';
            submitBtn.disabled = true;
            
            // Re-enable after a short delay in case of errors
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 3000);
        }
    });
}

// Setup pagination functionality
function setupPaginationHandler() {
    const paginationLinks = document.querySelectorAll('.destination__pagination .page-link');
    
    paginationLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Don't prevent default - let normal navigation happen
            // But add loading state
            const text = this.textContent;
            
            // Only add loading for actual page links, not disabled ones
            if (!this.closest('.page-item').classList.contains('disabled')) {
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                
                // Restore original text after navigation starts
                setTimeout(() => {
                    this.innerHTML = text;
                }, 100);
            }
        });
    });
    
    // Handle quick jump form
    const quickJumpForm = document.querySelector('.destination__quick-jump form');
    if (quickJumpForm) {
        quickJumpForm.addEventListener('submit', function(e) {
            const pageInput = this.querySelector('input[name="page"]');
            const pageValue = parseInt(pageInput.value);
            const maxPage = parseInt(pageInput.getAttribute('max'));
            
            // Validate page number
            if (isNaN(pageValue) || pageValue < 1 || pageValue > maxPage) {
                e.preventDefault();
                
                // Show error message
                showToastNotification(`Vui lòng nhập số trang từ 1 đến ${maxPage}`, 'error');
                
                // Focus and select the input
                pageInput.focus();
                pageInput.select();
                return;
            }
            
            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                const originalHTML = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                submitBtn.disabled = true;
                
                // Restore after a delay in case of errors
                setTimeout(() => {
                    submitBtn.innerHTML = originalHTML;
                    submitBtn.disabled = false;
                }, 3000);
            }
        });
        
        // Handle Enter key in page input
        const pageInput = quickJumpForm.querySelector('input[name="page"]');
        if (pageInput) {
            pageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    quickJumpForm.dispatchEvent(new Event('submit'));
                }
            });
            
            // Auto-select text when focused
            pageInput.addEventListener('focus', function() {
                this.select();
            });
        }
    }
}

// Setup keyboard shortcuts for pagination
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Only work if no input is focused
        if (document.activeElement.tagName === 'INPUT' || 
            document.activeElement.tagName === 'TEXTAREA' ||
            document.activeElement.isContentEditable) {
            return;
        }
        
        const pagination = document.querySelector('.departure__pagination');
        if (!pagination) return;
        
        const prevLink = pagination.querySelector('.page-item:not(.disabled) .page-link[aria-label="Trang trước"]');
        const nextLink = pagination.querySelector('.page-item:not(.disabled) .page-link[aria-label="Trang sau"]');
        
        // Arrow keys for navigation
        if (e.key === 'ArrowLeft' && prevLink) {
            e.preventDefault();
            prevLink.click();
        } else if (e.key === 'ArrowRight' && nextLink) {
            e.preventDefault();
            nextLink.click();
        }
        
        // Focus search with / key
        if (e.key === '/' || e.key === 'Control+f') {
            e.preventDefault();
            const searchInput = document.querySelector('input[name="search"]');
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }
    });
}

// CSS Styles for row animation
const styles = `
    .departure-row--deleting {
        background: #ffe6e6 !important;
        animation: rowFadeOut 0.5s ease-in-out;
    }
    
    @keyframes rowFadeOut {
        0% { opacity: 1; transform: scale(1); }
        50% { opacity: 0.5; transform: scale(0.98); }
        100% { opacity: 1; transform: scale(1); }
    }
`;

// Inject styles
const styleSheet = document.createElement("style");
styleSheet.textContent = styles;
document.head.appendChild(styleSheet);

document.addEventListener('DOMContentLoaded', function() {
  // Cuộn lên đầu bảng khi click phân trang
  const paginationLinks = document.querySelectorAll('.destination__pagination-item a');
  paginationLinks.forEach(link => {
    link.addEventListener('click', function() {
      // Sau khi chuyển trang, trình duyệt sẽ reload, nên chỉ cần cuộn lên nếu dùng AJAX
      // Nếu muốn cuộn lên khi reload, có thể dùng:
      // window.scrollTo({ top: 0, behavior: 'smooth' });
    });
  });
});

// Seed sample data function
function seedData() {
    if (confirm('Bạn có muốn thêm 15 điểm đến mẫu? Điều này sẽ xóa toàn bộ dữ liệu hiện tại.')) {
        fetch('/destination/seed')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToastNotification(data.message, 'success');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    showToastNotification(data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToastNotification('Có lỗi xảy ra khi thêm dữ liệu mẫu', 'error');
            });
    }
}

// Multiple deletion functionality
function toggleSelectAll(selectAllCheckbox) {
    const checkboxes = document.querySelectorAll('.destination-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });
    toggleDeleteButton();
}

function toggleDeleteButton() {
    const checkboxes = document.querySelectorAll('.destination-checkbox:checked');
    const deleteBtn = document.getElementById('deleteSelectedBtn');
    
    if (checkboxes.length > 0) {
        deleteBtn.style.display = 'inline-flex';
        deleteBtn.innerHTML = `<i class="fas fa-trash"></i> Xóa đã chọn (${checkboxes.length})`;
    } else {
        deleteBtn.style.display = 'none';
    }
    
    // Update select all checkbox state
    const allCheckboxes = document.querySelectorAll('.destination-checkbox');
    const selectAllCheckbox = document.getElementById('selectAll');
    
    if (checkboxes.length === allCheckboxes.length) {
        selectAllCheckbox.checked = true;
        selectAllCheckbox.indeterminate = false;
    } else if (checkboxes.length > 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = true;
    } else {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    }
}

function deleteSelected() {
    const checkboxes = document.querySelectorAll('.destination-checkbox:checked');
    
    if (checkboxes.length === 0) {
        alert('Vui lòng chọn ít nhất một điểm đến để xóa!');
        return;
    }
    
    // Update modal content
    document.getElementById('selectedCount').textContent = checkboxes.length;
    
    // Show modal
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteMultipleModal'));
    deleteModal.show();
    
    // Handle confirm delete
    const confirmBtn = document.getElementById('confirmDeleteMultipleBtn');
    const newConfirmBtn = confirmBtn.cloneNode(true);
    confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
    
    newConfirmBtn.addEventListener('click', function() {
        // Collect selected IDs
        const selectedIds = Array.from(checkboxes).map(cb => cb.value);
        
        // Show loading state
        newConfirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Đang xóa...';
        newConfirmBtn.disabled = true;
        
        // Send delete request
        fetch('/destination/delete-multiple', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ ids: selectedIds })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Hide modal immediately
                deleteModal.hide();
                
                // Show success message
                showToastNotification('Đã xóa thành công ' + selectedIds.length + ' điểm đến!', 'success');
                
                // Reload page after short delay to ensure data consistency
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                throw new Error(data.message || 'Có lỗi xảy ra khi xóa điểm đến');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToastNotification('Có lỗi xảy ra: ' + error.message, 'error');
            
            // Reset button state
            newConfirmBtn.innerHTML = '<i class="fas fa-trash me-2"></i>Xóa tất cả';
            newConfirmBtn.disabled = false;
        })
        .finally(() => {
            // Ensure modal is properly hidden on any outcome
            try {
                deleteModal.hide();
            } catch (e) {
                console.warn('Modal already hidden');
            }
        });
    });
}

// Helper functions for STT and table management
function updateRowNumbers() {
    const tableBody = document.querySelector('.destination__table tbody');
    if (!tableBody) return;
    
    // Get all data rows (exclude empty rows)
    const dataRows = tableBody.querySelectorAll('tr:not([data-empty])');
    
    // Update STT based on current position in table (index + 1)
    dataRows.forEach((row, index) => {
        const firstCell = row.querySelector('td:first-child');
        // Only update if this is a STT cell (not checkbox or other)
        if (firstCell && !firstCell.querySelector('input[type="checkbox"]')) {
            firstCell.textContent = index + 1;
        }
    });
}

function handleEmptyTable() {
    const tableBody = document.querySelector('.destination__table tbody');
    if (!tableBody) return;
    
    // Check if there are any actual data rows (not empty message rows)
    const dataRows = tableBody.querySelectorAll('tr:not([data-empty])');
    
    if (dataRows.length === 0) {
        // Clear all existing rows and show empty message
        const colspan = tableBody.closest('table').querySelector('thead tr').children.length;
        tableBody.innerHTML = `
            <tr data-empty="true">
                <td colspan="${colspan}" class="destination__table-empty text-center py-4">
                    <i class="fas fa-map-marker-alt text-muted mb-2" style="font-size: 3rem;"></i>
                    <p class="text-muted mb-0">Chưa có điểm đến nào.</p>
                    <p class="text-muted small">Thêm điểm đến đầu tiên của bạn!</p>
                </td>
            </tr>
        `;
        
        // Hide pagination if exists
        const pagination = document.querySelector('.destination__pagination');
        if (pagination) {
            pagination.style.display = 'none';
        }
    } else {
        // Show pagination if hidden and there's data
        const pagination = document.querySelector('.destination__pagination');
        if (pagination) {
            pagination.style.display = '';
        }
    }
}

function addNewRowToTable(destinationData) {
    const tableBody = document.querySelector('.destination__table tbody');
    if (!tableBody) return;
    
    const emptyRow = tableBody.querySelector('td.destination__table-empty');
    
    // Remove empty message if exists
    if (emptyRow) {
        emptyRow.closest('tr').remove();
    }
    
    // Get current number of data rows (excluding empty rows)
    const currentDataRows = tableBody.querySelectorAll('tr:not([data-empty])');
    const newSTT = currentDataRows.length + 1; // New record will be at the end
    
    // Create new row
    const newRow = document.createElement('tr');
    newRow.innerHTML = `
        <td>${newSTT}</td>
        <td>
            <strong>${destinationData.name}</strong>
        </td>
        <td>
            ${destinationData.description || 'Không có mô tả'}
        </td>
        <td>
            <button
                class="destination__badge destination__badge--toggle destination__badge--success"
                data-id="${destinationData.id}"
                data-status="Hoạt động"
                onclick="toggleStatus(this)"
                title="Nhấn để thay đổi trạng thái"
            >
                Hoạt động
            </button>
        </td>
        <td>
            <span class="destination__user-info">
                <i class="fas fa-user me-1"></i>
                Admin
            </span>
        </td>
        <td>
            <span class="destination__user-info">
                <i class="fas fa-user-edit me-1"></i>
                Admin
            </span>
        </td>
        <td>
            <div class="d-flex gap-1 justify-content-center">
                <a
                    href="/destination/edit/${destinationData.id}"
                    class="destination__btn destination__btn--warning destination__btn--sm"
                    title="Chỉnh sửa điểm đến"
                >
                    <i class="fas fa-edit"></i>
                </a>
                <form
                    class="destination__form destination__form--delete"
                    action="/destination/delete/${destinationData.id}"
                    method="POST"
                    data-destination-name="${destinationData.name}"
                >
                    <button
                        type="submit"
                        class="destination__btn destination__btn--danger destination__btn--sm"
                        title="Xóa điểm đến"
                    >
                        <i class="fas fa-trash"></i>
                    </button>
                </form>
            </div>
        </td>
    `;
    
    // IMPORTANT: Add to END of table (using appendChild, not insertBefore)
    tableBody.appendChild(newRow);
    
    // Setup delete handler for new row
    const deleteForm = newRow.querySelector('.destination__form--delete');
    setupSingleDeleteHandler(deleteForm);
    
    // Show pagination if hidden
    const pagination = document.querySelector('.destination__pagination');
    if (pagination) {
        pagination.style.display = '';
    }
}

function setupSingleDeleteHandler(form) {
    if (!form || form.hasAttribute("data-delete-setup")) {
        return;
    }
    form.setAttribute("data-delete-setup", "true");

    form.addEventListener("submit", function (e) {
        e.preventDefault();
        
        const row = form.closest("tr");
        const nameCell = row.querySelector("td:nth-child(2) strong");
        const destinationName = nameCell ? nameCell.textContent.trim() : "điểm đến này";
        
        showDeleteModal(destinationName, form, row);
    });
}
