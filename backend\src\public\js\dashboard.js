document.addEventListener('DOMContentLoaded', function() {
    console.log('Dashboard JS loaded');
    
    // Parse dashboard data from the server
    let dashboardData = {};
    try {
        const dataElement = document.getElementById('dashboardData');
        if (dataElement && dataElement.textContent) {
            dashboardData = JSON.parse(dataElement.textContent);
            console.log('Dashboard data loaded:', dashboardData);
        }
    } catch (error) {
        console.error('Error parsing dashboard data:', error);
    }
    
    // Xử lý hiển thị thông báo toast
    const dashboard = document.querySelector('.dashboard');
    if (dashboard) {
        // Xử lý thông báo thành công
        if (dashboard.hasAttribute('data-success-message')) {
            const message = dashboard.getAttribute('data-success-message');
            if (typeof showToastNotification === 'function') {
                showToastNotification(message, "success");
            } else {
                // Tạo thông báo tạm thời nếu không có hàm showToastNotification
                const tempNotification = document.createElement('div');
                tempNotification.className = 'alert alert-success position-fixed top-0 end-0 m-3';
                tempNotification.innerHTML = message;
                document.body.appendChild(tempNotification);
                setTimeout(() => {
                    tempNotification.remove();
                }, 5000);
            }
        }
        
        // Xử lý thông báo lỗi
        if (dashboard.hasAttribute('data-error-message')) {
            const errorMessage = dashboard.getAttribute('data-error-message');
            if (typeof showToastNotification === 'function') {
                showToastNotification(errorMessage, "error");
            } else {
                // Tạo thông báo tạm thời nếu không có hàm showToastNotification
                const tempNotification = document.createElement('div');
                tempNotification.className = 'alert alert-danger position-fixed top-0 end-0 m-3';
                tempNotification.innerHTML = errorMessage;
                document.body.appendChild(tempNotification);
                setTimeout(() => {
                    tempNotification.remove();
                }, 5000);
            }
        }
    }
    
    // Revenue Chart
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    const monthlyRevenueData = dashboardData.monthlyRevenue || Array(12).fill(0);
    const revenueChart = new Chart(revenueCtx, {
        type: 'line',
        data: {
            labels: ['Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6', 'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'],
            datasets: [{
                label: 'Doanh thu',
                data: monthlyRevenueData,
                borderColor: '#36A2EB',
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderWidth: 2,
                tension: 0.3,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString() + ' đ';
                        }
                    }
                }
            }
        }
    });

    // Order Status Chart
    const orderStatusCtx = document.getElementById('orderStatusChart').getContext('2d');
    const pendingOrders = dashboardData.pendingOrders || 0;
    const confirmedOrders = dashboardData.confirmedOrders || 0;
    const cancelledOrders = dashboardData.cancelledOrders || 0;
    
    const orderStatusChart = new Chart(orderStatusCtx, {
        type: 'doughnut',
        data: {
            labels: ['Đang chờ', 'Đã xác nhận', 'Đã hủy'],
            datasets: [{
                data: [pendingOrders, confirmedOrders, cancelledOrders],
                backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56'],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.raw || 0;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            },
            cutout: '60%'
        }
    });

    // Tab switching for charts
    document.querySelectorAll('.chart-tab').forEach(tab => {
        tab.addEventListener('click', function() {
            // Remove active class from all tabs in the same container
            const tabContainer = this.closest('.chart-tabs');
            tabContainer.querySelectorAll('.chart-tab').forEach(t => {
                t.classList.remove('active');
            });
            
            // Add active class to clicked tab
            this.classList.add('active');
            
            // Update chart data based on tab
            const chartType = this.getAttribute('data-type');
            const chartContainer = this.closest('.chart-container');
            
            if (chartContainer.classList.contains('revenue-chart')) {
                // Update revenue chart based on selected tab
                if (chartType === 'revenue') {
                    revenueChart.data.datasets[0].label = 'Doanh thu';
                    revenueChart.data.datasets[0].data = monthlyRevenueData;
                    revenueChart.data.datasets[0].borderColor = '#36A2EB';
                    revenueChart.data.datasets[0].backgroundColor = 'rgba(54, 162, 235, 0.2)';
                    revenueChart.options.scales.y.ticks.callback = function(value) {
                        return value.toLocaleString() + ' đ';
                    };
                } else if (chartType === 'orders') {
                    // Tính số lượng đơn hàng theo tháng từ doanh thu
                    // Giả định rằng đơn hàng trung bình là 2 triệu đồng
                    const orderCountData = monthlyRevenueData.map(revenue => {
                        return Math.max(0, Math.round(revenue / 2000000));
                    });
                    
                    revenueChart.data.datasets[0].label = 'Số lượng đơn hàng';
                    revenueChart.data.datasets[0].data = orderCountData;
                    revenueChart.data.datasets[0].borderColor = '#4BC0C0';
                    revenueChart.data.datasets[0].backgroundColor = 'rgba(75, 192, 192, 0.2)';
                    revenueChart.options.scales.y.ticks.callback = function(value) {
                        return value;
                    };
                } else if (chartType === 'cancellations') {
                    // Tính tỉ lệ hủy đơn theo tháng
                    const totalOrders = pendingOrders + confirmedOrders + cancelledOrders;
                    const cancellationRate = totalOrders > 0 ? (cancelledOrders / totalOrders) * 100 : 0;
                    
                    // Tạo dữ liệu mẫu cho biểu đồ tỉ lệ hủy đơn
                    const cancellationRateData = [5, 8, 12, 7, 3, 6, 9, 4, 5, 8, 10, cancellationRate];
                    
                    revenueChart.data.datasets[0].label = 'Tỉ lệ hủy đơn (%)';
                    revenueChart.data.datasets[0].data = cancellationRateData;
                    revenueChart.data.datasets[0].borderColor = '#FF6384';
                    revenueChart.data.datasets[0].backgroundColor = 'rgba(255, 99, 132, 0.2)';
                    revenueChart.options.scales.y.ticks.callback = function(value) {
                        return value + '%';
                    };
                }
                revenueChart.update();
            } else if (chartContainer.classList.contains('status-chart')) {
                // Update order status chart based on selected tab
                if (chartType === 'orders') {
                    orderStatusChart.data.labels = ['Đang chờ', 'Đã xác nhận', 'Đã hủy'];
                    orderStatusChart.data.datasets[0].data = [pendingOrders, confirmedOrders, cancelledOrders];
                    orderStatusChart.data.datasets[0].backgroundColor = ['#FF6384', '#36A2EB', '#FFCE56'];
                } else if (chartType === 'revenue-by-payment') {
                    // Giả lập dữ liệu doanh thu theo phương thức thanh toán
                    orderStatusChart.data.labels = ['Tiền mặt', 'Chuyển khoản', 'Ví điện tử'];
                    orderStatusChart.data.datasets[0].data = [50, 30, 20];
                    orderStatusChart.data.datasets[0].backgroundColor = ['#FF9F40', '#4BC0C0', '#9966FF'];
                }
                orderStatusChart.update();
            }
        });
    });

    // Tab switching for tour list
    document.querySelectorAll('.tour-tab').forEach(tab => {
        tab.addEventListener('click', function() {
            // Remove active class from all tabs
            document.querySelectorAll('.tour-tab').forEach(t => {
                t.classList.remove('active');
            });
            
            // Add active class to clicked tab
            this.classList.add('active');
            
            // In a real app, you would load different tour data here
            // For now, we'll just show a message
            const chartType = this.getAttribute('data-type');
            if (chartType !== 'most-booked') {
                const tourTableBody = document.querySelector('.tour-table tbody');
                tourTableBody.innerHTML = `
                    <tr>
                        <td colspan="5" class="text-center">
                            Chức năng đang được phát triển. Vui lòng quay lại sau.
                        </td>
                    </tr>
                `;
            } else {
                // Reload the original data
                location.reload();
            }
        });
    });
});