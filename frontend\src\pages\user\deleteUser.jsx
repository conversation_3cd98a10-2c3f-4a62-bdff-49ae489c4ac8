import React, { useState } from "react";
import { message } from "antd";
import { useNavigate } from "react-router-dom";
import { userApi } from "../../api/userApi";

function DeleteUser() {
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleDeleteAccount = async () => {
    setLoading(true);

    try {
      const response = await userApi.deleteCurrentUser();
      message.success(response.message || "Xóa tài khoản thành công!");
      localStorage.removeItem("token");
      navigate("/login");
    } catch (error) {
      message.error(error.response?.data?.message || "Đã xảy ra lỗi khi xóa tài khoản!");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="text-center m-5">
      <h2 className="text-2xl text-[#f44336]"><PERSON><PERSON><PERSON></h2>
      <p className="mb-5 text-base text-[#555]">
        <PERSON><PERSON><PERSON> chắc chắn muốn xóa tài khoản của mình? Quá trình này không thể hoàn
        tác.
      </p>
      <button
        onClick={handleDeleteAccount}
        disabled={loading}
        className="py-2.5 px-5 text-base text-white bg-[#f44336] border-none cursor-pointer rounded hover:bg-[#d32f2f] disabled:bg-[#ccc] disabled:cursor-not-allowed transition-colors duration-200"
      >
        {loading ? "Đang xử lý..." : "Xóa tài khoản"}
      </button>
    </div>
  );
}

export default DeleteUser;