@use "../abstracts" as *;

// departure Management Styles
.departure {
    margin-left: 240px;
    padding: 2rem;
    height: 100%;
    min-width: calc(100% - 260px);
    overflow: auto;

    // Header Section
    &__header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        background: white;
        padding: 1.5rem 2rem;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border: 1px solid #e9ecef;
    }

    &__search-section {
        margin-bottom: 2rem;
        
        .departure__search-form {
            .input-group {
                max-width: 400px;
                & > button {
                    border-top-right-radius: 8px !important;
                    border-bottom-right-radius: 8px !important;
                    }
                .form-control {
                    padding: 1rem;
                    font-size: 1rem;
                    border-radius: 8px;
                    
                    &:focus {
                        border-color: #007bff;
                        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
                    }
                }
                
                .btn {
                    padding: 1rem;
                    font-size: 1.2rem;
                    background: $primary-color;
                    color: #ffffff;
                    border-radius: 8px;
                    border: 1px solid $primary-color;

                    &:hover {
                        background: transparent;
                        color: $primary-darker;
                        border-color: $primary-color;
                    }
                }

                .btn-outline-danger {
                    position: absolute;
                    right: 75px;
                    top: 50%;
                    transform: translateY(-50%);
                    background: transparent;
                    color: #ccc;
                    border: none;

                    &:hover {
                        color: #5b5b5b;
                    }
                }
            }
        }
        
        .departure__info {
            font-size: 0.9rem;
            color: #666;
        }
    }

    &__title {
        font-size: 1.8rem;
        font-weight: 600;
        color: #2c3e50;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    &__actions {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    &__btn--delete-selected {
        animation: slideIn 0.3s ease-out;
    }

    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateX(20px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    // Buttons
    &__btn {
        padding: 0.75rem 2.5rem;
        border: 1px solid transparent;
        outline: none;
        border-radius: 5px;
        font-weight: 500;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 1.2rem;
        line-height: 1.5;

        &:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        &--primary {
            background: $primary-color;
            color: white;

            &:hover {
                background: #f8f9fa;
                color: $primary-darker;
                border-color: $primary-color;
            }
        }

        &--secondary {
            background: #6c757d;
            color: white;

            &:hover {
                background: #5a6268;
                color: white;

            }
        }

        &--warning {
            border: 1px solid #ccc;

            &:hover {
                background: #ffffff;
                color: #959595;
            }
        }

        &--danger {
            border: 1px solid #e53935;
            color: #e53935;

            &:hover {
                background: #ff5252;
                color: white;
            }
        }

        &--sm {
            padding: 0.8rem 1rem;
            font-size: 0.8rem;
        }
    }

    // Table Wrapper
    &__table--wrapper {
        background: white;
        box-shadow: 0px 0px 30px 9px rgba(0,0,0,0.1);
        overflow: hidden;
        border-radius: 10px;
    }

    // Table
    &__table {
        width: 100%;
        margin: 0;
        background: white;

        th {
            background: $primary-color;
            color: white;
            padding: 2rem;
            text-align: center;
            font-weight: 600;
            font-size: 1.2rem;
        }

        td {
            padding: 1.5rem;
            border-bottom: 1px solid #e9ecef;
            vertical-align: middle;
            font-size: 1.2rem;
            text-align: center;

            &:first-child {
                font-weight: 600;
            }

            &:nth-child(4) {
                text-align: left;
            }
        }
    }

    // Empty state styling
    &__table-empty {
        text-align: center;
        color: #6c757d;
        font-style: italic;
        padding: 3rem !important;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);

        span {
            display: inline-block;
            padding: 10rem;
        }
    }

    // Badges
    &__badge {
        padding: 1rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        letter-spacing: 0.5px;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        border: none;
        cursor: default;
        transition: all 0.3s ease;

        &--success {
            background: #28a745;
            color: #fff;

            // &:hover {
            //     background: #baff3a;
            // }
        }

        &--inactive {
            background: #dc3545;
            color: #fff;

            // &:hover {
            //     background: #f45461;
            // }
        }

        &--toggle {
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            }

            &:active {
                transform: translateY(0);
            }

            &:disabled {
                cursor: not-allowed;
                opacity: 0.6;
            }
        }
    }

    // User Info Styling
    &__user-info {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        color: #495057;
        font-size: 0.875rem;
        font-weight: 500;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 0.5rem 1rem;
        border-radius: 20px;
        border: 1px solid #dee2e6;
        transition: all 0.3s ease;

        &:hover {
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        i {
            color: #6c757d;
            font-size: 0.875rem;
        }
    }

    // Form Styles
    &__form {
        &--delete {
            display: inline-block;
            margin-left: 0.5rem;
        }
    }

    // Animation for status toggle
    .status-changing {
        animation: pulse 0.5s ease-in-out;
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.05);
        }
        100% {
            transform: scale(1);
        }
    }


    // Items per page form
    &__items-per-page-form {
        .form-label {
            font-size: 0.9rem;
            font-weight: 500;
            white-space: nowrap;
        }
        
        .form-select {
            border-radius: 8px;
            border: 1px solid #ced4da;
            min-width: 70px;
            
            &:focus {
                border-color: #007bff;
                box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            }
        }
        
        .d-inline-flex {
            align-items: center;
            gap: 0.5rem;
        }
    }

    // Pagination
    &__pagination {
        margin-top: 2rem;
        
        .pagination {
            .page-item {
                margin: 0 2px;
                
                .page-link {
                    border-radius: 6px;
                    border: 1px solid #dee2e6;
                    color: $primary-color;
                    padding: 0.5rem 0.75rem;
                    font-weight: 500;
                    transition: all 0.2s ease;
                    min-width: 40px;
                    text-align: center;
                    background-color: #fff;
                    
                    &:hover {
                        background-color: #e9ecef;
                        border-color: #adb5bd;
                        color: #0056b3;
                        text-decoration: none;
                    }
                    
                    &:focus {
                        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
                        z-index: 3;
                    }
                }
                
                &.active .page-link {
                    background-color: #007bff;
                    border-color: #007bff;
                    color: white;
                    font-weight: 600;
                    
                    &:hover {
                        background-color: #007bff;
                        border-color: #007bff;
                        color: white;
                    }
                }
                
                &.disabled .page-link {
                    color: #6c757d;
                    background-color: #fff;
                    border-color: #dee2e6;
                    cursor: not-allowed;
                    opacity: 0.65;
                    
                    &:hover {
                        background-color: #fff;
                        border-color: #dee2e6;
                        color: #6c757d;
                    }
                }
            }
            
            // First and Last page buttons (« and »)
            .page-item:first-child .page-link,
            .page-item:last-child .page-link {
                font-weight: bold;
                font-size: 1.1rem;
            }
        }
        
        &-info {
            margin-top: 1.5rem;
            
            small {
                font-size: 0.875rem;
                color: #6c757d;
                display: block;
                margin-bottom: 0.5rem;
            }
        }
    }
    
    // Quick Jump
    &__quick-jump {
        .form-control {
            border-radius: 8px;
            border: 1px solid #ced4da;
            text-align: center;
            font-weight: 500;
            
            &:focus {
                border-color: #007bff;
                box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            }
        }
        
        .btn {
            border-radius: 8px;
            font-weight: 500;
            
            &:hover {
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
            }
        }
        
        small {
            font-weight: 500;
            color: #495057;
        }
    }

    // Responsive Design
    @media (max-width: 768px) {
        padding: 1rem;

        &__header {
            flex-direction: column;
            gap: 1rem;
            text-align: center;
        }

        &__title {
            font-size: 1.5rem;
        }

        &__table--wrapper {
            overflow-x: auto;
        }

        &__table {
            min-width: 600px;

            thead th,
            tbody td {
                padding: 0.75rem;
                font-size: 0.8rem;
            }
        }

        &__btn {
            padding: 0.5rem 1rem;
            font-size: 0.8rem;

            &--sm {
                padding: 0.4rem 0.8rem;
                font-size: 0.75rem;
            }
        }
    }

    // Checkbox styling
    .departure-checkbox,
    #selectAll {
        transform: scale(1.2);
        margin: 0;
        cursor: pointer;
        border: 1px solid #ccc;
    }

    // Checkbox column
    th:first-child,
    td:first-child {
        width: 50px;
        text-align: center;
        padding: 1rem 0.5rem !important;
    }

}

// departure Add/Edit Form Styles
.departure-form {
    background: white;
    border-radius: 12px;
    box-shadow: 0px 0px 30px 9px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin: 2rem auto;

    &__header {
        background: $primary-color;
        color: white;
        padding: 2rem;
        text-align: left;

        h3 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
        }
    }

    &__body {
        padding: 2rem;
    }

    .form-label {
        font-weight: 600;
        color: #2c3e50;
        margin: 1rem 0;
        display: block;
    }

    .form-control,
    .form-select {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 1rem;
        font-size: 1.2rem;
        transition: all 0.3s ease;

        &:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            outline: none;
        }
    }

    .btn {
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-size: 1.2rem;
        font-weight: 500;
        text-decoration: none;
        transition: all 0.3s ease;

        &:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        &-primary {
            background: $primary-color;
            border: 1px solid $primary-color;

            &:hover {
                color: $primary-darker;
                background: transparent;
            }
        }

        &-secondary {
            background: #6c757d;
            border: none;

            &:hover {
                background: #5a6268;
            }
        }
    }
}

