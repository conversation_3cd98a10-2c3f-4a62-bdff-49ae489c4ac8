import React, { useState } from "react";
import { message, Input, Button } from "antd";
import { useNavigate } from "react-router-dom";
import { userApi } from "../../api/userApi";

function ChangePassword() {
  const [oldPassword, setOldPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmNewPassword, setConfirmNewPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setErrorMessage("");

    if (!oldPassword || !newPassword || !confirmNewPassword) {
      const error = "Vui lòng nhập đầy đủ thông tin!";
      message.error(error);
      setErrorMessage(error);
      return;
    }

    if (newPassword !== confirmNewPassword) {
      const error = "<PERSON>ác nhận mật khẩu không khớp!";
      message.error(error);
      setErrorMessage(error);
      return;
    }

    setLoading(true);

    try {
      const response = await userApi.changePassword({
        oldPassword,
        newPassword,
        confirmNewPassword,
      });

      message.success("Đổi mật khẩu thành công!");
      navigate("/user");
    } catch (error) {
      const errorMsg = error.response?.data || "Đã xảy ra lỗi khi thay đổi mật khẩu!";
      message.error(errorMsg);
      setErrorMessage(errorMsg);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-[400px] my-[50px] mx-auto p-5 bg-white rounded-lg shadow-md">
      <h2 className="text-center text-2xl mb-5 text-[#333]">Đổi Mật Khẩu</h2>
      
      <form onSubmit={handleSubmit} className="flex flex-col w-full">
        <div className="mb-4 w-full flex-1">
          <label 
            htmlFor="oldPassword" 
            className="block text-sm text-[#555] mb-1"
          >
            Mật khẩu cũ
          </label>
          <input
            type="password"
            id="oldPassword"
            value={oldPassword}
            onChange={(e) => setOldPassword(e.target.value)}
            placeholder="Nhập mật khẩu cũ"
            className="flex-1 w-full p-2.5 text-base border border-[#ccc] rounded outline-none transition-colors duration-300 focus:border-[#4e7ed9]"
          />
        </div>

        <div className="mb-4 w-full flex-1">
          <label 
            htmlFor="newPassword" 
            className="block text-sm text-[#555] mb-1"
          >
            Mật khẩu mới
          </label>
          <input
            type="password"
            id="newPassword"
            value={newPassword}
            onChange={(e) => setNewPassword(e.target.value)}
            placeholder="Nhập mật khẩu mới"
            className="flex-1 w-full p-2.5 text-base border border-[#ccc] rounded outline-none transition-colors duration-300 focus:border-[#4e7ed9]"
          />
        </div>

        <div className="mb-4 w-full flex-1">
          <label 
            htmlFor="confirmNewPassword" 
            className="block text-sm text-[#555] mb-1"
          >
            Xác nhận mật khẩu mới
          </label>
          <input
            type="password"
            id="confirmNewPassword"
            value={confirmNewPassword}
            onChange={(e) => setConfirmNewPassword(e.target.value)}
            placeholder="Nhập lại mật khẩu mới"
            className="flex-1 w-full p-2.5 text-base border border-[#ccc] rounded outline-none transition-colors duration-300 focus:border-[#4e7ed9]"
          />
        </div>

        <button
          type="submit"
          disabled={loading}
          className="py-2.5 px-5 bg-[#4e7ed9] text-white text-base border-none rounded cursor-pointer transition-colors duration-300 hover:bg-[#3d69b2] disabled:bg-[#bbb] disabled:cursor-not-allowed"
        >
          {loading ? "Đang xử lý..." : "Lưu"}
        </button>

        {errorMessage && (
          <div className="text-red-500 text-sm mt-2.5">
            {errorMessage}
          </div>
        )}
      </form>
    </div>
  );
}

export default ChangePassword;