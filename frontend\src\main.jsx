import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { createBrowserRouter, RouterProvider } from 'react-router-dom'
import './index.css'

// Auth Provider
import { AuthProvider } from './hooks/useAuth'

// Components
import { Login } from './pages/login'
import Register from './pages/register'
import Home from './pages/home'
import User from './pages/user'
import { ChangePassword } from './pages/user'
import { DeleteUser } from './pages/user'
import Tour from './pages/tours'
import TourDetails from './pages/tourDetails'
import Order from './pages/order'

//Layout
import Layout from './layouts/main-layout/MainLayout'

// Router configuration
const router = createBrowserRouter([
  {
    path: "",
    element: <Layout />,
    children: [
      {
        path: "/",
        element: <Home />,
      },
      {
        path: "/login",
        element: <Login />,
      },
      {
        path: "/register",
        element: <Register />,
      },
      {
          path: "/tours/:slug",
          element: <Tour />,
        },
        {
          path: "/user",
          element: <User />,
        },
        {
          path: "/user/change-password",
          element: <ChangePassword />,
        },
        {
          path: "/user/delete-user",
          element: <DeleteUser />,
        },
        {
          path: "/tour-details/:slug",
          element: <TourDetails />,
        },
        {
          path: '/order',
          element: <Order />
        },
      {
        path: "*",
        element: (
          <div className="min-h-screen flex items-center justify-center bg-gray-50">
            <div className="text-center">
              <div className="text-6xl text-gray-400 mb-4">404</div>
              <h1 className="text-2xl font-bold text-gray-800 mb-2">
                Trang không tồn tại
              </h1>
              <p className="text-gray-600 mb-4">
                Trang bạn đang tìm kiếm không được tìm thấy.
              </p>
              <a
                href="/"
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition duration-300 inline-block"
              >
                Về trang chủ
              </a>
            </div>
          </div>
        ),
      },
    ],
  },
])

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <AuthProvider>
      <RouterProvider router={router} />
    </AuthProvider>
  </StrictMode>,
)