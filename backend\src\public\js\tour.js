// Tour Management JavaScript
document.addEventListener("DOMContentLoaded", function () {
    // Setup delete handlers
    setupDeleteHandlers();
    
    // Setup edit form handler
    setupEditFormHandler();
    
    // Setup add form handler
    setupAddFormHandler();
    
    // Setup search functionality
    setupSearchHandler();
    
    // Setup pagination
    setupPaginationHandler();
    
    // Setup tour code validation
    setupTourCodeValidation();
    
    // Initialize itinerary delete buttons
    initializeItineraryDeleteButtons();
    
    // Setup number formatting for inputs
    setupNumberFormatting();
    
    // Setup multiple images preview
    setupMultipleImagesPreview();
    
    // Initialize checkbox functionality
    initializeCheckboxes();
    updateDeleteButtonState();
    
    // Update itinerary delete buttons on page load (for edit forms with pre-filled data)
    updateItineraryDeleteButtons();
});

function setupDeleteHandlers() {
    const deleteButtons = document.querySelectorAll(".tour__form--delete");

    deleteButtons.forEach((form, index) => {
        const button = form.querySelector('button[type="button"]');
        if (button) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const tourTitle = this.getAttribute('data-tour-title') || 'tour này';
                const row = this.closest('tr');
                showDeleteModal(tourTitle, form, row);
            });
        }
    });
}

// Show delete confirmation modal
function showDeleteModal(tourTitle, form, row) {
    // Set tour title in modal
    document.getElementById('tourNameToDelete').textContent = tourTitle;
    
    // Get modal instance with proper backdrop configuration
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'), {
        backdrop: true, // Enable backdrop
        keyboard: true, // Allow ESC key to close
        focus: true     // Focus on modal when shown
    });
    
    // Show modal
    deleteModal.show();
    
    // Handle confirm delete button
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    
    // Remove existing listeners to avoid duplicates
    const newConfirmBtn = confirmDeleteBtn.cloneNode(true);
    confirmDeleteBtn.parentNode.replaceChild(newConfirmBtn, confirmDeleteBtn);
    
    // Add new listener for confirm delete
    newConfirmBtn.addEventListener('click', function() {
        // Add deleting class to row
        row.classList.add('tour-row--deleting');
        
        // Disable button and show loading
        newConfirmBtn.disabled = true;
        newConfirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Đang xóa...';
        
        // Get tour ID from form action
        const formAction = form.getAttribute('action');
        const tourId = formAction.split('/').pop();
        
        // Use AJAX to delete instead of form submission
        fetch(formAction, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            // Hide modal
            deleteModal.hide();
            
            if (data.success) {
                // Show success toast
                showToastNotification(data.message || 'Xóa tour thành công!', 'success');
                
                // Reload page after short delay to get updated data from server
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                // Show error toast
                showToastNotification(data.message || 'Có lỗi xảy ra khi xóa tour', 'error');
                
                // Remove deleting class
                row.classList.remove('tour-row--deleting');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            
            // Hide modal
            deleteModal.hide();
            
            // Show error toast
            showToastNotification('Có lỗi xảy ra khi xóa tour', 'error');
            
            // Remove deleting class
            row.classList.remove('tour-row--deleting');
        })
        .finally(() => {
            // Re-enable button
            newConfirmBtn.disabled = false;
            newConfirmBtn.innerHTML = '<i class="fas fa-trash me-2"></i>Xóa';
        });
    });
    
    // Ensure cancel buttons work properly
    const cancelButtons = document.querySelectorAll('#deleteModal [data-bs-dismiss="modal"]');
    cancelButtons.forEach(button => {
        button.addEventListener('click', function() {
            deleteModal.hide();
        });
    });
    
    // Handle backdrop click to close modal
    document.getElementById('deleteModal').addEventListener('click', function(event) {
        if (event.target === this) {
            deleteModal.hide();
        }
    });
    
    // Handle ESC key to close modal
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape' && deleteModal._isShown) {
            deleteModal.hide();
        }
    });
}

// Toggle Status Function
function toggleStatus(button) {
    const tourId = button.getAttribute("data-id");
    const currentStatus = button.getAttribute("data-status") === "true";

    // Set fixed width to prevent table jumping
    if (!button.style.minWidth) {
        button.style.minWidth = button.offsetWidth + "px";
    }

    // Disable button during request
    button.disabled = true;
    button.style.opacity = "0.6";

    fetch(`/tour/toggle-status/${tourId}`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "X-CSRF-Token": window.csrfToken
        },
        body: JSON.stringify({
            _csrf: window.csrfToken
        })
    })
        .then((response) => response.json())
        .then((data) => {
            if (data.success) {
                const newStatus = !currentStatus;
                button.setAttribute("data-status", newStatus);
                
                // Update button appearance
                button.className = `btn btn-sm ${newStatus ? 'btn-success' : 'btn-secondary'} tour__status-toggle`;
                button.innerHTML = `${newStatus ? 'Hoạt động' : 'Tạm dừng'}`;
                
                showToastNotification(data.message, 'success');
            } else {
                showToastNotification(data.message || 'Có lỗi xảy ra khi cập nhật trạng thái', 'error');
            }
        })
        .catch((error) => {
            console.error("Error:", error);
            showToastNotification('Có lỗi xảy ra khi cập nhật trạng thái', 'error');
        })
        .finally(() => {
            // Re-enable button
            button.disabled = false;
            button.style.opacity = "1";
        });
}

// Toggle Highlight Function
function toggleHighlight(button) {
    const tourId = button.getAttribute("data-id");
    const currentHighlight = button.getAttribute("data-highlight") === "true";

    // Set fixed width to prevent table jumping
    if (!button.style.minWidth) {
        button.style.minWidth = button.offsetWidth + "px";
    }

    // Disable button during request
    button.disabled = true;
    button.style.opacity = "0.6";

    fetch(`/tour/toggle-highlight/${tourId}`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "X-CSRF-Token": window.csrfToken
        },
        body: JSON.stringify({
            _csrf: window.csrfToken
        })
    })
        .then((response) => response.json())
        .then((data) => {
            if (data.success) {
                const newHighlight = !currentHighlight;
                button.setAttribute("data-highlight", newHighlight);
                
                // Update button appearance
                button.className = `btn btn-sm ${newHighlight ? 'btn-warning' : 'btn-outline-warning'} tour__highlight-toggle`;
                button.innerHTML = `${newHighlight ? 'Nổi bật' : 'Bình thường'}`;
                
                showToastNotification(data.message, 'success');
            } else {
                showToastNotification(data.message || 'Có lỗi xảy ra khi cập nhật nổi bật', 'error');
            }
        })
        .catch((error) => {
            console.error("Error:", error);
            showToastNotification('Có lỗi xảy ra khi cập nhật nổi bật', 'error');
        })
        .finally(() => {
            // Re-enable button
            button.disabled = false;
            button.style.opacity = "1";
        });
}

// Setup edit form handler
function setupEditFormHandler() {
    const editForm = document.querySelector('form[action*="/tour/edit/"]');
    if (!editForm) return;

    editForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const submitBtn = this.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Đang cập nhật...';
        }
        
        // Prepare form data with cleaned number values
        const formData = prepareFormDataForSubmission(editForm);
        
        // Submit the form
        fetch(editForm.action, {
            method: 'POST',
            body: formData,
            redirect: 'manual' // Handle redirects manually
        })
        .then(response => {
            if (response.type === 'opaqueredirect' || response.status === 302) {
                // Success redirect - form was submitted successfully
                showToastNotification('Cập nhật tour thành công!', 'success');
                setTimeout(() => {
                    window.location.href = '/tour';
                }, 1500);
                return;
            }
            
            if (response.ok) {
                return response.text();
            }
            throw new Error('Network response was not ok');
        })
        .then(html => {
            if (!html) return; // Already handled redirect above
            
            // Parse the response to check for error messages (redirect to form with errors)
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            
            // Check for error messages
            const errorMessage = doc.querySelector('.modal-notify--error .modal-notify__message, .alert-danger');
            if (errorMessage) {
                showToastNotification(errorMessage.textContent, 'error');
            } else {
                showToastNotification('Có lỗi xảy ra, vui lòng thử lại!', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToastNotification('Có lỗi xảy ra khi cập nhật tour!', 'error');
        })
        .finally(() => {
            // Restore button state
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-save me-2"></i>Lưu thay đổi';
            }
        });
    });
}

// Setup add form handler
function setupAddFormHandler() {
    const addForm = document.querySelector('form[action="/tour/add"]');
    if (!addForm) return;

    addForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const submitBtn = this.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Đang lưu...';
        }
        
        // Prepare form data with cleaned number values
        const formData = prepareFormDataForSubmission(addForm);
        
        // Submit the form
        fetch(addForm.action, {
            method: 'POST',
            body: formData,
            redirect: 'manual' // Handle redirects manually
        })
        .then(response => {
            if (response.type === 'opaqueredirect' || response.status === 302) {
                // Success redirect - form was submitted successfully
                showToastNotification('Thêm tour thành công!', 'success');
                setTimeout(() => {
                    window.location.href = '/tour';
                }, 1500);
                return;
            }
            
            if (response.ok) {
                return response.text();
            }
            throw new Error('Network response was not ok');
        })
        .then(html => {
            if (!html) return; // Already handled redirect above
            
            // Parse the response to check for error messages (redirect to form with errors)
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            
            // Check for error messages
            const errorMessage = doc.querySelector('.modal-notify--error .modal-notify__message, .alert-danger');
            if (errorMessage) {
                showToastNotification(errorMessage.textContent, 'error');
            } else {
                showToastNotification('Có lỗi xảy ra, vui lòng thử lại!', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToastNotification('Có lỗi xảy ra khi thêm tour!', 'error');
        })
        .finally(() => {
            // Restore button state
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-plus me-2"></i>Thêm tour';
            }
        });
    });
}

// Setup search functionality
function setupSearchHandler() {
    const searchForm = document.querySelector('.tour__search-form');
    const searchInput = searchForm?.querySelector('input[name="q"]');
    
    if (!searchForm || !searchInput) return;
    
    // Auto-submit search after user stops typing
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            if (this.value.length >= 2 || this.value.length === 0) {
                searchForm.submit();
            }
        }, 500);
    });
}

// Setup pagination functionality
function setupPaginationHandler() {
    const paginationLinks = document.querySelectorAll('.tour__pagination .page-link');
    
    paginationLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Add loading state
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        });
    });
}

// Setup tour code validation
function setupTourCodeValidation() {
    const codeInput = document.getElementById('code');
    const titleInput = document.getElementById('title');
    const generateCodeBtn = document.getElementById('generateCodeBtn');
    const tourForm = document.getElementById('tourForm');
    
    if (!codeInput) return;
    
    // Lưu mã tour ban đầu để so sánh
    const originalCode = codeInput.value;
    
    // Kiểm tra mã tour khi nhập
    let codeCheckTimeout;
    codeInput.addEventListener('input', function() {
        clearTimeout(codeCheckTimeout);
        const code = this.value.trim();
        
        // Không kiểm tra nếu mã không thay đổi (trong trường hợp edit)
        if (code === originalCode) {
            clearCodeValidation();
            return;
        }
        
        if (code.length >= 3) {
            codeCheckTimeout = setTimeout(() => {
                checkTourCode(code);
            }, 500);
        } else {
            clearCodeValidation();
        }
    });
    
    // Tạo mã tự động
    if (generateCodeBtn) {
        generateCodeBtn.addEventListener('click', function() {
            const title = titleInput ? titleInput.value.trim() : '';
            if (!title) {
                showCodeMessage('Vui lòng nhập tên tour trước', 'error');
                return;
            }
            
            generateTourCode(title);
        });
    }
    
    // Kiểm tra mã tour trước khi submit form
    if (tourForm) {
        tourForm.addEventListener('submit', function(e) {
            const code = codeInput.value.trim();
            const messageDiv = document.getElementById('codeMessage');
            
            if (messageDiv && messageDiv.classList.contains('text-danger') && messageDiv.style.display !== 'none') {
                e.preventDefault();
                showCodeMessage('Vui lòng sửa mã tour trước khi lưu', 'error');
                codeInput.focus();
                return false;
            }
        });
    }
}

// Kiểm tra mã tour
function checkTourCode(code) {
    const codeInput = document.getElementById('code');
    showCodeMessage('Đang kiểm tra mã tour...', 'info');
    
    fetch('/tour/check-code', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ code })
    })
    .then(response => response.json())
    .then(data => {
        if (data.exists) {
            showCodeMessage(data.message, 'error');
            codeInput.classList.add('is-invalid');
            codeInput.classList.remove('is-valid');
        } else {
            showCodeMessage('Mã tour có thể sử dụng', 'success');
            codeInput.classList.add('is-valid');
            codeInput.classList.remove('is-invalid');
        }
    })
    .catch(error => {
        console.error('Error checking tour code:', error);
        showCodeMessage('Có lỗi xảy ra khi kiểm tra mã tour', 'error');
        codeInput.classList.remove('is-valid', 'is-invalid');
    });
}

// Tạo mã tour tự động
function generateTourCode(title) {
    const generateBtn = document.getElementById('generateCodeBtn');
    const originalIcon = generateBtn.innerHTML;
    
    // Hiển thị loading
    generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    generateBtn.disabled = true;
    
    fetch('/tour/generate-code', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ title })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const codeInput = document.getElementById('code');
            codeInput.value = data.code;
            codeInput.classList.add('is-valid');
            codeInput.classList.remove('is-invalid');
            showCodeMessage('Mã tour đã được tạo tự động', 'success');
            
            // Hiệu ứng highlight
            codeInput.style.backgroundColor = '#d4edda';
            setTimeout(() => {
                codeInput.style.backgroundColor = '';
            }, 2000);
        } else {
            showCodeMessage(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error generating tour code:', error);
        showCodeMessage('Có lỗi xảy ra khi tạo mã tour', 'error');
    })
    .finally(() => {
        // Khôi phục button
        generateBtn.innerHTML = originalIcon;
        generateBtn.disabled = false;
    });
}

// Hiển thị thông báo mã tour
function showCodeMessage(message, type) {
    const messageDiv = document.getElementById('codeMessage');
    if (!messageDiv) return;
    
    let className = 'form-text ';
    switch(type) {
        case 'error':
            className += 'text-danger';
            break;
        case 'success':
            className += 'text-success';
            break;
        case 'info':
            className += 'text-info';
            break;
        default:
            className += 'text-muted';
    }
    
    messageDiv.className = className;
    messageDiv.textContent = message;
    messageDiv.style.display = 'block';
    
    // Hiệu ứng fade in
    messageDiv.style.opacity = '0';
    setTimeout(() => {
        messageDiv.style.opacity = '1';
    }, 50);
}

// Xóa thông báo mã tour
function clearCodeValidation() {
    const messageDiv = document.getElementById('codeMessage');
    const codeInput = document.getElementById('code');
    
    if (messageDiv) {
        messageDiv.style.display = 'none';
    }
    
    if (codeInput) {
        codeInput.classList.remove('is-valid', 'is-invalid');
    }
}

// CSS Styles for row animation and notification container
const styles = `
    .notification-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        max-width: 400px;
        pointer-events: none;
    }
    
    .notification-container .modal-notify {
        position: relative;
        margin-bottom: 10px;
        transform: translateX(100%);
        transition: all 0.3s ease;
        pointer-events: auto;
    }
    
    .notification-container .modal-notify--active {
        transform: translateX(0);
    }
    
    .tour-row--deleting {
        background: #ffe6e6 !important;
        animation: rowFadeOut 0.5s ease-in-out;
    }
    
    @keyframes rowFadeOut {
        0% { opacity: 1; transform: scale(1); }
        50% { opacity: 0.5; transform: scale(0.98); }
        100% { opacity: 1; transform: scale(1); }
    }
`;

// Inject styles
const styleSheet = document.createElement("style");
styleSheet.textContent = styles;
document.head.appendChild(styleSheet);

// Auto-hide notifications on page load
document.addEventListener('DOMContentLoaded', function() {
    const notifications = document.querySelectorAll('.modal-notify--active');
    notifications.forEach(notification => {
        setTimeout(() => {
            hideNotification(notification);
        }, 5000);
    });
});

// Advanced Filter JavaScript
// Toggle advanced filter visibility
function toggleAdvancedFilter() {
    const content = document.getElementById('advancedFilterContent');
    const icon = document.getElementById('filterToggleIcon');
    const toggleBtn = icon.closest('button');
    
    if (content.style.display === 'none' || content.style.display === '') {
        content.style.display = 'block';
        content.classList.add('show');
        icon.classList.remove('fa-chevron-down');
        icon.classList.add('fa-chevron-up');
        toggleBtn.classList.add('expanded');
        localStorage.setItem('filterExpanded', 'true');
    } else {
        content.classList.remove('show');
        setTimeout(() => {
            content.style.display = 'none';
        }, 300); 
        icon.classList.remove('fa-chevron-up');
        icon.classList.add('fa-chevron-down');
        toggleBtn.classList.remove('expanded');
        localStorage.setItem('filterExpanded', 'false');
    }
}

// Set quick filter values and submit
function setQuickFilter(type) {
    const form = document.getElementById('tourFilterForm');
    
    // Clear existing filters first
    clearAllFilters(false);
    
    switch(type) {
        case 'expiring':
            // Tours expiring within 30 days
            const expiringDate = new Date();
            expiringDate.setDate(expiringDate.getDate() + 30);
            form.querySelector('[name="startDate"]').value = expiringDate.toISOString().split('T')[0];
            form.querySelector('[name="status"]').value = 'true';
            break;
            
        case 'expired':
            // Tours that have already ended
            const today = new Date().toISOString().split('T')[0];
            form.querySelector('[name="endDate"]').value = today;
            form.querySelector('[name="expired"]').value = 'true';
            break;
            
        case 'featured':
            // Featured tours
            form.querySelector('[name="highlight"]').value = 'true';
            break;
            
        case 'active':
            // Active tours
            form.querySelector('[name="status"]').value = 'true';
            break;
    }
    
    form.submit();
}

// Clear all filters
function clearAllFilters(submit = true) {
    const form = document.getElementById('tourFilterForm');
    const inputs = form.querySelectorAll('input, select');
    
    inputs.forEach(input => {
        if (input.type === 'text' || input.type === 'date') {
            input.value = '';
        } else if (input.tagName === 'SELECT') {
            input.selectedIndex = 0;
        }
    });
    
    if (submit) {
        form.submit();
    }
}

// Apply filters with animation
function applyFilters() {
    const form = document.getElementById('tourFilterForm');
    const submitBtn = form.querySelector('[name="quickFilter"]');
    
    // Add loading animation
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Đang áp dụng...';
    submitBtn.disabled = true;
    
    setTimeout(() => {
        form.submit();
    }, 500);
}

// Initialize filter state on page load
document.addEventListener('DOMContentLoaded', function() {
    // Restore filter visibility state
    const filterExpanded = localStorage.getItem('filterExpanded');
    const content = document.getElementById('advancedFilterContent');
    const icon = document.getElementById('filterToggleIcon');
    const toggleBtn = icon.closest('button');
    
    if (filterExpanded === null || filterExpanded === 'true') {
        content.style.display = 'block';
        content.classList.add('show');
        icon.classList.remove('fa-chevron-down');
        icon.classList.add('fa-chevron-up');
        toggleBtn.classList.add('expanded');
    } else {
        content.style.display = 'none';
        content.classList.remove('show');
        icon.classList.remove('fa-chevron-up');
        icon.classList.add('fa-chevron-down');
        toggleBtn.classList.remove('expanded');
    }

    // Add change listeners to all filter elements
    const form = document.getElementById('tourFilterForm');
    const filterElements = form.querySelectorAll('select, input[type="date"]');
    
    filterElements.forEach(element => {
        element.addEventListener('change', function() {
            // Add visual feedback
            this.style.borderColor = '#007bff';
            setTimeout(() => {
                this.style.borderColor = '';
            }, 1000);
        });
    });

    // Add filter count indicator
    updateFilterCount();
});

// Update filter count
function updateFilterCount() {
    const form = document.getElementById('tourFilterForm');
    const inputs = form.querySelectorAll('input[value], select');
    let activeFilters = 0;

    inputs.forEach(input => {
        if (input.value && input.value !== '' && input.name !== 'q') {
            activeFilters++;
        }
    });

    // Add filter count badge to header
    const header = document.querySelector('.card-header h6');
    let badge = header.querySelector('.badge');
    
    if (activeFilters > 0) {
        if (!badge) {
            badge = document.createElement('span');
            badge.className = 'badge bg-primary ms-2';
            header.appendChild(badge);
        }
        badge.textContent = activeFilters;
    } else if (badge) {
        badge.remove();
    }
}

// Auto-submit search after typing delay
let searchTimeout;
document.querySelector('input[name="q"]').addEventListener('input', function() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        if (this.value.length >= 2 || this.value.length === 0) {
            document.getElementById('tourFilterForm').submit();
        }
    }, 1000);
});

// Tour Form Dynamic Functions
// Add/Remove Itinerary Functions
function addItinerary() {
    const container = document.getElementById('itineraryContainer');
    const items = container.querySelectorAll('.itinerary-item');
    const newIndex = items.length;
    const newDay = newIndex + 1;
    
    const newItem = document.createElement('div');
    newItem.className = 'itinerary-item border rounded p-3 mb-3 position-relative';
    newItem.innerHTML = `
        <button type="button" class="btn btn-sm btn-outline-danger itinerary-delete-btn" onclick="removeItinerary(this)" style="position: absolute; top: 10px; right: 10px; width:24px; height: 23px; border: 1px solid #3498db; padding:0; border-radius: 6px; ">
            <i class="fas fa-trash"></i>
        </button>
        
        <div class="row">
            <div class="col-md-3">
                <div class="mb-3">
                    <label class="form-label">Ngày <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" name="itinerary[${newIndex}][day]" value="${newDay}" min="1" required disabled>
                </div>
            </div>
            <div class="col-md-9">
                <div class="mb-3">
                    <label class="form-label">Tiêu đề <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" name="itinerary[${newIndex}][title]" placeholder="Ví dụ: Khởi hành từ Hà Nội" required>
                </div>
            </div>
        </div>
        <div class="mb-3">
            <label class="form-label">Thông tin chi tiết <span class="text-danger">*</span></label>
            <textarea class="form-control" name="itinerary[${newIndex}][details]" rows="4" placeholder="Nhập thông tin chi tiết cho ngày này..." required></textarea>
        </div>
    `;
    
    container.appendChild(newItem);
    reindexItinerary();
    updateItineraryDeleteButtons();
}

function removeItinerary(button) {
    const container = document.getElementById('itineraryContainer');
    const item = button.closest('.itinerary-item');
    
    // Don't allow removal if only one item remains
    if (container.children.length <= 1) return;
    
    // Remove with animation
    item.style.opacity = '0.5';
    item.style.transform = 'scale(0.95)';
    
    setTimeout(() => {
        item.remove();
        reindexItinerary();
        updateItineraryDeleteButtons();
    }, 200);
}

function reindexItinerary() {
    const container = document.getElementById('itineraryContainer');
    const items = container.querySelectorAll('.itinerary-item');
    
    items.forEach((item, index) => {
        // Update form fields with proper names and values
        const dayInput = item.querySelector('input[name*="[day]"]');
        const titleInput = item.querySelector('input[name*="[title]"]');
        const detailsTextarea = item.querySelector('textarea[name*="[details]"]');
        
        if (dayInput) {
            dayInput.name = `itinerary[${index}][day]`;
            dayInput.value = index + 1;
        }
        if (titleInput) titleInput.name = `itinerary[${index}][title]`;
        if (detailsTextarea) detailsTextarea.name = `itinerary[${index}][details]`;
    });
}

// Update delete button visibility for itinerary items
function updateItineraryDeleteButtons() {
    const container = document.getElementById('itineraryContainer');
    if (!container) return;
    
    const items = container.querySelectorAll('.itinerary-item');
    const itemCount = items.length;
    
    items.forEach((item) => {
        const deleteBtn = item.querySelector('.itinerary-delete-btn');
        if (deleteBtn) {
            if (itemCount > 1) {
                deleteBtn.style.display = 'inline-block';
                deleteBtn.style.visibility = 'visible';
            } else {
                deleteBtn.style.display = 'none';
                deleteBtn.style.visibility = 'hidden';
            }
        }
    });
}

// Initialize itinerary delete buttons on page load
function initializeItineraryDeleteButtons() {
    updateItineraryDeleteButtons();
}

// Dynamic Price Block Management
function addPriceBlock() {
    const container = document.getElementById('priceContainer');
    const blocks = container.querySelectorAll('.price-block');
    const newIndex = blocks.length;
    
    const newBlock = document.createElement('div');
    newBlock.className = 'price-block border rounded p-4 mb-4';
    newBlock.innerHTML = `
        <div class="price-block-header">
            <h5 class="price-block-title">Ngày ${newIndex + 1}</h5>
            <button type="button" class="btn btn-sm btn-outline-danger price-block-delete" onclick="removePriceBlock(this)">
                <i class="fas fa-trash"></i>
            </button>
        </div>
        
        <div class="row">
            <div class="col-md-3">
                <div class="mb-3">
                    <label class="form-label">Giá người lớn <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" name="tourDetails[${newIndex}][adultPrice]" placeholder="0 đ" required>
                </div>
            </div>
            <div class="col-md-3">
                <div class="mb-3">
                    <label class="form-label">Giá trẻ em</label>
                    <input type="text" class="form-control" name="tourDetails[${newIndex}][childrenPrice]" placeholder="0 đ">
                </div>
            </div>
            <div class="col-md-3">
                <div class="mb-3">
                    <label class="form-label">Giá trẻ nhỏ</label>
                    <input type="text" class="form-control" name="tourDetails[${newIndex}][childPrice]" placeholder="0 đ">
                </div>
            </div>
            <div class="col-md-3">
                <div class="mb-3">
                    <label class="form-label">Giá trẻ sơ sinh</label>
                    <input type="text" class="form-control" name="tourDetails[${newIndex}][babyPrice]" placeholder="0 đ">
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-3">
                <div class="mb-3">
                    <label class="form-label">Phụ thu phòng đơn</label>
                    <input type="text" class="form-control" name="tourDetails[${newIndex}][singleRoomSupplementPrice]" placeholder="0 đ">
                </div>
            </div>
            <div class="col-md-3">
                <div class="mb-3">
                    <label class="form-label">Giảm giá (%)</label>
                    <input type="text" class="form-control" name="tourDetails[${newIndex}][discount]" placeholder="0%">
                </div>
            </div>
            <div class="col-md-3">
                <div class="mb-3">
                    <label class="form-label">Stock <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" name="tourDetails[${newIndex}][stock]" placeholder="0" required>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">Ngày đi <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" name="tourDetails[${newIndex}][dayStart]" required min="${new Date().toISOString().split('T')[0]}">
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">Ngày về <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" name="tourDetails[${newIndex}][dayReturn]" required min="${new Date().toISOString().split('T')[0]}">
                </div>
            </div>
        </div>
    `;
    
    container.appendChild(newBlock);
    
    // Apply formatting to the new inputs
    const priceInputs = newBlock.querySelectorAll('input[name*="Price"], input[name*="price"]');
    priceInputs.forEach(input => formatPriceInput(input));
    
    const discountInput = newBlock.querySelector('input[name*="discount"]');
    if (discountInput) formatDiscountInput(discountInput);
    
    const stockInput = newBlock.querySelector('input[name*="stock"]');
    if (stockInput) formatStockInput(stockInput);
    
    updatePriceBlockButtons();
}

function removePriceBlock(button) {
    const block = button.closest('.price-block');
    const container = document.getElementById('priceContainer');
    
    if (container.children.length > 1) {
        // Thêm class animation trước khi xóa
        block.style.opacity = '0';
        block.style.transform = 'translateX(-20px)';
        block.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        
        setTimeout(() => {
            block.remove();
            updatePriceBlockIndexes();
            updatePriceBlockButtons();
        }, 300);
    } else {
        // Hiển thị thông báo nếu cố gắng xóa block cuối cùng
        alert('Không thể xóa block cuối cùng!');
    }
}

function updatePriceBlockIndexes() {
    const container = document.getElementById('priceContainer');
    const blocks = container.querySelectorAll('.price-block');
    
    blocks.forEach((block, index) => {
        // Update heading
        const heading = block.querySelector('.price-block-title');
        if (heading) {
            heading.textContent = `Ngày ${index + 1}`;
        }
        
        // Update input names
        const inputs = block.querySelectorAll('input');
        inputs.forEach(input => {
            if (input.name.includes('tourDetails[')) {
                const fieldName = input.name.split('][')[1];
                input.name = `tourDetails[${index}][${fieldName}`;
            }
        });
    });
}

function updatePriceBlockButtons() {
    const container = document.getElementById('priceContainer');
    if (!container) return;
    
    const blocks = container.querySelectorAll('.price-block');
    const totalBlocks = blocks.length;
    
    blocks.forEach((block, index) => {
        const deleteBtn = block.querySelector('.price-block-delete');
        if (deleteBtn) {
            // Chỉ hiển thị nút xóa khi có từ 2 block trở lên
            if (totalBlocks > 1) {
                // Hiển thị nút với animation
                deleteBtn.style.display = 'inline-block';
                deleteBtn.style.opacity = '1';
                deleteBtn.style.visibility = 'visible';
            } else {
                // Ẩn nút với animation
                deleteBtn.style.opacity = '0';
                deleteBtn.style.visibility = 'hidden';
            }
        }
        
        // Cập nhật tiêu đề block
        const heading = block.querySelector('.price-block-title');
        if (heading) {
            heading.textContent = `Ngày ${index + 1}`;
        }
    });
    
    // Thêm/xóa thông báo cho block duy nhất
    updateSingleBlockMessage(totalBlocks === 1);
}

// Hàm thêm thông báo cho block duy nhất
function updateSingleBlockMessage(isSingleBlock) {
    const container = document.getElementById('priceContainer');
    if (!container) return;
    
    const existingMessage = container.querySelector('.single-block-message');
    
    if (isSingleBlock && !existingMessage) {
        const message = document.createElement('div');
        message.className = 'single-block-message alert alert-info mt-2';
        message.innerHTML = `
            <i class="fas fa-info-circle me-2"></i>
            <small>Đây là block chi tiết duy nhất. Thêm block mới để có thêm tùy chọn giá khác nhau.</small>
        `;
        container.appendChild(message);
    } else if (!isSingleBlock && existingMessage) {
        existingMessage.remove();
    }
}

// Multiple Images Preview Functionality
function setupMultipleImagesPreview() {
    const imagesInput = document.getElementById('images');
    
    if (!imagesInput) {
        return;
    }
    
    imagesInput.addEventListener('change', function(e) {
        handleImagePreview(e.target.files);
    });
}

function handleImagePreview(files) {
    const imagesPreview = document.getElementById('imagesPreview');
    const previewContainer = document.getElementById('previewContainer');
    
    // Clear previous previews
    if (previewContainer) {
        previewContainer.innerHTML = '';
    }
    
    if (files.length === 0) {
        if (imagesPreview) {
            imagesPreview.style.display = 'none';
        }
        return;
    }
    
    // Show preview container
    if (imagesPreview) {
        imagesPreview.style.display = 'block';
    }
    
    // Process each file
    Array.from(files).forEach((file, index) => {
        // Validate file type
        if (!file.type.startsWith('image/')) {
            alert(`File "${file.name}" không phải là ảnh!`);
            return;
        }
        
        // Validate file size (5MB)
        if (file.size > 5 * 1024 * 1024) {
            alert(`File "${file.name}" quá lớn! Kích thước tối đa: 5MB`);
            return;
        }
        
        // Create preview
        const reader = new FileReader();
        reader.onload = function(e) {
            createImagePreviewItem(e.target.result, file, index);
        };
        reader.readAsDataURL(file);
    });
}

function createImagePreviewItem(imageSrc, file, index) {
    const previewContainer = document.getElementById('previewContainer');
    if (!previewContainer) {
        return;
    }
    
    const previewItem = document.createElement('div');
    previewItem.className = 'col-md-3 col-sm-4 col-6 mb-3';
    
    const sizeInMB = (file.size / 1024 / 1024).toFixed(2);
    
    previewItem.innerHTML = `
        <div class="image-preview-item border rounded p-2 bg-light">
            <img src="${imageSrc}" alt="Preview ${index + 1}" class="img-fluid w-100" style="height: 120px; object-fit: cover; border-radius: 4px;">
            <div class="mt-2">
                <small class="text-muted d-block text-truncate" title="${file.name}">${file.name}</small>
                <small class="text-muted">${sizeInMB} MB</small>
            </div>
        </div>
    `;
    
    previewContainer.appendChild(previewItem);
}

// Enable disabled day inputs before form submission
document.addEventListener('DOMContentLoaded', function() {
    // Handle form submission to enable day inputs
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            // Enable all disabled day inputs before submission
            const dayInputs = form.querySelectorAll('input[name*="[day]"][disabled]');
            dayInputs.forEach(input => {
                input.disabled = false;
            });
        });
    });
});

// Number formatting functions
function setupNumberFormatting() {
    // Format price inputs
    document.querySelectorAll('input[name*="Price"], input[name*="price"]').forEach(input => {
        formatPriceInput(input);
    });
    
    // Format discount inputs
    document.querySelectorAll('input[name*="discount"]').forEach(input => {
        formatDiscountInput(input);
    });
    
    // Format stock inputs
    document.querySelectorAll('input[name*="stock"]').forEach(input => {
        formatStockInput(input);
    });
}

// Format price input with thousand separators and "đ" suffix
function formatPriceInput(input) {
    if (!input) return;
    
    // Change input type to text
    input.type = 'text';
    
    // Format on input
    input.addEventListener('input', function(e) {
        let value = e.target.value;
        
        // Remove all non-numeric characters except dots
        value = value.replace(/[^\d]/g, '');
        
        if (value === '') {
            e.target.value = '';
            return;
        }
        
        // Add thousand separators
        const formattedValue = formatNumberWithDots(value);
        e.target.value = formattedValue + ' đ';
        
        // Set cursor position
        const cursorPosition = e.target.value.length - 2; // Before " đ"
        setTimeout(() => {
            e.target.setSelectionRange(cursorPosition, cursorPosition);
        }, 0);
    });
    
    // Format on blur
    input.addEventListener('blur', function(e) {
        let value = e.target.value.replace(/[^\d]/g, '');
        if (value !== '') {
            e.target.value = formatNumberWithDots(value) + ' đ';
        }
    });
    
    // Format on focus - select number part only
    input.addEventListener('focus', function(e) {
        const value = e.target.value.replace(/[^\d]/g, '');
        if (value !== '') {
            e.target.value = formatNumberWithDots(value) + ' đ';
            // Select the number part (before " đ")
            setTimeout(() => {
                e.target.setSelectionRange(0, e.target.value.length - 2);
            }, 0);
        }
    });
    
    // Prevent invalid characters
    input.addEventListener('keypress', function(e) {
        // Allow only numbers, backspace, delete, arrow keys
        if (!/[\d]/.test(e.key) && !['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(e.key)) {
            e.preventDefault();
        }
    });
    
    // Format existing value on page load
    if (input.value && input.value !== '') {
        let value = input.value.replace(/[^\d]/g, '');
        if (value !== '') {
            input.value = formatNumberWithDots(value) + ' đ';
        }
    }
}

// Format discount input with "%" suffix
function formatDiscountInput(input) {
    if (!input) return;
    
    // Change input type to text
    input.type = 'text';
    
    // Format on input
    input.addEventListener('input', function(e) {
        let value = e.target.value;
        
        // Remove all non-numeric characters except dots
        value = value.replace(/[^\d.]/g, '');
        
        // Ensure only one decimal point
        const parts = value.split('.');
        if (parts.length > 2) {
            value = parts[0] + '.' + parts.slice(1).join('');
        }
        
        // Limit to 2 decimal places
        if (parts[1] && parts[1].length > 2) {
            value = parts[0] + '.' + parts[1].substring(0, 2);
        }
        
        if (value === '') {
            e.target.value = '';
            return;
        }
        
        // Ensure discount doesn't exceed 100
        const numValue = parseFloat(value);
        if (numValue > 100) {
            value = '100';
        }
        
        e.target.value = value + '%';
        
        // Set cursor position
        const cursorPosition = e.target.value.length - 1; // Before "%"
        setTimeout(() => {
            e.target.setSelectionRange(cursorPosition, cursorPosition);
        }, 0);
    });
    
    // Format on blur
    input.addEventListener('blur', function(e) {
        let value = e.target.value.replace(/[^\d.]/g, '');
        if (value !== '') {
            const numValue = parseFloat(value);
            if (numValue > 100) {
                value = '100';
            }
            e.target.value = value + '%';
        }
    });
    
    // Format on focus - select number part only
    input.addEventListener('focus', function(e) {
        const value = e.target.value.replace(/[^\d.]/g, '');
        if (value !== '') {
            e.target.value = value + '%';
            // Select the number part (before "%")
            setTimeout(() => {
                e.target.setSelectionRange(0, e.target.value.length - 1);
            }, 0);
        }
    });
    
    // Prevent invalid characters
    input.addEventListener('keypress', function(e) {
        // Allow only numbers, decimal point, backspace, delete, arrow keys
        if (!/[\d.]/.test(e.key) && !['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(e.key)) {
            e.preventDefault();
        }
    });
    
    // Format existing value on page load
    if (input.value && input.value !== '') {
        let value = input.value.replace(/[^\d.]/g, '');
        if (value !== '') {
            const numValue = parseFloat(value);
            if (numValue > 100) {
                value = '100';
            }
            input.value = value + '%';
        }
    }
}

// Format stock input (numbers only)
function formatStockInput(input) {
    if (!input) return;
    
    // Change input type to text
    input.type = 'text';
    
    // Format on input
    input.addEventListener('input', function(e) {
        let value = e.target.value;
        
        // Remove all non-numeric characters
        value = value.replace(/[^\d]/g, '');
        
        e.target.value = value;
    });
    
    // Prevent invalid characters
    input.addEventListener('keypress', function(e) {
        // Allow only numbers, backspace, delete, arrow keys
        if (!/[\d]/.test(e.key) && !['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(e.key)) {
            e.preventDefault();
        }
    });
    
    // Format existing value on page load
    if (input.value && input.value !== '') {
        let value = input.value.replace(/[^\d]/g, '');
        input.value = value;
    }
}

// Helper function to format number with dots as thousand separators
function formatNumberWithDots(num) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.');
}

// Helper function to parse formatted number back to integer
function parseFormattedNumber(formattedValue) {
    return parseInt(formattedValue.replace(/[^\d]/g, '')) || 0;
}

// Helper function to parse formatted discount back to number
function parseFormattedDiscount(formattedValue) {
    return parseFloat(formattedValue.replace(/[^\d.]/g, '')) || 0;
}

// Function to prepare form data before submission
function prepareFormDataForSubmission(form) {
    const formData = new FormData(form);
    
    // Process price inputs
    form.querySelectorAll('input[name*="Price"], input[name*="price"]').forEach(input => {
        if (input.value) {
            const cleanValue = parseFormattedNumber(input.value);
            formData.set(input.name, cleanValue.toString());
        }
    });
    
    // Process discount inputs
    form.querySelectorAll('input[name*="discount"]').forEach(input => {
        if (input.value) {
            const cleanValue = parseFormattedDiscount(input.value);
            formData.set(input.name, cleanValue.toString());
        }
    });
    
    // Process stock inputs
    form.querySelectorAll('input[name*="stock"]').forEach(input => {
        if (input.value) {
            const cleanValue = parseFormattedNumber(input.value);
            formData.set(input.name, cleanValue.toString());
        }
    });
    
    return formData;
}

// Show notification function
function showToastNotification(message, type = 'success') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.modal-notify');
    existingNotifications.forEach(notification => {
        notification.classList.remove('modal-notify--active');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    });
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `modal-notify modal-notify--${type}`;
    notification.style.zIndex = '9999'; // Ensure high z-index
    notification.style.opacity = '0'; // Start hidden
    notification.innerHTML = `
        <div class="modal-notify__content">
            <span class="modal-notify__message">${message}</span>
            <button class="modal-notify__close" onclick="hideToastNotification(this.closest('.modal-notify'))">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    // Add to body
    document.body.appendChild(notification);
    
    // Force styles to ensure visibility
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.background = 'white';
    notification.style.zIndex = '9999';
    
    // Show notification with animation
    setTimeout(() => {
        notification.classList.add('modal-notify--active');
        notification.style.opacity = '1'; // Force full opacity
    }, 10);
    
    // Auto hide after 3 seconds
    setTimeout(() => {
        hideToastNotification(notification);
    }, 3000);
}

// Function to hide toast notification
function hideToastNotification(notification) {
    if (notification && notification.classList.contains('modal-notify--active')) {
        notification.classList.remove('modal-notify--active');
        notification.style.opacity = '0'; // Fade out
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }
}

// Make the hide function globally available
window.hideToastNotification = hideToastNotification;

// Make formatting functions available globally
window.formatPriceInput = formatPriceInput;
window.formatDiscountInput = formatDiscountInput;
window.formatStockInput = formatStockInput;
window.formatNumberWithDots = formatNumberWithDots;
window.parseFormattedNumber = parseFormattedNumber;
window.parseFormattedDiscount = parseFormattedDiscount;
window.prepareFormDataForSubmission = prepareFormDataForSubmission;

// Initialize checkbox functionality
function initializeCheckboxes() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const tourCheckboxes = document.querySelectorAll('.tour-checkbox');
    const deleteSelectedBtn = document.getElementById('deleteSelectedBtn');
    
    // Select all checkbox handler
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const isChecked = this.checked;
            tourCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
            });
            updateDeleteButtonState();
        });
    }
    
    // Individual checkbox handlers
    tourCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectAllState();
            updateDeleteButtonState();
        });
    });
    
    // Delete selected button handler
    if (deleteSelectedBtn) {
        deleteSelectedBtn.addEventListener('click', function() {
            const selectedTours = getSelectedTours();
            if (selectedTours.length === 0) {
                showToastNotification('Vui lòng chọn ít nhất một tour để xóa', 'error');
                return;
            }
            
            showDeleteMultipleModal(selectedTours);
        });
    }
}

// Update select all checkbox state based on individual checkboxes
function updateSelectAllState() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const tourCheckboxes = document.querySelectorAll('.tour-checkbox');
    
    if (!selectAllCheckbox || tourCheckboxes.length === 0) return;
    
    const checkedCount = document.querySelectorAll('.tour-checkbox:checked').length;
    const totalCount = tourCheckboxes.length;
    
    if (checkedCount === 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    } else if (checkedCount === totalCount) {
        selectAllCheckbox.checked = true;
        selectAllCheckbox.indeterminate = false;
    } else {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = true;
    }
}

// Update delete button state based on selections
function updateDeleteButtonState() {
    const deleteSelectedBtn = document.getElementById('deleteSelectedBtn');
    const selectedCount = document.querySelectorAll('.tour-checkbox:checked').length;
    
    if (deleteSelectedBtn) {
        if (selectedCount > 0) {
            deleteSelectedBtn.disabled = false;
            deleteSelectedBtn.innerHTML = `<i class="fas fa-trash"></i> Xóa (${selectedCount})`;
        } else {
            deleteSelectedBtn.disabled = true;
            deleteSelectedBtn.innerHTML = '<i class="fas fa-trash"></i> Xóa';
        }
    }
}

// Get selected tour IDs and titles
function getSelectedTours() {
    const selectedTours = [];
    const checkedBoxes = document.querySelectorAll('.tour-checkbox:checked');
    
    checkedBoxes.forEach(checkbox => {
        const row = checkbox.closest('tr');
        const tourId = checkbox.getAttribute('data-id');
        const tourTitle = row.querySelector('.tour-title')?.textContent || 'Không có tiêu đề';
        
        selectedTours.push({
            id: tourId,
            title: tourTitle
        });
    });
    
    return selectedTours;
}

// Show delete multiple modal
function showDeleteMultipleModal(selectedTours) {
    const modal = document.getElementById('deleteMultipleModal');
    const modalBody = modal.querySelector('.modal-body');
    const confirmBtn = modal.querySelector('#confirmDeleteMultiple');
    
    // Update modal content
    modalBody.innerHTML = `
        <p>Bạn có chắc chắn muốn xóa <strong>${selectedTours.length}</strong> tour sau:</p>
        <ul class="list-unstyled">
            ${selectedTours.map(tour => `<li>• ${tour.title}</li>`).join('')}
        </ul>
        <p class="text-danger"><strong>Hành động này không thể hoàn tác!</strong></p>
    `;
    
    // Update confirm button handler
    confirmBtn.onclick = function() {
        deleteMultipleTours(selectedTours.map(tour => tour.id));
    };
    
    // Show modal
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
}

// Delete multiple tours
async function deleteMultipleTours(tourIds) {
    try {
        const response = await fetch('/tour/delete-multiple', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ ids: tourIds })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showToastNotification(result.message, 'success');
            
            // Close modal immediately
            const modal = bootstrap.Modal.getInstance(document.getElementById('deleteMultipleModal'));
            if (modal) {
                modal.hide();
            }
            
            // Reload page after short delay to ensure data consistency and proper STT
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showToastNotification(result.message || 'Có lỗi xảy ra khi xóa tour', 'error');
        }
    } catch (error) {
        console.error('Error deleting tours:', error);
        showToastNotification('Có lỗi xảy ra khi xóa tour', 'error');
    } finally {
        // Ensure modal is properly closed on any outcome
        try {
            const modal = bootstrap.Modal.getInstance(document.getElementById('deleteMultipleModal'));
            if (modal) {
                modal.hide();
            }
        } catch (e) {
            console.warn('Modal already hidden');
        }
    }
}

// Update row numbers after deletion
function updateRowNumbers() {
    const tableBody = document.querySelector('.tour__table tbody');
    if (!tableBody) return;
    
    // Get all data rows (exclude empty rows)
    const dataRows = tableBody.querySelectorAll('tr:not([data-empty])');
    
    // Update STT based on current position in table (index + 1)
    dataRows.forEach((row, index) => {
        const firstCell = row.querySelector('td:first-child');
        // Only update if this is a STT cell (not checkbox or other)
        if (firstCell && !firstCell.querySelector('input[type="checkbox"]')) {
            firstCell.textContent = index + 1;
        }
    });
}

function handleEmptyTable() {
    const tableBody = document.querySelector('.tour__table tbody');
    if (!tableBody) return;
    
    // Check if there are any actual data rows (not empty message rows)
    const dataRows = tableBody.querySelectorAll('tr:not([data-empty])');
    
    if (dataRows.length === 0) {
        // Clear all existing rows and show empty message
        const colspan = tableBody.closest('table').querySelector('thead tr').children.length;
        tableBody.innerHTML = `
            <tr data-empty="true">
                <td colspan="${colspan}" class="tour__table-empty text-center py-4">
                    <i class="fas fa-map text-muted mb-2" style="font-size: 3rem;"></i>
                    <p class="text-muted mb-0">Chưa có tour nào.</p>
                    <p class="text-muted small">Thêm tour đầu tiên của bạn!</p>
                </td>
            </tr>
        `;
        
        // Hide pagination if exists
        const pagination = document.querySelector('.tour__pagination');
        if (pagination) {
            pagination.style.display = 'none';
        }
    } else {
        // Show pagination if hidden and there's data
        const pagination = document.querySelector('.tour__pagination');
        if (pagination) {
            pagination.style.display = '';
        }
    }
}

function addNewRowToTable(tourData) {
    const tableBody = document.querySelector('.tour__table tbody');
    if (!tableBody) return;
    
    const emptyRow = tableBody.querySelector('td.tour__table-empty');
    
    // Remove empty message if exists
    if (emptyRow) {
        emptyRow.closest('tr').remove();
    }
    
    // Get current number of data rows (excluding empty rows)
    const currentDataRows = tableBody.querySelectorAll('tr:not([data-empty])');
    const newSTT = currentDataRows.length + 1; // New record will be at the end
    
    // Create new row HTML (simplified version - actual implementation would be more complex)
    const newRow = document.createElement('tr');
    newRow.innerHTML = `
        <td>${newSTT}</td>
        <td class="tour__name-cell">
            <div class="tour__name-wrapper">
                <div class="tour__image-wrapper">
                    <img src="${tourData.image || '/images/no-image.jpg'}" alt="${tourData.name}" class="tour__image">
                </div>
                <div class="tour__name-info">
                    <strong class="tour__name">${tourData.name}</strong>
                    <span class="tour__code">${tourData.code || ''}</span>
                </div>
            </div>
        </td>
        <td>
            <span class="tour__price">${tourData.price || 0} VNĐ</span>
        </td>
        <td>
            <button
                class="tour__badge tour__badge--toggle ${tourData.status === 'Hoạt động' ? 'tour__badge--success' : 'tour__badge--inactive'}"
                data-id="${tourData.id}"
                data-status="${tourData.status}"
                onclick="toggleTourStatus(this)"
                title="Nhấn để thay đổi trạng thái"
            >
                ${tourData.status === 'Hoạt động' ? 'Hoạt động' : 'Tạm dừng'}
            </button>
        </td>
        <td>
            <div class="d-flex gap-1 justify-content-center">
                <a href="/tour/edit/${tourData.id}" class="tour__btn tour__btn--warning tour__btn--sm" title="Chỉnh sửa tour">
                    <i class="fas fa-edit"></i>
                </a>
                <a href="/tour/${tourData.id}" class="tour__btn tour__btn--info tour__btn--sm" title="Xem chi tiết">
                    <i class="fas fa-eye"></i>
                </a>
                <form class="tour__form tour__form--delete" action="/tour/delete/${tourData.id}" method="POST">
                    <button type="submit" class="tour__btn tour__btn--danger tour__btn--sm" title="Xóa tour">
                        <i class="fas fa-trash"></i>
                    </button>
                </form>
            </div>
        </td>
    `;
    
    // IMPORTANT: Add to END of table (using appendChild, not insertBefore)
    tableBody.appendChild(newRow);
    
    // Setup delete handler for new row
    const deleteForm = newRow.querySelector('.tour__form--delete');
    setupSingleDeleteHandler(deleteForm);
    
    // Show pagination if hidden
    const pagination = document.querySelector('.tour__pagination');
    if (pagination) {
        pagination.style.display = '';
    }
}

function setupSingleDeleteHandler(form) {
    if (!form || form.hasAttribute("data-delete-setup")) {
        return;
    }
    form.setAttribute("data-delete-setup", "true");

    form.addEventListener("submit", function (e) {
        e.preventDefault();
        
        const row = form.closest("tr");
        const nameCell = row.querySelector(".tour__name");
        const tourName = nameCell ? nameCell.textContent.trim() : "tour này";
        
        showDeleteModal(tourName, form, row);
    });
}
