import { useState, useEffect, useContext, createContext } from 'react'
import { userApi } from '../api/userApi'

const AuthContext = createContext()

//Thành phần AuthProvider
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  //Kiểm tra xem người dùng có đăng nhập trên ứng dụng bắt đầu không
  useEffect(() => {
    checkAuthStatus()
  }, [])

  const checkAuthStatus = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        setLoading(false)
        return
      }

      //Xác minh mã thông báo với phụ trợ
      const response = await userApi.getProfile()
      setUser(response.data)
      setIsAuthenticated(true)
    } catch (error) {
      //Mã thông báo không hợp lệ, xóa nó
      localStorage.removeItem('token')
      setUser(null)
      setIsAuthenticated(false)
    } finally {
      setLoading(false)
    }
  }

  const login = async (credentials) => {
    try {
      const response = await userApi.login(credentials)
      
      //Kiểm tra xem đăng nhập có thành công không
      if (!response.success) {
        throw new Error(response.message || 'Đăng nhập thất bại')
      }
      
      const { user: userData, token } = response

      // Lưu trữ mã thông báo trong localStorage
      localStorage.setItem('token', token)

      // Cập nhật trạng thái
      setUser(userData)
      setIsAuthenticated(true)

      return userData
    } catch (error) {
      console.error('Login error:', error)
      throw new Error(error.response?.data?.message || error.message || 'Đăng nhập thất bại')
    }
  }

  const register = async (userData) => {
    try {
      const response = await userApi.register(userData)
      return response.data
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Đăng ký thất bại')
    }
  }

  const logout = async () => {
    try {
     //Gọi API đăng xuất nếu cần
      await userApi.logout()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // Xóa local storage và trạng thái
      localStorage.removeItem('token')
      setUser(null)
      setIsAuthenticated(false)
    }
  }

  const updateUser = (userData) => {
    setUser(userData)
  }

  const value = {
    user,
    loading,
    isAuthenticated,
    login,
    register,
    logout,
    updateUser,
    checkAuthStatus
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

//Móc tùy chỉnh để sử dụng bối cảnh auth
export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}