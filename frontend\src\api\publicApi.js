import axios from 'axios'

// Tạo instance axios cho public APIs (không cần authentication)
const publicApi = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3000',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: false, // Không gửi credentials cho public APIs
})

// Response interceptor chỉ để log lỗi, không redirect
publicApi.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    // Chỉ log lỗi, không redirect
    console.log('Public API error:', error.message)
    return Promise.reject(error)
  }
)

export { publicApi }
