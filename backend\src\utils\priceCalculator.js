const TourDetail = require("../models/tourDetailModel");
const Tour = require("../models/tourModel");

/**
 * Tính toán tổng giá từ các tour details
 * @param {Array} tourDetails - Mảng các tour detail
 * @returns {Number} - Tổng giá
 */
function calculateTotalPrice(tourDetails) {
    if (!tourDetails || !Array.isArray(tourDetails) || tourDetails.length === 0) {
        return 0;
    }

    let totalPrice = 0;
    
    tourDetails.forEach(detail => {
        // Tính giá chính (lấy giá cao nhất làm giá chuẩn)
        const basePrice = Math.max(
            detail.adultPrice || 0,
            detail.childrenPrice || 0,
            detail.childPrice || 0,
            detail.babyPrice || 0
        );
        
        // Nếu có giảm giá, áp dụng giảm giá
        let finalPrice = basePrice;
        if (detail.discount && detail.discount > 0) {
            finalPrice = basePrice - (basePrice * detail.discount / 100);
        }
        
        // Cộng thêm phụ thu phòng đơn (nếu có)
        if (detail.singleRoomSupplementPrice) {
            finalPrice += detail.singleRoomSupplementPrice;
        }
        
        totalPrice += finalPrice;
    });
    
    return Math.round(totalPrice);
}

/**
 * Tính toán lại tổng giá và cập nhật vào tour
 * @param {String} tourId - ID của tour
 * @returns {Promise<Number>} - Tổng giá đã cập nhật
 */
async function recalculateAndUpdateTourPrice(tourId) {
    try {
        // Lấy tất cả tour details của tour
        const tourDetails = await TourDetail.find({ tourId }).lean();
        
        // Tính tổng giá
        const totalPrice = calculateTotalPrice(tourDetails);
        
        // Cập nhật tổng giá vào tour
        await Tour.findByIdAndUpdate(tourId, { 
            totalPrice: totalPrice,
            price: totalPrice // Cập nhật cả price cũ để tương thích
        });
        
        return totalPrice;
    } catch (error) {
        console.error('Error recalculating tour price:', error);
        throw error;
    }
}

/**
 * Tính giá tối thiểu từ tour details (để hiển thị "Từ xxx VND")
 * @param {Array} tourDetails - Mảng các tour detail
 * @returns {Number} - Giá tối thiểu
 */
function calculateMinPrice(tourDetails) {
    if (!tourDetails || !Array.isArray(tourDetails) || tourDetails.length === 0) {
        return 0;
    }

    let minPrice = Number.MAX_VALUE;
    
    tourDetails.forEach(detail => {
        const prices = [
            detail.adultPrice || 0,
            detail.childrenPrice || 0,
            detail.childPrice || 0,
            detail.babyPrice || 0
        ].filter(price => price > 0);
        
        if (prices.length > 0) {
            const detailMinPrice = Math.min(...prices);
            
            // Áp dụng giảm giá nếu có
            let finalPrice = detailMinPrice;
            if (detail.discount && detail.discount > 0) {
                finalPrice = detailMinPrice - (detailMinPrice * detail.discount / 100);
            }
            
            minPrice = Math.min(minPrice, finalPrice);
        }
    });
    
    return minPrice === Number.MAX_VALUE ? 0 : Math.round(minPrice);
}

/**
 * Tính giá tối đa từ tour details
 * @param {Array} tourDetails - Mảng các tour detail
 * @returns {Number} - Giá tối đa
 */
function calculateMaxPrice(tourDetails) {
    if (!tourDetails || !Array.isArray(tourDetails) || tourDetails.length === 0) {
        return 0;
    }

    let maxPrice = 0;
    
    tourDetails.forEach(detail => {
        const basePrice = Math.max(
            detail.adultPrice || 0,
            detail.childrenPrice || 0,
            detail.childPrice || 0,
            detail.babyPrice || 0
        );
        
        // Áp dụng giảm giá nếu có
        let finalPrice = basePrice;
        if (detail.discount && detail.discount > 0) {
            finalPrice = basePrice - (basePrice * detail.discount / 100);
        }
        
        // Cộng thêm phụ thu phòng đơn (nếu có)
        if (detail.singleRoomSupplementPrice) {
            finalPrice += detail.singleRoomSupplementPrice;
        }
        
        maxPrice = Math.max(maxPrice, finalPrice);
    });
    
    return Math.round(maxPrice);
}

module.exports = {
    calculateTotalPrice,
    recalculateAndUpdateTourPrice,
    calculateMinPrice,
    calculateMaxPrice
};
