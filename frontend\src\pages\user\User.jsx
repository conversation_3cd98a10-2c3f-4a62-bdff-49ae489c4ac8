import React from "react";
import { useState, useEffect, useRef } from "react";
import { Navigate, useNavigate } from "react-router-dom";
import { userApi } from "../../api/userApi";
import { message } from "antd";

const User = () => {
  const navigate = useNavigate();
  const [isEditingName, setIsEditingName] = useState(false);
  const [isEditingPhone, setIsEditingPhone] = useState(false);
  const [isEditingAddress, setIsEditingAddress] = useState(false);
  const [user, setUser] = useState({});
  const [name, setName] = useState("");
  const [phone, setPhone] = useState("");
  const [address, setAddress] = useState("");

  const handleLogout = async () => {
    try {
      await userApi.logout();
      message.success("Đã đăng xuất");
      navigate("/login");
    } catch (error) {
      console.log(error);
      message.error("Đăng xuất thất bại");
    }
  };

  const handleEditUser = async () => {
    try {
      const data = {
        fullName: name,
        phoneNumber: phone,
        address: address,
      };
      await userApi.updateProfile(data);
      message.success("Cập nhật thông tin thành công!");
      setIsEditingName(false);
      setIsEditingPhone(false);
      setIsEditingAddress(false);
    } catch (error) {
      console.log(error);
      message.error("Cập nhật thông tin thất bại!");
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await userApi.getProfile();
        setUser(response);
        setName(response.user.fullName);
        setPhone(response.user.phoneNumber);
        setAddress(response.user.address);
      } catch (error) {
        console.log(error);
        message.error("Không thể tải thông tin người dùng!");
      }
    };
    fetchData();
  }, []);

  return (
    <>
    <div className="flex flex-col justify-start items-center relative">
      <div className="flex justify-center flex-col items-center gap-4 mb-20 w-full">
        {/* Header */}
        <div className="w-[85%] flex flex-col items-center justify-center">
          <div className="w-full">
            <button
              className="bg-transparent border-none flex flex-row justify-center items-center gap-2 text-base font-medium cursor-pointer"
              onClick={() => navigate("/")}
            >
              <i className="fa-solid fa-arrow-left"></i>
              <span className="text-base font-semibold">Quay lại</span>
            </button>
          </div>
          <h1 className="text-4xl font-bold mt-8 mb-6 text-center text-[#0b5da7]">
            Tài khoản của bạn
          </h1>
        </div>

        {/* Content */}
        <div className="flex flex-row items-stretch gap-12 justify-center w-[85%]">
          {/* Sidebar */}
          <div className="w-[30%] relative">
            <div className="max-w-[30rem] p-8 border border-gray-300 rounded-lg">
              {/* Sidebar Top */}
              <div className="overflow-hidden border-b border-gray-300 pb-8">
                <div className="w-full flex flex-row justify-start items-start gap-8">
                  <img src="" alt="" className="rounded-full w-[50px]" />
                  <div className="flex flex-col">
                    <div className="font-medium">{user?.user?.fullName}</div>
                    <div className="text-gray-600">{user?.user?.email}</div>
                  </div>
                </div>
              </div>

              {/* Sidebar Body */}
              <div className="w-full flex flex-col justify-start pt-8 overflow-x-hidden items-start gap-3">
                <div className="items-end w-full flex flex-col justify-start">
                  <button className="no-underline text-xl border-none bg-none w-full text-left cursor-pointer outline-none py-3 flex items-center gap-3">
                    <i className="fa-solid fa-user"></i> Tài khoản
                  </button>
                  <div className="w-[90%] flex flex-col justify-start items-start">
                    <button className="text-xl no-underline border-none bg-none w-full text-left cursor-pointer outline-none py-4 flex items-center gap-3">
                      Thông tin cá nhân
                    </button>
                    <button
                      className="text-xl no-underline border-none bg-none w-full text-left cursor-pointer outline-none py-4 flex items-center gap-3"
                      onClick={() => navigate("/user/change-password")}
                    >
                      Đổi mật khẩu
                    </button>
                    <button 
                      className="text-xl no-underline border-none bg-none w-full text-left cursor-pointer outline-none py-4 flex items-center gap-3" 
                      onClick={handleLogout}
                    >
                      Đăng xuất
                    </button>
                    <button
                      className="text-xl no-underline border-none bg-none w-full text-left cursor-pointer outline-none py-4 flex items-center gap-3"
                      onClick={() => navigate("/user/delete-user")}
                    >
                      Yêu cầu xóa tài khoản
                    </button>
                  </div>
                </div>
                <div className="items-end w-full flex flex-col justify-start">
                  <button className="text-xl no-underline border-none bg-none w-full text-left cursor-pointer outline-none py-3 flex items-center gap-3">
                    <i className="fa-solid fa-heart"></i> Yêu thích đã lưu
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="w-[80%]">
            <div className="w-full flex flex-col items-center justify-center">
              <div className="flex flex-row justify-start items-start gap-8 w-full">
                <div className="mb-24 flex-grow p-8 border border-gray-300 rounded-lg">
                  {/* Content Header */}
                  <div className="pb-8 mb-8 border-b border-gray-300">
                    <h4 className="text-gray-900 text-2xl font-bold">
                      Thông tin cá nhân
                    </h4>
                    <p className="text-xl font-medium m-0">
                      Cập nhật thông tin của Quý khách và tìm hiểu các thông tin
                      này được sử dụng ra sao
                    </p>
                  </div>

                  {/* Content Body */}
                  <div className="grid grid-cols-2 gap-0">
                    {/* Name Field */}
                    <div className="flex justify-between border-b border-gray-300 pb-6 mb-6 text-xl font-medium gap-4">
                      <div className="flex-none w-1/4 max-w-[30rem] whitespace-nowrap">
                        Họ tên
                      </div>
                      <div className="flex-grow text-left mr-8">
                        {!isEditingName ? (
                          <div className="flex items-center justify-between w-full gap-8">
                            {name}
                            <button className="border-none outline-none bg-transparent mr-8 flex-grow text-right">
                              <i
                                className="fa-solid fa-pen cursor-pointer"
                                onClick={() => setIsEditingName(true)}
                              ></i>
                            </button>
                          </div>
                        ) : (
                          <div>
                            <label className="block text-base font-bold">
                              Họ tên
                            </label>
                            <input
                              type="text"
                              placeholder="Nhập tên mới"
                              value={name}
                              onChange={(e) => setName(e.target.value)}
                              className="w-full border-none outline-none bg-transparent p-0 mt-3 mb-5 font-medium text-sm"
                            />
                            <div className="flex justify-end gap-3">
                              <button
                                className="text-[#004476] bg-transparent text-base font-bold py-3 px-6 border border-[#004476] rounded-lg cursor-pointer"
                                onClick={() => setIsEditingName(false)}
                              >
                                Hủy
                              </button>
                              <button
                                className="text-white bg-[#004476] text-base font-bold py-3 px-6 border border-[#004476] rounded-lg cursor-pointer"
                                onClick={() => handleEditUser()}
                              >
                                Lưu
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Phone Field */}
                    <div className="flex justify-between border-b border-gray-300 pb-6 mb-6 text-xl font-medium gap-4">
                      <div className="flex-none w-1/4 max-w-[30rem] whitespace-nowrap">
                        Điện thoại
                      </div>
                      <div className="flex-grow text-left mr-8">
                        {!isEditingPhone ? (
                          <div className="flex items-center justify-between w-full gap-8">
                            {phone}
                            <button className="border-none outline-none bg-transparent mr-8 flex-grow text-right">
                              <i
                                className="fa-solid fa-pen cursor-pointer"
                                onClick={() => setIsEditingPhone(true)}
                              ></i>
                            </button>
                          </div>
                        ) : (
                          <div>
                            <label className="block text-base font-bold">
                              Điện thoại
                            </label>
                            <input
                              type="text"
                              placeholder="Nhập số điện thoại"
                              value={phone}
                              onChange={(e) => setPhone(e.target.value)}
                              className="w-full border-none outline-none bg-transparent p-0 mt-3 mb-5 font-medium text-sm"
                            />
                            <div className="flex justify-end gap-3">
                              <button
                                className="text-[#004476] bg-transparent text-base font-bold py-3 px-6 border border-[#004476] rounded-lg cursor-pointer"
                                onClick={() => setIsEditingPhone(false)}
                              >
                                Hủy
                              </button>
                              <button
                                className="text-white bg-[#004476] text-base font-bold py-3 px-6 border border-[#004476] rounded-lg cursor-pointer"
                                onClick={() => handleEditUser()}
                              >
                                Lưu
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Email Field */}
                    <div className="flex justify-between border-b border-gray-300 pb-6 mb-6 text-xl font-medium gap-4">
                      <div className="flex-none w-1/4 max-w-[30rem] whitespace-nowrap">
                        Email
                      </div>
                      <div className="flex-grow text-left mr-8">
                        {user?.user?.email}
                      </div>
                    </div>

                    {/* Address Field */}
                    <div className="flex justify-between border-b border-gray-300 pb-6 mb-6 text-xl font-medium gap-4">
                      <div className="flex-none w-1/4 max-w-[30rem] whitespace-nowrap">
                        Địa chỉ
                      </div>
                      <div className="flex-grow text-left mr-8">
                        {!isEditingAddress ? (
                          <div className="flex items-center justify-between w-full gap-8">
                            {address}
                            <button className="border-none outline-none bg-transparent mr-8 flex-grow text-right">
                              <i
                                className="fa-solid fa-pen cursor-pointer"
                                onClick={() => setIsEditingAddress(true)}
                              ></i>
                            </button>
                          </div>
                        ) : (
                          <div>
                            <label className="block text-base font-bold">
                              Địa chỉ:
                            </label>
                            <input
                              type="text"
                              placeholder="Nhập địa chỉ"
                              value={address}
                              onChange={(e) => setAddress(e.target.value)}
                              className="w-full border-none outline-none bg-transparent p-0 mt-3 mb-5 font-medium text-sm"
                            />
                            <div className="flex justify-end gap-3">
                              <button
                                className="text-[#004476] bg-transparent text-base font-bold py-3 px-6 border border-[#004476] rounded-lg cursor-pointer"
                                onClick={() => setIsEditingAddress(false)}
                              >
                                Hủy
                              </button>
                              <button
                                className="text-white bg-[#004476] text-base font-bold py-3 px-6 border border-[#004476] rounded-lg cursor-pointer"
                                onClick={() => handleEditUser()}
                              >
                                Lưu
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    </>
  );
}

export default User;