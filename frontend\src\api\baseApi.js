import axios from 'axios'

// Tạo instance axios
const baseApi = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3000',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
})

// <PERSON><PERSON>u cầu Interceptor để thêm mã thông báo auth
baseApi.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Bộ chặn phản hồi để xử lý các lỗi phổ biến
baseApi.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    // Xử lý lỗi mạng (không có response)
    if (!error.response) {
      console.error('Network error:', error.message)
      // <PERSON><PERSON> thể hiển thị thông báo lỗi mạng cho user
      return Promise.reject({
        ...error,
        message: 'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối internet.'
      })
    }

    // Xử lý 401 Unauthorized
    if (error.response?.status === 401) {
      const isRegisterRequest = error.config?.url?.includes('/api/register')
      const isLoginRequest = error.config?.url?.includes('/api/login')
      const isFeaturedToursRequest = error.config?.url?.includes('/api/tours') && error.config?.params?.featured
      const isDestinationsRequest = error.config?.url?.includes('/api/destinations')

      // Chỉ redirect khi không phải là request đăng ký, đăng nhập, hoặc public requests
      if (!isRegisterRequest && !isLoginRequest && !isFeaturedToursRequest && !isDestinationsRequest) {
        localStorage.removeItem('token')

        if (window.location.pathname !== '/login') {
          window.location.href = '/login?session=expired'
        }
      }
    }

    // Xử lý 403 Forbidden
    if (error.response?.status === 403) {
      console.error('Access denied:', error.response.data)
    }

    // Xử lý 500 Server Error
    if (error.response?.status >= 500) {
      console.error('Server error:', error.response.data)
    }

    return Promise.reject(error)
  }
)

export { baseApi }