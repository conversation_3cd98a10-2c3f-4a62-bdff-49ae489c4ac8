<!DOCTYPE html>
<html lang="vi">
    <head>
        <meta charset="UTF-8" />
        <title>Quản lý tài khoản</title>
        <!-- Embed Font -->
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
        <link
            href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap"
            rel="stylesheet"
        />
        <!-- Bootstrap CSS -->
        <link
            href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"
            rel="stylesheet"
        />
        <!-- Font Awesome -->
        <link
            rel="stylesheet"
            href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
        />
        <!-- CSS -->
        <link rel="stylesheet" href="/css/main.css" />
    </head>
    <body>
        <div class="dashboard">
            <div class="container_fuild">
                <div class="dashboard__inner">
                    <%- include('partials/sidebar') %>
                    <div class="account">
                        <div class="account__header">
                            <h2 class="account__title">Quản lý tài khoản</h2>
                            <div class="account__actions">
                                <% if (userPermissions && userPermissions.includes('CREATE_USERS')) { %>
                                    <a
                                        href="/account/add"
                                        class="account__btn account__btn--primary account__btn--add"
                                        >Thêm tài khoản mới</a
                                    >
                                <% } else { %>
                                    <a
                                        class="account__btn account__btn--primary account__btn--add disabled"
                                        style="pointer-events: none; opacity: 0.5"
                                        >Thêm tài khoản mới</a
                                    >
                                <% } %>
                            </div>
                        </div>

                        <!-- Search and Filter Section -->
                        <div class="account__search-section">
                            <div class="row align-items-center mb-3">
                                <div class="col-md-8">
                                    <form method="GET" action="/account" class="account__search-form">
                                        <div class="input-group">
                                            <input 
                                                type="text" 
                                                name="search" 
                                                class="form-control" 
                                                placeholder="Tìm kiếm theo tên, email hoặc vai trò..." 
                                                value="<%= search %>"
                                                maxlength="100"
                                            >
                                            <button class="btn btn-outline-secondary" type="submit">
                                                Tìm kiếm
                                            </button>
                                            <% if (search) { %>
                                            <a href="/account" class="btn btn-outline-danger">
                                                <i class="fas fa-times"></i>
                                            </a>
                                            <% } %>
                                        </div>
                                    </form>
                                </div>
                                <div class="col-md-4 text-end">
                                    <form method="GET" action="/account" class="account__items-per-page-form">
                                        <% if (search) { %>
                                        <input type="hidden" name="search" value="<%= search %>">
                                        <% } %>
                                        <input type="hidden" name="page" value="1">
                                        <div class="d-inline-flex align-items-center">
                                            <label for="itemsPerPage" class="form-label text-muted me-2 mb-0">
                                                Hiển thị:
                                            </label>
                                            <select name="limit" id="itemsPerPage" class="form-select form-select-sm" style="width: auto;" onchange="handleLimitChange(this)">
                                                <option value="5" <%= pagination.limit == 5 ? 'selected' : '' %>>5</option>
                                                <option value="10" <%= pagination.limit == 10 ? 'selected' : '' %>>10</option>
                                                <option value="15" <%= pagination.limit == 15 ? 'selected' : '' %>>15</option>
                                                <option value="20" <%= pagination.limit == 20 ? 'selected' : '' %>>20</option>
                                                <option value="25" <%= pagination.limit == 25 ? 'selected' : '' %>>25</option>
                                                <option value="50" <%= pagination.limit == 50 ? 'selected' : '' %>>50</option>
                                            </select>
                                            <span class="text-muted ms-2" style="font-size: 0.9rem; font-weight: 600;">mục/trang</span>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Notification Messages -->
                        <% if (message && message.length > 0) { %>
                        <div
                            class="modal-notify modal-notify--active modal-notify--success"
                        >
                            <div class="modal-notify__content">
                                <span class="modal-notify__message">
                                    <%= message[0] %>
                                </span>
                                <button
                                    class="modal-notify__close"
                                    onclick="hideToastNotification(this.closest('.modal-notify'))"
                                >
                                    ×
                                </button>
                            </div>
                        </div>
                        <% } %> 
                        <% if (error && error.length > 0) { %>
                        <div
                            class="modal-notify modal-notify--active modal-notify--error"
                        >
                            <div class="modal-notify__content">
                                <span class="modal-notify__message">
                                    <%= error[0] %>
                                </span>
                                <button
                                    class="modal-notify__close"
                                    onclick="hideToastNotification(this.closest('.modal-notify'))"
                                >
                                    ×
                                </button>
                            </div>
                        </div>
                        <% } %>

                        <div class="account__table--wrapper">
                            <table class="table table-striped account__table">
                                <thead>
                                    <tr>
                                        <th>STT</th>
                                        <th>Ảnh đại diện</th>
                                        <th>Họ tên</th>
                                        <th>Email</th>
                                        <th>Vai trò</th>
                                        <th>Loại người dùng</th>
                                        <th>Trạng thái</th>
                                        <th>Ngày tạo</th>
                                        <th>Hành động</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <% if (users && users.length > 0) { %>
                                        <% users.forEach((user, index) => { %>
                                            <tr>
                                                <td><%= (pagination.current - 1) * pagination.limit + index + 1 %></td>
                                                <td>
                                                    <% if (user.avatar && user.avatar.trim() !== '') { %>
                                                        <img 
                                                            src="<%= user.avatar %>" 
                                                            alt="<%= user.fullName %>" 
                                                            style="width: 70px; height: 50px; object-fit: cover; border: 1px solid #ddd;"
                                                            onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
                                                        >
                                                        <div style="width: 70px; height: 50px; background: #e9ecef; display: none; align-items: center; justify-content: center; border: 1px solid #ddd;">
                                                            <i class="fas fa-user text-muted"></i>
                                                        </div>
                                                    <% } else { %>
                                                        <div style="width: 70px; height: 50px; background: #e9ecef; display: flex; align-items: center; justify-content: center; border: 1px solid #ddd; margin: 0 auto;">
                                                            <i class="fas fa-user text-muted"></i>
                                                        </div>
                                                    <% } %>
                                                </td>
                                                <td>
                                                    <strong><%= user.fullName %></strong>
                                                </td>
                                                <td>
                                                    <%= user.email %>
                                                </td>
                                                <td>
                                                    <span class="badge bg-info"><%= user.role ? user.role.name : 'Không có' %></span>
                                                </td>
                                                <td>
                                                    <% if(user.user_type === 'customer') { %>
                                                        <span class="badge bg-primary">Khách hàng</span>
                                                    <% } else if(user.user_type === 'admin') { %>
                                                        <span class="badge bg-danger">Admin</span>
                                                    <% } else { %>
                                                        <span class="badge bg-secondary">Nhân viên</span>
                                                    <% } %>
                                                </td>
                                                <td>
                                                    <% if (userPermissions && userPermissions.includes('UPDATE_USERS')) { %>
                                                    <button 
                                                        class="account__badge account__badge--<%= user.status === 'Hoạt động' ? 'success' : 'inactive' %> account__badge--toggle"
                                                        onclick="toggleUserStatus('<%= user._id %>', this)"
                                                        title="Click để thay đổi trạng thái"
                                                        data-user-id="<%= user._id %>"
                                                        data-current-status="<%= user.status %>"
                                                    >
                                                        <%= user.status %>
                                                    </button>
                                                    <% } else { %>
                                                        <span class="account__badge account__badge--<%= user.status === 'Hoạt động' ? 'success' : 'inactive' %>" style="opacity:0.5;">
                                                            <%= user.status === 'Hoạt động' ? 'Hoạt động' : 'Tạm dừng' %>
                                                        </span>
                                                    <% } %>
                                                </td>
                                                <td>
                                                    <%= new Date(user.createdAt).toLocaleDateString('vi-VN') %>
                                                </td>
                                                <td>
                                                    <div class="d-flex gap-1 justify-content-center">
                                                        <% if (userPermissions && userPermissions.includes('UPDATE_USERS')) { %>
                                                            <a
                                                                href="/account/edit/<%= user._id %>"
                                                                class="account__btn account__btn--warning account__btn--sm"
                                                                title="Chỉnh sửa tài khoản"
                                                            >
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                        <% } else { %>
                                                            <a
                                                                class="account__btn account__btn--warning account__btn--sm disabled"
                                                                style="pointer-events: none; opacity: 0.5"
                                                                title="Không có quyền chỉnh sửa"
                                                            >
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                        <% } %>
                                                        <% if (userPermissions && userPermissions.includes('DELETE_USERS')) { %>
                                                            <form
                                                                class="account__form account__form--delete"
                                                                action="/account/delete/<%= user._id %>"
                                                                method="POST"
                                                                data-user-name="<%= user.fullName %>"
                                                            >
                                                                <%- include('partials/csrf-token') %>
                                                                <button
                                                                    type="submit"
                                                                    class="account__btn account__btn--danger account__btn--sm"
                                                                    title="Xóa tài khoản"
                                                                >
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            </form>
                                                        <% } else { %>
                                                            <button
                                                                class="account__btn account__btn--danger account__btn--sm disabled"
                                                                style="pointer-events: none; opacity: 0.5"
                                                                title="Không có quyền xóa"
                                                            >
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        <% } %>
                                                    </div>
                                                </td>
                                            </tr>
                                        <% }) %>
                                    <% } else { %>
                                        <tr>
                                            <td colspan="8" class="account__table-empty">
                                                <span>Chưa có tài khoản nào.</span>
                                            </td>
                                        </tr>
                                    <% } %>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination Section -->
                        <% if (users && users.length > 0) { %>
                        <div class="account__pagination">
                            <nav aria-label="User pagination">
                                <ul class="pagination justify-content-center">
                                    <!-- First Page -->
                                    <li class="page-item <%= !pagination.hasPrev ? 'disabled' : '' %>">
                                        <a class="page-link" href="<%= pagination.hasPrev ? `?page=1${search ? `&search=${encodeURIComponent(search)}` : ''}&limit=${pagination.limit}` : '#' %>" aria-label="Trang đầu">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                    <!-- Page Numbers -->
                                    <%
                                        let startPage = Math.max(1, pagination.current - 2);
                                        let endPage = Math.min(pagination.total, pagination.current + 2);
                                        
                                        // Ensure we show 5 pages when possible
                                        if (endPage - startPage < 4) {
                                            if (startPage === 1) {
                                                endPage = Math.min(pagination.total, startPage + 4);
                                            } else {
                                                startPage = Math.max(1, endPage - 4);
                                            }
                                        }
                                    %>
                                    
                                    <% for (let i = startPage; i <= endPage; i++) { %>
                                        <li class="page-item <%= i === pagination.current ? 'active' : '' %>">
                                            <a class="page-link" href="?page=<%= i %><%= search ? `&search=${encodeURIComponent(search)}` : '' %>&limit=<%= pagination.limit %>">
                                                <%= i %>
                                                <% if (i === pagination.current) { %>
                                                    <span class="sr-only">(current)</span>
                                                <% } %>
                                            </a>
                                        </li>
                                    <% } %>
                                    <!-- Last Page -->
                                    <li class="page-item <%= !pagination.hasNext ? 'disabled' : '' %>">
                                        <a class="page-link" href="<%= pagination.hasNext ? `?page=${pagination.total}${search ? `&search=${encodeURIComponent(search)}` : ''}&limit=${pagination.limit}` : '#' %>" aria-label="Trang cuối">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                        <% } %>
                    </div>
                </div>
            </div>
        </div>

        <!-- Delete Confirmation Modal -->
        <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header border-0">
                        <h5 class="modal-title" id="deleteModalLabel">
                            Xóa tài khoản
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p class="mb-3">Bạn có chắc chắn muốn xóa tài khoản <strong id="userNameToDelete"></strong> ?</p>
                        <div class="alert alert-warning d-flex align-items-center">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <small>Hành động này không thể hoàn tác!</small>
                        </div>
                    </div>
                    <div class="modal-footer border-0">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Hủy
                        </button>
                        <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                            <i class="fas fa-trash me-2"></i>Xóa
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Thêm CSRF token cho JavaScript -->
        <script>
            window.csrfToken = '<%= csrfToken %>';
        </script>
        
        <!-- Bootstrap JS -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
        <script src="/js/toast-auto-hide.js?v=<%= Date.now() %>"></script>
        <script src="/js/account.js?v=<%= Date.now() %>"></script>
        
        <!-- Session Security -->
        <script src="/js/session-security.js?v=<%= Date.now() %>"></script>
    </body>
</html>
