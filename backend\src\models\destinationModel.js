const mongoose = require('mongoose');

const destinationSchema = new mongoose.Schema({
  name: { type: String, required: true, trim: true, unique: true },
  slug: { type: String, unique: true, sparse: true },
  fullSlug: { type: String, unique: true, sparse: true },
  info: String,
  image: String, 
  status: { type: String, default: 'Hoạt động' },
  parent: { type: mongoose.Schema.Types.ObjectId, ref: 'Destination', default: null },
  createdBy: { type: String, default: 'System' },
  updatedBy: { type: String, default: 'System' },
}, {
  timestamps: true 
});

module.exports = mongoose.model('Destination', destinationSchema);