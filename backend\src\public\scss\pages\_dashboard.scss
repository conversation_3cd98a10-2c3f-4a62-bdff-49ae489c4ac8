@use "../abstracts" as *;

// Dashboard
.dashboard {
    background-color: #f5f7fa;
    min-height: 100vh;
    overflow: auto;
    &__inner {
        display: flex;
        position: relative;
    }
}

// Dashboard Main Content
.dashboard-main {
    flex: 1;
    padding: 1.5rem;
    margin-left: 240px;
    width: calc(100% - 240px);
}

// Stats Cards
.stats-cards {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 1rem;
    margin-bottom: 2rem;
}

.stats-card {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease;

    &:hover {
        transform: translateY(-2px);
    }

    &:nth-child(1) .stats-card__icon {
        background: #3498db;
    }

    &:nth-child(2) .stats-card__icon {
        background: #9b59b6;
    }

    &:nth-child(3) .stats-card__icon {
        background: #2ecc71;
    }

    &:nth-child(4) .stats-card__icon {
        background: #e74c3c;
    }

    &:nth-child(5) .stats-card__icon {
        background: #f39c12;
    }

    &__icon {
        width: 50px;
        height: 50px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
    }

    &__content {
        h3 {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            color: #2c3e50;
        }
    }

    &__numbers {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;

        span {
            font-size: 0.875rem;
            padding: 0.375rem 0.75rem;
            border-radius: 15px;
            font-weight: 500;

            &.active {
                background: rgba(46, 204, 113, 0.1);
                color: #27ae60;
            }

            &.inactive {
                background: rgba(149, 165, 166, 0.1);
                color: #95a5a6;
            }
        }
    }
}

// Dashboard Charts
.dashboard-charts {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.chart-container {
    background-color: #fff;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    
    .chart-tabs {
        display: flex;
        gap: 1rem;
        margin-bottom: 1.5rem;
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 1rem;
        
        .chart-tab {
            background: none;
            border: none;
            padding: 0.5rem 1rem;
            font-size: 1.2rem;
            color: #6c757d;
            cursor: pointer;
            border-radius: 5px;
            transition: all 0.2s ease;
            
            &:hover {
                background-color: #f8f9fa;
            }
            
            &.active {
                background-color: rgba($primary-color, 0.1);
                color: $primary-color;
                font-weight: 500;
            }
        }
    }
    
    .chart-content {
        height: 300px;
        position: relative;
    }
}

// Top Tours Section
.top-tours {
    background-color: #fff;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    
    .tour-tabs {
        display: flex;
        gap: 1rem;
        margin-bottom: 1.5rem;
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 1rem;
        
        .tour-tab {
            background: none;
            border: none;
            padding: 0.5rem 1rem;
            font-size: 1.2rem;
            color: #6c757d;
            cursor: pointer;
            border-radius: 5px;
            transition: all 0.2s ease;
            
            &:hover {
                background-color: #f8f9fa;
            }
            
            &.active {
                background-color: rgba($primary-color, 0.1);
                color: $primary-color;
                font-weight: 500;
            }
        }
    }
    
    .tour-list {
        overflow-x: auto;
    }
    
    .tour-table {
        width: 100%;
        border-collapse: collapse;
        
        th {
            text-align: left;
            padding: 1rem;
            font-size: 1.2rem;
            font-weight: 600;
            color: #495057;
            border-bottom: 2px solid #e9ecef;
        }
        
        td {
            padding: 1rem;
            font-size: 1rem;
            border-bottom: 1px solid #e9ecef;
            vertical-align: middle;
            font-style: italic;
            color: #495057;

            img {   
                width: 60px;
                height: 40px;
                object-fit: cover;
                border-radius: 4px;
            }
        }
        
        tr:hover td {
            background-color: #f8f9fa;
        }
    }
}

// Responsive
@media (max-width: 1200px) {
    .stats-cards {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 992px) {
    .dashboard-main {
        margin-left: 0;
        width: 100%;
    }
    
    .dashboard-charts {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .stats-cards {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .stats-cards {
        grid-template-columns: 1fr;
    }
}

