const mongoose = require("mongoose");

const categorySchema = new mongoose.Schema(
    {
        name: {
            type: String,
            required: true,
            trim: true,
            unique: true,
        },
        slug: {
            type: String,
            unique: true,
            sparse: true, // Allow null values but ensure uniqueness when present
        },
        fullSlug: {
            type: String,
            unique: true,
            sparse: true, // Allow null values but ensure uniqueness when present
        },
        description: {
            type: String,
            trim: true,
        },
        status: {
            type: String,
            default: "Hoạt động",
            enum: ["Hoạt động", "Không hoạt động"],
        },
        createdBy: {
            type: String,
            default: "System",
        },
        updatedBy: {
            type: String,
            default: "System",
        },
    },
    {
        timestamps: true,
    }
);

// Create case-insensitive unique index for name
categorySchema.index({ name: 1 }, { 
    unique: true, 
    collation: { locale: 'en', strength: 2 } 
});

module.exports = mongoose.model("Category", categorySchema);
