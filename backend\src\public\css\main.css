@charset "UTF-8";
/* http://meyerweb.com/eric/tools/css/reset/ 
   v2.0 | 20110126
   License: none (public domain)
*/
html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed,
figure, figcaption, footer, header, hgroup,
menu, nav, output, ruby, section, summary,
time, mark, audio, video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
}

/* HTML5 display-role reset for older browsers */
article, aside, details, figcaption, figure,
footer, header, hgroup, menu, nav, section {
  display: block;
}

body {
  line-height: 1;
}

ol, ul {
  list-style: none;
}

blockquote, q {
  quotes: none;
}

blockquote:before, blockquote:after,
q:before, q:after {
  content: "";
  content: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
}

html {
  font-size: 62.5%;
}

body {
  font-size: 1.6rem;
  font-family: "Inter", sans-serif;
}

li {
  list-style: none;
  padding: 0;
}

a {
  text-decoration: none;
  color: inherit;
}

button {
  background: none;
  border: none;
  cursor: pointer;
}

input {
  border: none;
  outline: none;
}

input,
button,
textarea,
select {
  font-family: inherit;
}

.invalid-feedback {
  font-size: 1rem !important;
  margin-top: 0.5rem !important;
}

body.modal-open {
  padding-right: 0 !important;
  overflow: hidden !important;
}

#deleteModal .modal-content, #deleteMultipleModal .modal-content, #permissionUpdateModal .modal-content {
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  background-color: #fff;
  border: none;
  overflow: hidden;
}
#deleteModal .modal-content .modal-header, #deleteMultipleModal .modal-content .modal-header, #permissionUpdateModal .modal-content .modal-header {
  padding: 1.5rem;
  border-bottom: none;
}
#deleteModal .modal-content .modal-header .modal-title, #deleteMultipleModal .modal-content .modal-header .modal-title, #permissionUpdateModal .modal-content .modal-header .modal-title {
  font-size: 1.4rem;
  font-weight: 600;
  color: #212529;
}
#deleteModal .modal-content .modal-header .btn-close, #deleteMultipleModal .modal-content .modal-header .btn-close, #permissionUpdateModal .modal-content .modal-header .btn-close {
  background: none;
  border: none;
  font-size: 1rem;
  opacity: 0.6;
}
#deleteModal .modal-content .modal-header .btn-close:hover, #deleteMultipleModal .modal-content .modal-header .btn-close:hover, #permissionUpdateModal .modal-content .modal-header .btn-close:hover {
  opacity: 1;
}
#deleteModal .modal-content .modal-body, #deleteMultipleModal .modal-content .modal-body, #permissionUpdateModal .modal-content .modal-body {
  padding: 1rem 1.5rem;
}
#deleteModal .modal-content .modal-body p, #deleteMultipleModal .modal-content .modal-body p, #permissionUpdateModal .modal-content .modal-body p {
  margin-bottom: 1rem;
  font-size: 1.2rem;
  color: #212529;
}
#deleteModal .modal-content .modal-body .alert-warning, #deleteMultipleModal .modal-content .modal-body .alert-warning, #permissionUpdateModal .modal-content .modal-body .alert-warning {
  background-color: #fff3cd;
  border: 1px solid #ffeeba;
  color: #856404;
  font-size: 1.2rem;
  padding: 0.75rem 1rem;
  border-radius: 5px;
}
#deleteModal .modal-content .modal-body .alert-warning i, #deleteMultipleModal .modal-content .modal-body .alert-warning i, #permissionUpdateModal .modal-content .modal-body .alert-warning i {
  font-size: 1.1rem;
  color: #ffc107;
}
#deleteModal .modal-content .modal-body .alert-warning small, #deleteMultipleModal .modal-content .modal-body .alert-warning small, #permissionUpdateModal .modal-content .modal-body .alert-warning small {
  margin-left: 0.5rem;
}
#deleteModal .modal-content .modal-footer, #deleteMultipleModal .modal-content .modal-footer, #permissionUpdateModal .modal-content .modal-footer {
  padding: 1rem 1.5rem;
  border-top: none;
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}
#deleteModal .modal-content .modal-footer .btn, #deleteMultipleModal .modal-content .modal-footer .btn, #permissionUpdateModal .modal-content .modal-footer .btn {
  font-size: 1.1rem;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 6px;
}
#deleteModal .modal-content .modal-footer .btn i, #deleteMultipleModal .modal-content .modal-footer .btn i, #permissionUpdateModal .modal-content .modal-footer .btn i {
  margin-right: 0.4rem;
}
#deleteModal .modal-content .modal-footer .btn-secondary, #deleteMultipleModal .modal-content .modal-footer .btn-secondary, #permissionUpdateModal .modal-content .modal-footer .btn-secondary {
  background-color: #6c757d;
  color: #fff;
}
#deleteModal .modal-content .modal-footer .btn-secondary:hover, #deleteMultipleModal .modal-content .modal-footer .btn-secondary:hover, #permissionUpdateModal .modal-content .modal-footer .btn-secondary:hover {
  background-color: #5a6268;
}
#deleteModal .modal-content .modal-footer .btn-danger, #deleteMultipleModal .modal-content .modal-footer .btn-danger, #permissionUpdateModal .modal-content .modal-footer .btn-danger {
  background-color: #dc3545;
  color: #fff;
}
#deleteModal .modal-content .modal-footer .btn-danger:hover, #deleteMultipleModal .modal-content .modal-footer .btn-danger:hover, #permissionUpdateModal .modal-content .modal-footer .btn-danger:hover {
  background-color: #c82333;
}

.modal-notify {
  position: fixed;
  top: 35px;
  right: 20px;
  z-index: 9999;
  min-width: 320px;
  max-width: 400px;
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
}
.modal-notify--active {
  opacity: 1 !important;
  transform: translateX(0) !important;
  pointer-events: all;
}
.modal-notify--success .modal-notify__content {
  border-left-color: #28a745;
}
.modal-notify--error .modal-notify__content {
  border-left-color: #dc3545;
}
.modal-notify--warning .modal-notify__content {
  border-left-color: #ffc107;
}
.modal-notify--info .modal-notify__content {
  border-left-color: #3498db;
}
.modal-notify__content {
  background: #ffffff;
  border-radius: 6px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #e9ecef;
  border-left: 3px solid #dee2e6;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  min-height: 40px;
  transition: all 0.2s ease;
}
.modal-notify__content:hover {
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.12), 0 3px 8px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}
.modal-notify__message {
  flex: 1;
  font-size: 10px;
  font-weight: 500;
  line-height: 1.4;
  color: #2c3e50;
  margin: 0;
  padding: 0;
  word-wrap: break-word;
  text-align: left;
}
.modal-notify__message i {
  display: none;
}
.modal-notify__close {
  background: transparent;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 0;
  margin: 0 0 0 8px;
  border-radius: 3px;
  transition: all 0.2s ease;
  font-size: 10px;
  line-height: 1;
  font-weight: 400;
  width: 20px;
  height: 20px;
  display: flex !important;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-family: Arial, sans-serif;
  font-size: 0;
}
.modal-notify__close::before {
  content: "×";
  display: block !important;
  font-size: 16px;
  line-height: 1;
  color: #6c757d;
}
.modal-notify__close:hover {
  background-color: #f8f9fa;
}
.modal-notify__close:hover::before {
  color: #495057;
}
.modal-notify__close:focus {
  outline: 2px solid #3498db;
  outline-offset: 1px;
  background-color: #f8f9fa;
}
.modal-notify__close:active {
  transform: scale(0.9);
}
.modal-notify__close i {
  display: none !important;
}
.modal-notify--hiding {
  opacity: 0 !important;
  transform: translateX(100%) !important;
  pointer-events: none;
}

@media (max-width: 768px) {
  .modal-notify {
    right: 16px;
    left: 16px;
    min-width: auto;
    max-width: none;
    width: auto;
  }
  .modal-notify__content {
    padding: 8px 10px;
    min-height: 32px;
  }
  .modal-notify__message {
    font-size: 12px;
  }
  .modal-notify__close {
    width: 18px;
    height: 18px;
  }
  .modal-notify__close::before {
    font-size: 14px;
  }
}
@media (prefers-contrast: high) {
  .modal-notify__content {
    border-width: 2px;
    border-left-width: 3px;
  }
  .modal-notify__message {
    font-weight: 600;
  }
}
@media (prefers-color-scheme: dark) {
  .modal-notify__content {
    background: #2c3e50;
    border-color: #495057;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3), 0 2px 4px rgba(0, 0, 0, 0.2);
  }
  .modal-notify__message {
    color: #ffffff;
  }
  .modal-notify__close {
    color: #adb5bd;
  }
  .modal-notify__close:hover {
    background-color: #495057;
    color: #ffffff;
  }
}
.dashboard-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 62px;
  padding: 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  height: 70px;
  background-color: #263248;
  position: absolute;
  bottom: 0;
  width: 100%;
}
.dashboard-header .user-info {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0.5rem 1rem;
  transition: all 0.3s ease;
  position: relative;
  color: #fff;
}
.dashboard-header .user-info:hover {
  background-color: rgba(255, 255, 255, 0.05);
}
.dashboard-header .user-info .user-avatar-container {
  position: relative;
  margin-right: 12px;
}
.dashboard-header .user-info .user-avatar-container::after {
  content: "";
  position: absolute;
  right: 0;
  bottom: 2px;
  width: 10px;
  height: 10px;
  background-color: #4cd137;
  border-radius: 50%;
  border: 2px solid #263248;
}
.dashboard-header .user-info .user-avatar {
  width: 38px;
  height: 38px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}
.dashboard-header .user-info .user-avatar:hover {
  border-color: rgba(255, 255, 255, 0.5);
  transform: scale(1.05);
}
.dashboard-header .user-info .user-details {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}
.dashboard-header .user-info .user-details h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #fff;
  letter-spacing: 0.2px;
}
.dashboard-header .user-info .user-details .user-role {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  display: block;
  margin-top: 0.1rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.dashboard-header .user-info .logout-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  color: rgba(255, 255, 255, 0.8);
  background-color: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  text-decoration: none;
  margin-left: 10px;
}
.dashboard-header .user-info .logout-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
}
.dashboard-header .user-info .logout-button i {
  font-size: 0.85rem;
}
@media (max-width: 576px) {
  .dashboard-header .user-info {
    padding: 0.5rem;
  }
  .dashboard-header .user-info .user-details {
    max-width: 120px;
  }
}

.sidebar {
  width: 220px;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: 1000;
  transition: transform 0.3s ease;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}
.sidebar__header {
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.sidebar__title {
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0;
  color: white;
}
.sidebar__title i {
  margin-right: 0.5rem;
  color: #3498db;
}
.sidebar__toggle {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
}
.sidebar__toggle:hover {
  background-color: rgba(255, 255, 255, 0.1);
}
.sidebar__nav {
  padding: 1rem 0;
}
.sidebar__list {
  list-style: none;
  padding: 0;
  margin: 0;
}
.sidebar__item {
  margin-bottom: 1rem;
  border-radius: 0 25px 25px 0;
  margin-right: 10px;
  overflow: hidden;
  transition: all 0.3s ease;
}
.sidebar__item:hover {
  margin-right: 5px;
  background: rgba(255, 255, 255, 0.05);
}
.sidebar__link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
  position: relative;
}
.sidebar__link:hover {
  background-color: rgba(255, 255, 255, 0.15);
  color: white;
  text-decoration: none;
  transform: translateX(2px);
}
.sidebar__link:hover .sidebar__icon {
  color: #3498db;
  transform: scale(1.1);
}
.sidebar__link:focus {
  outline: none;
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}
.sidebar__link--active {
  background-color: rgba(52, 152, 219, 0.3);
  color: white;
  border-left-color: #3498db;
  box-shadow: inset 0 0 10px rgba(52, 152, 219, 0.2);
}
.sidebar__link--active .sidebar__icon {
  color: #3498db;
  transform: scale(1.05);
}
.sidebar__link--active:hover {
  background-color: rgba(52, 152, 219, 0.4);
  transform: translateX(0);
}
.sidebar__link:active {
  transform: translateX(1px);
  background-color: rgba(52, 152, 219, 0.25);
}
.sidebar__icon {
  width: 1.25rem;
  margin-right: 0.75rem;
  color: inherit;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}
.sidebar__text {
  font-weight: 500;
  font-size: 1.4rem;
  transition: all 0.3s ease;
}

.mobile-toggle {
  position: fixed;
  top: 1rem;
  left: 1rem;
  z-index: 1001;
  background: #2c3e50;
  color: white;
  border: none;
  border-radius: 0.375rem;
  padding: 0.75rem;
  font-size: 1.1rem;
  cursor: pointer;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  transition: all 0.2s ease;
}
.mobile-toggle:hover {
  background: #34495e;
  transform: translateY(-1px);
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
}
@media (min-width: 992px) {
  .mobile-toggle {
    display: none !important;
  }
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}
.overlay--active {
  opacity: 1;
  visibility: visible;
}

@media (max-width: 991.98px) {
  .sidebar {
    transform: translateX(-100%);
  }
  .sidebar--open {
    transform: translateX(0);
  }
}
.sidebar__link {
  position: relative;
  overflow: hidden;
  padding: 1.5rem;
}
.sidebar__link::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
  z-index: 0;
}
.sidebar__link:active::before {
  width: 200px;
  height: 200px;
}
.sidebar__link * {
  position: relative;
  z-index: 1;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(52, 152, 219, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(52, 152, 219, 0);
  }
}
.sidebar__link--active {
  animation: pulse 2s infinite;
}

body {
  background: #fff;
  min-height: 100vh;
  overflow: auto;
}

.login-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 90vh;
}
.login-header {
  text-align: center;
  margin-bottom: 3rem;
}
.login-header img {
  max-width: 400px;
  height: auto;
}

.login-form {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
}
.login-form__wrapper {
  width: clamp(320px, 90vw, 900px);
  background: #fff;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 90px rgba(0, 0, 0, 0.1);
  display: flex;
  min-height: 500px;
}
.login-form__left {
  flex: 1;
  background: #3498db;
  padding: 3rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  text-align: center;
}
.login-form__left h3 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}
.login-form__left p {
  font-size: 1.2rem;
  opacity: 0.9;
  line-height: 1.6;
}
.login-form__right {
  flex: 1;
  padding: 3rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.login-form h2 {
  font-weight: 700;
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 1.5rem;
  text-align: center;
}

.form-label {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}
.form-label i {
  color: #667eea;
  width: 16px;
}
.form-label .required {
  color: #dc3545;
  margin-left: 3px;
}

.form-control {
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  padding: 12px 16px;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}
.form-control:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.15);
}
.form-control::placeholder {
  color: #a0a6b1;
  font-size: 1.2rem;
}

.btn-login {
  background: #3498db;
  border: none;
  border-radius: 10px;
  padding: 14px 20px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  margin-top: 1rem;
}
.btn-login:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}
.btn-login:disabled {
  opacity: 0.7;
  transform: none;
}

.alert {
  border-radius: 10px;
  border: none;
  margin-bottom: 1.5rem;
  padding: 12px 16px;
  font-size: 0.9rem;
}
.alert.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
  border-left: 4px solid #dc3545;
}
.alert.alert-success {
  background-color: #d1edff;
  color: #0c5460;
  border-left: 4px solid #17a2b8;
}
.alert i {
  margin-right: 8px;
}

.password-toggle {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: #666;
  transition: color 0.3s ease;
}
.password-toggle:hover {
  color: #667eea;
}

@media (max-width: 768px) {
  .login-form__wrapper {
    flex-direction: column;
    min-height: auto;
  }
  .login-form__left {
    padding: 2rem;
  }
  .login-form__left h3 {
    font-size: 1.5rem;
  }
  .login-form__left p {
    font-size: 1rem;
  }
  .login-form__right {
    padding: 2rem;
  }
  .login-page {
    padding: 1rem;
  }
}
.dashboard {
  background-color: #f5f7fa;
  min-height: 100vh;
  overflow: auto;
}
.dashboard__inner {
  display: flex;
  position: relative;
}

.dashboard-main {
  flex: 1;
  padding: 1.5rem;
  margin-left: 240px;
  width: calc(100% - 240px);
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
}

.stats-card {
  background: white;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: transform 0.3s ease;
}
.stats-card:hover {
  transform: translateY(-2px);
}
.stats-card:nth-child(1) .stats-card__icon {
  background: #3498db;
}
.stats-card:nth-child(2) .stats-card__icon {
  background: #9b59b6;
}
.stats-card:nth-child(3) .stats-card__icon {
  background: #2ecc71;
}
.stats-card:nth-child(4) .stats-card__icon {
  background: #e74c3c;
}
.stats-card:nth-child(5) .stats-card__icon {
  background: #f39c12;
}
.stats-card__icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}
.stats-card__content h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #2c3e50;
}
.stats-card__numbers {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}
.stats-card__numbers span {
  font-size: 0.875rem;
  padding: 0.375rem 0.75rem;
  border-radius: 15px;
  font-weight: 500;
}
.stats-card__numbers span.active {
  background: rgba(46, 204, 113, 0.1);
  color: #27ae60;
}
.stats-card__numbers span.inactive {
  background: rgba(149, 165, 166, 0.1);
  color: #95a5a6;
}

.dashboard-charts {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.chart-container {
  background-color: #fff;
  border-radius: 10px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.chart-container .chart-tabs {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 1rem;
}
.chart-container .chart-tabs .chart-tab {
  background: none;
  border: none;
  padding: 0.5rem 1rem;
  font-size: 1.2rem;
  color: #6c757d;
  cursor: pointer;
  border-radius: 5px;
  transition: all 0.2s ease;
}
.chart-container .chart-tabs .chart-tab:hover {
  background-color: #f8f9fa;
}
.chart-container .chart-tabs .chart-tab.active {
  background-color: rgba(52, 152, 219, 0.1);
  color: #3498db;
  font-weight: 500;
}
.chart-container .chart-content {
  height: 300px;
  position: relative;
}

.top-tours {
  background-color: #fff;
  border-radius: 10px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.top-tours .tour-tabs {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 1rem;
}
.top-tours .tour-tabs .tour-tab {
  background: none;
  border: none;
  padding: 0.5rem 1rem;
  font-size: 1.2rem;
  color: #6c757d;
  cursor: pointer;
  border-radius: 5px;
  transition: all 0.2s ease;
}
.top-tours .tour-tabs .tour-tab:hover {
  background-color: #f8f9fa;
}
.top-tours .tour-tabs .tour-tab.active {
  background-color: rgba(52, 152, 219, 0.1);
  color: #3498db;
  font-weight: 500;
}
.top-tours .tour-list {
  overflow-x: auto;
}
.top-tours .tour-table {
  width: 100%;
  border-collapse: collapse;
}
.top-tours .tour-table th {
  text-align: left;
  padding: 1rem;
  font-size: 1.2rem;
  font-weight: 600;
  color: #495057;
  border-bottom: 2px solid #e9ecef;
}
.top-tours .tour-table td {
  padding: 1rem;
  font-size: 1rem;
  border-bottom: 1px solid #e9ecef;
  vertical-align: middle;
  font-style: italic;
  color: #495057;
}
.top-tours .tour-table td img {
  width: 60px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
}
.top-tours .tour-table tr:hover td {
  background-color: #f8f9fa;
}

@media (max-width: 1200px) {
  .stats-cards {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media (max-width: 992px) {
  .dashboard-main {
    margin-left: 0;
    width: 100%;
  }
  .dashboard-charts {
    grid-template-columns: 1fr;
  }
}
@media (max-width: 768px) {
  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 576px) {
  .stats-cards {
    grid-template-columns: 1fr;
  }
}
.tour {
  margin-left: 240px;
  padding: 2rem;
  min-height: 100vh;
  min-width: calc(100% - 260px);
  overflow: hidden;
}
.tour__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: white;
  padding: 1.5rem 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  border: 1px solid #e9ecef;
}
.tour__search-section {
  margin-bottom: 2rem;
}
.tour__search-section .card {
  border: none !important;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: #ffffff;
}
.tour__search-section .card:hover {
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.12);
}
.tour__search-section .card .card-header {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%) !important;
  border: none !important;
  padding: 1.75rem 2rem;
  position: relative;
  overflow: hidden;
}
.tour__search-section .card .card-header .d-flex {
  position: relative;
  z-index: 2;
}
.tour__search-section .card .card-header .d-flex h6 {
  color: #ffffff;
  font-weight: 700;
  font-size: 1.3rem;
  margin: 0;
  display: flex;
  align-items: center;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}
.tour__search-section .card .card-header .d-flex h6 i {
  color: rgba(255, 255, 255, 0.95);
  font-size: 1.2rem;
  margin-right: 0.75rem;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}
.tour__search-section .card .card-header .d-flex h6 .badge {
  background: rgba(255, 255, 255, 0.25) !important;
  color: #ffffff;
  font-size: 0.75rem;
  padding: 0.35rem 0.8rem;
  border-radius: 20px;
  font-weight: 700;
  margin-left: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(10px);
  animation: pulseGlow 2s infinite;
}
.tour__search-section .card .card-header .d-flex h6 .badge[style*="display: none"] {
  display: none !important;
}
.tour__search-section .card .card-header .d-flex .btn-outline-secondary {
  background: rgba(255, 255, 255, 0.15);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: #ffffff;
  padding: 0.6rem 0.9rem;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(15px);
  position: relative;
  overflow: hidden;
}
.tour__search-section .card .card-header .d-flex .btn-outline-secondary::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  opacity: 0;
  transition: opacity 0.3s ease;
}
.tour__search-section .card .card-header .d-flex .btn-outline-secondary:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.6);
  color: #ffffff;
  transform: scale(1.08) translateY(-1px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}
.tour__search-section .card .card-header .d-flex .btn-outline-secondary:hover::before {
  opacity: 1;
}
.tour__search-section .card .card-header .d-flex .btn-outline-secondary:active {
  transform: scale(1.02);
}
.tour__search-section .card .card-header .d-flex .btn-outline-secondary i {
  transition: transform 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  font-size: 1rem;
}
.tour__search-section .card .card-header .d-flex .btn-outline-secondary.expanded i {
  transform: rotate(180deg);
}
.tour__search-section .card .card-body {
  padding: 2.5rem;
  background: #ffffff;
}
.tour__search-section .card .card-body#advancedFilterContent {
  opacity: 0;
  max-height: 0;
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 0 2.5rem;
  transform: translateY(-20px);
}
.tour__search-section .card .card-body#advancedFilterContent.show {
  opacity: 1;
  max-height: 2000px;
  padding: 2.5rem;
  transform: translateY(0);
}
.tour__search-section .card .card-body#advancedFilterContent[style*="display: none"] {
  display: none !important;
}
.tour__search-section .card .card-body#advancedFilterContent[style*="display: block"] {
  display: block !important;
  opacity: 1;
  max-height: 2000px;
  padding: 2.5rem;
  transform: translateY(0);
}
.tour__search-section .card .card-body #tourFilterForm .row {
  margin-bottom: 2rem;
}
.tour__search-section .card .card-body #tourFilterForm .row:last-child {
  margin-bottom: 0;
}
.tour__search-section .card .card-body #tourFilterForm .form-label {
  font-weight: 700;
  color: #040404;
  font-size: 0.95rem;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}
.tour__search-section .card .card-body #tourFilterForm .form-label i {
  color: #3498db;
  font-size: 0.9rem;
  width: 18px;
  margin-right: 0.6rem;
  filter: drop-shadow(0 1px 2px rgba(52, 152, 219, 0.3));
}
.tour__search-section .card .card-body #tourFilterForm .form-control,
.tour__search-section .card .card-body #tourFilterForm .form-select {
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 0.9rem 1.2rem;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: #ffffff;
  color: #6c757d;
  position: relative;
}
.tour__search-section .card .card-body #tourFilterForm .form-control:focus,
.tour__search-section .card .card-body #tourFilterForm .form-select:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 4px rgba(52, 152, 219, 0.12), 0 8px 25px rgba(52, 152, 219, 0.15);
  outline: none;
  transform: translateY(-2px);
  background: #fbfcfc;
}
.tour__search-section .card .card-body #tourFilterForm .form-control::placeholder,
.tour__search-section .card .card-body #tourFilterForm .form-select::placeholder {
  color: #6a6a6a;
  font-style: italic;
  font-weight: 400;
}
.tour__search-section .card .card-body #tourFilterForm .form-control:hover:not(:focus),
.tour__search-section .card .card-body #tourFilterForm .form-select:hover:not(:focus) {
  border-color: #75b9e7;
  background: #fefefe;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}
.tour__search-section .card .card-body #tourFilterForm .form-control:active,
.tour__search-section .card .card-body #tourFilterForm .form-select:active {
  transform: translateY(0);
}
.tour__search-section .card .card-body #tourFilterForm .input-group {
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}
.tour__search-section .card .card-body #tourFilterForm .input-group:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}
.tour__search-section .card .card-body #tourFilterForm .input-group .form-control {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
  box-shadow: none;
}
.tour__search-section .card .card-body #tourFilterForm .input-group .form-control:focus {
  z-index: 3;
  border-right: 2px solid #3498db;
  box-shadow: none;
}
.tour__search-section .card .card-body #tourFilterForm .input-group .form-control:hover {
  transform: none;
  box-shadow: none;
}
.tour__search-section .card .card-body #tourFilterForm .input-group .btn-primary {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  border: 2px solid #3498db;
  border-left: none;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 12px;
  border-bottom-right-radius: 12px;
  padding: 0.9rem 1.5rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}
.tour__search-section .card .card-body #tourFilterForm .input-group .btn-primary::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}
.tour__search-section .card .card-body #tourFilterForm .input-group .btn-primary:hover {
  background: linear-gradient(135deg, #2980b9 0%, #20638f 100%);
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
}
.tour__search-section .card .card-body #tourFilterForm .input-group .btn-primary:hover::before {
  left: 100%;
}
.tour__search-section .card .card-body #tourFilterForm .input-group .btn-primary:active {
  transform: scale(0.98);
}
.tour__search-section .card .card-body #tourFilterForm .input-group .btn-primary i {
  font-size: 1.1rem;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}
.tour__search-section .card .card-body #tourFilterForm input[type=date] {
  position: relative;
  cursor: pointer;
}
.tour__search-section .card .card-body #tourFilterForm input[type=date]::-webkit-calendar-picker-indicator {
  background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='%233498db' d='M3.5 0a.5.5 0 0 1 .5.5V1h8V.5a.5.5 0 0 1 1 0V1h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h1V.5a.5.5 0 0 1 .5-.5zM2 4v10a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1V4H2z'/%3e%3c/svg%3e") no-repeat center;
  background-size: 18px;
  cursor: pointer;
  opacity: 0.7;
  transition: all 0.3s ease;
  padding: 4px;
  border-radius: 4px;
}
.tour__search-section .card .card-body #tourFilterForm input[type=date]::-webkit-calendar-picker-indicator:hover {
  opacity: 1;
  background-color: rgba(52, 152, 219, 0.1);
  transform: scale(1.1);
}
.tour__search-section .card .card-body .quick-filter-buttons .btn {
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-weight: 700;
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  border: 2px solid transparent;
}
.tour__search-section .card .card-body .quick-filter-buttons .btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}
.tour__search-section .card .card-body .quick-filter-buttons .btn:hover::before {
  left: 100%;
}
.tour__search-section .card .card-body .quick-filter-buttons .btn:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}
.tour__search-section .card .card-body .quick-filter-buttons .btn:active {
  transform: translateY(-1px) scale(1);
}
.tour__search-section .card .card-body .quick-filter-buttons .btn.filter-btn--apply {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  border: 2px solid #2980b9;
  color: #ffffff;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}
.tour__search-section .card .card-body .quick-filter-buttons .btn.filter-btn--apply:hover {
  background: linear-gradient(135deg, #2980b9 0%, #20638f 100%);
  box-shadow: 0 12px 35px rgba(52, 152, 219, 0.4);
  border-color: #2472a4;
}
.tour__search-section .card .card-body .quick-filter-buttons .btn.filter-btn--apply.active {
  background: linear-gradient(135deg, #2472a4 0%, #1b557a 100%);
  box-shadow: inset 0 3px 7px rgba(0, 0, 0, 0.2);
}
.tour__search-section .card .card-body .quick-filter-buttons .btn.filter-btn--expiring {
  background: linear-gradient(135deg, #f39c12 0%, #c87f0a 100%);
  border: 2px solid #e08e0b;
  color: #ffffff;
  box-shadow: 0 4px 15px rgba(241, 196, 15, 0.3);
}
.tour__search-section .card .card-body .quick-filter-buttons .btn.filter-btn--expiring:hover {
  background: linear-gradient(135deg, #e08e0b 0%, #b06f09 100%);
  box-shadow: 0 12px 35px rgba(241, 196, 15, 0.4);
}
.tour__search-section .card .card-body .quick-filter-buttons .btn.filter-btn--expired {
  background: linear-gradient(135deg, #e74c3c 0%, #d62c1a 100%);
  border: 2px solid #e43725;
  color: #ffffff;
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}
.tour__search-section .card .card-body .quick-filter-buttons .btn.filter-btn--expired:hover {
  background: linear-gradient(135deg, #e43725 0%, #bf2718 100%);
  box-shadow: 0 12px 35px rgba(231, 76, 60, 0.4);
}
.tour__search-section .card .card-body .quick-filter-buttons .btn.filter-btn--featured {
  background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
  border: 2px solid #148ea1;
  color: #ffffff;
  box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
}
.tour__search-section .card .card-body .quick-filter-buttons .btn.filter-btn--featured:hover {
  background: linear-gradient(135deg, #148ea1 0%, #0f6674 100%);
  box-shadow: 0 12px 35px rgba(23, 162, 184, 0.4);
}
.tour__search-section .card .card-body .quick-filter-buttons .btn.filter-btn--active {
  background: linear-gradient(135deg, #27ae60 0%, #1e8449 100%);
  border: 2px solid #229955;
  color: #ffffff;
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}
.tour__search-section .card .card-body .quick-filter-buttons .btn.filter-btn--active:hover {
  background: linear-gradient(135deg, #229955 0%, #19703e 100%);
  box-shadow: 0 12px 35px rgba(39, 174, 96, 0.4);
}
.tour__search-section .card .card-body .quick-filter-buttons .btn.filter-btn--clear {
  background: linear-gradient(135deg, #6c757d 0%, #545b62 100%);
  border: 2px solid #60686f;
  color: #ffffff;
  box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}
.tour__search-section .card .card-body .quick-filter-buttons .btn.filter-btn--clear:hover {
  background: linear-gradient(135deg, #60686f 0%, #494f54 100%);
  box-shadow: 0 12px 35px rgba(108, 117, 125, 0.4);
}
.tour__search-section .card .card-body .quick-filter-buttons .btn.loading {
  pointer-events: none;
  position: relative;
  color: transparent;
}
.tour__search-section .card .card-body .quick-filter-buttons .btn.loading::after {
  content: "";
  position: absolute;
  width: 18px;
  height: 18px;
  top: 50%;
  left: 50%;
  margin-left: -9px;
  margin-top: -9px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  color: white;
}
.tour__title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.tour__actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}
.tour__btn--delete-selected {
  animation: slideIn 0.3s ease-out;
}
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
.tour__btn {
  padding: 0.75rem 2.5rem;
  border: 1px solid transparent;
  outline: none;
  border-radius: 5px;
  font-weight: 500;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.2rem;
  line-height: 1.5;
}
.tour__btn:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.tour__btn--primary {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
  transition: all 0.3s ease;
}
.tour__btn--primary:hover {
  background: #f8f9fa;
  color: #040404;
  border-color: #3498db;
}
.tour__btn--secondary {
  background: #6c757d;
  color: white;
}
.tour__btn--secondary:hover {
  background: #5a6268;
  color: white;
}
.tour__btn--warning {
  border: 1px solid #ccc;
}
.tour__btn--warning:hover {
  background: #ffffff;
  color: #959595;
}
.tour__btn--danger {
  border: 1px solid #e53935;
  color: #e53935;
}
.tour__btn--danger:hover {
  background: #ff5252;
  color: white;
}
.tour__btn--sm {
  padding: 0.8rem 1rem;
  font-size: 0.8rem;
}
.tour__table--wrapper {
  background: white;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  overflow-x: auto;
  border-radius: 12px;
  margin-top: 1rem;
  width: 100%;
  border: 1px solid rgba(52, 152, 219, 0.08);
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: #3498db #f1f1f1;
}
.tour__table--wrapper::-webkit-scrollbar {
  height: 8px;
}
.tour__table--wrapper::-webkit-scrollbar-track {
  background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 4px;
}
.tour__table--wrapper::-webkit-scrollbar-thumb {
  background: linear-gradient(90deg, #3498db 0%, #2980b9 100%);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}
.tour__table--wrapper::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(90deg, #2980b9 0%, #20638f 100%);
}
.tour__table {
  width: 100%;
  margin: 0;
  background: white;
  font-size: 1.2rem;
  table-layout: auto;
}
.tour__table thead th {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
  padding: 1rem;
  text-align: center;
  font-weight: 700;
  font-size: 1.1rem;
  white-space: nowrap;
  border: none;
  position: relative;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  letter-spacing: 0.3px;
}
.tour__table tbody tr {
  transition: all 0.3s ease;
  border-bottom: 1px solid #e9ecef;
}
.tour__table tbody tr:hover {
  background: linear-gradient(135deg, #f8fafe 0%, #eaf6fb 100%);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.08);
}
.tour__table tbody tr:nth-child(even) {
  background-color: rgba(248, 250, 254, 0.3);
}
.tour__table tbody tr:nth-child(even):hover {
  background: linear-gradient(135deg, #f8fafe 0%, #eaf6fb 100%);
}
.tour__table tbody tr:last-child {
  border-bottom: none;
}
.tour__table tbody td {
  padding: 2rem 0.8rem;
  vertical-align: middle;
  text-align: center;
  border: none;
  font-size: 1.2rem;
  color: #2c3e50;
  line-height: 1.4;
}
.tour__table tbody td strong {
  white-space: nowrap;
}
.tour__table tbody td:nth-child(1) {
  font-weight: 600;
  color: #6c757d;
  font-size: 1.1rem;
}
.tour__table tbody td:nth-child(2) {
  padding-left: 1rem;
}
.tour__table tbody td:nth-child(2) .tour-title {
  font-weight: 700;
  color: #2c3e50;
  line-height: 1.3;
  font-size: 1.1rem;
}
.tour__table tbody td:nth-child(4) .badge {
  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
  color: white;
  font-weight: 600;
  padding: 0.5rem 0.8rem;
  border-radius: 8px;
  font-size: 0.9rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(108, 117, 125, 0.2);
}
.tour__table tbody td:nth-child(7) .text-primary {
  color: #e53935 !important;
  font-weight: 700;
  font-size: 1.1rem;
}
.tour__table tbody td:nth-child(7) .text-info {
  color: #3498db !important;
  font-size: 0.9rem;
  font-weight: 500;
}
.tour__table tbody td:nth-child(8) .badge {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
  font-weight: 600;
  padding: 0.4rem 0.7rem;
  border-radius: 6px;
  font-size: 0.8rem;
}
.tour__table tbody td:nth-child(9) small, .tour__table tbody td:nth-child(10) small, .tour__table tbody td:nth-child(11) small {
  color: #495057;
  font-size: 1rem;
  font-weight: 500;
}
.tour__table tbody td:nth-child(12) small, .tour__table tbody td:nth-child(13) small {
  color: #495057;
  font-size: 1rem;
  font-weight: 600;
}
.tour__table tbody td:nth-child(14) .btn-group {
  display: flex;
  gap: 0.3rem;
  justify-content: center;
}
.tour__table tbody td:nth-child(14) .btn-group .btn {
  transition: all 0.3s ease;
  border-radius: 6px;
  font-weight: 600;
}
.tour__table tbody td:nth-child(14) .btn-group .btn:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.tour__table tbody td:nth-child(14) .btn-group .btn.btn-outline-info {
  border-color: #3498db;
  color: #3498db;
}
.tour__table tbody td:nth-child(14) .btn-group .btn.btn-outline-info:hover {
  background: #3498db;
  border-color: #3498db;
  color: white;
}
.tour__table tbody td:nth-child(14) .btn-group .btn.btn-outline-primary {
  border-color: #28a745;
  color: #28a745;
}
.tour__table tbody td:nth-child(14) .btn-group .btn.btn-outline-primary:hover {
  background: #28a745;
  border-color: #28a745;
  color: white;
}
.tour__table tbody td:nth-child(14) .btn-group .btn.btn-outline-danger {
  border-color: #dc3545;
  color: #dc3545;
  padding: 0.3rem 0.6rem;
}
.tour__table tbody td:nth-child(14) .btn-group .btn.btn-outline-danger:hover {
  background: #dc3545;
  border-color: #dc3545;
  color: white;
}
.tour__table .tour-image {
  width: 70px;
  height: 50px;
  object-fit: cover;
  border-radius: 8px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.12);
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.8);
}
.tour__table .tour-image:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}
.tour__table .tour-image-placeholder {
  width: 70px;
  height: 50px;
  border-radius: 8px;
  border: 2px dashed #cbd5e0;
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #a0aec0;
  transition: all 0.3s ease;
  margin: 0 auto;
}
.tour__table .tour-image-placeholder:hover {
  background: linear-gradient(135deg, #edf2f7 0%, #e2e8f0 100%);
  border-color: #a0aec0;
  color: #718096;
}
.tour__table .tour-image-placeholder i {
  font-size: 1.2rem;
}
.tour__table-empty {
  text-align: center;
  color: #6c757d;
  padding: 4rem 2rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}
.tour__table-empty .empty-state i {
  color: #bdc3c7;
  margin-bottom: 1rem;
}
.tour__table-empty .empty-state h5 {
  color: #6c757d;
  font-weight: 500;
  margin-bottom: 0;
}
.tour__badge {
  padding: 0.5rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  letter-spacing: 0.5px;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  border: none;
  cursor: default;
  transition: all 0.3s ease;
  white-space: nowrap;
}
.tour__badge--success {
  background: #3498db;
  color: #fff;
}
.tour__badge--inactive {
  background: #dc3545;
  color: #fff;
}
.tour__badge--toggle {
  cursor: pointer;
  transition: all 0.3s ease;
}
.tour__badge--toggle:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.tour__badge--toggle:active {
  transform: translateY(0);
}
.tour__badge--toggle:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}
.tour .price-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.25rem;
}
.tour .price-info .text-primary {
  font-size: 1rem;
  font-weight: 600;
}
.tour .price-info .text-muted {
  font-size: 0.8rem;
  font-weight: 400;
}
.tour .text-info {
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  margin-top: 0.25rem;
}
.tour .text-info i {
  font-size: 0.7rem;
}
.tour__form--delete {
  display: inline-block;
}
.tour .status-changing {
  animation: pulse 0.5s ease-in-out;
}
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}
.tour__items-per-page {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  z-index: 1050;
}
.tour__items-per-page .form-label {
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 0;
  white-space: nowrap;
  color: #495057;
}
.tour__items-per-page .form-select {
  border-radius: 8px;
  border: 1px solid #ced4da;
  min-width: 85px;
  font-size: 0.875rem;
  padding: 0.375rem 1.75rem 0.375rem 0.5rem;
  transition: all 0.2s ease;
  position: relative;
  z-index: 1051;
}
.tour__items-per-page .form-select:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
  z-index: 1052;
}
.tour__items-per-page .form-select:disabled {
  opacity: 0.7;
  background-color: #f8f9fa;
}
.tour__items-per-page span {
  font-size: 0.875rem;
  color: #6c757d;
  white-space: nowrap;
}
.tour__pagination {
  margin-top: 2rem;
}
.tour__pagination .pagination .page-item {
  margin: 0 2px;
}
.tour__pagination .pagination .page-item .page-link {
  border-radius: 6px;
  border: 1px solid #dee2e6;
  color: #3498db;
  padding: 0.5rem 0.75rem;
  font-weight: 500;
  transition: all 0.2s ease;
  min-width: 40px;
  text-align: center;
  background-color: #fff;
}
.tour__pagination .pagination .page-item .page-link:hover {
  background-color: #e3f2fd;
  border-color: #3498db;
  color: #3498db;
  text-decoration: none;
}
.tour__pagination .pagination .page-item .page-link:focus {
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
  z-index: 3;
}
.tour__pagination .pagination .page-item.active .page-link {
  background-color: #3498db;
  border-color: #3498db;
  color: white;
  font-weight: 600;
}
.tour__pagination .pagination .page-item.active .page-link:hover {
  background-color: #3498db;
  border-color: #3498db;
  color: white;
}
.tour__pagination .pagination .page-item.disabled .page-link {
  color: #6c757d;
  background-color: #fff;
  border-color: #dee2e6;
  cursor: not-allowed;
  opacity: 0.65;
}
.tour__pagination .pagination .page-item.disabled .page-link:hover {
  background-color: #fff;
  border-color: #dee2e6;
  color: #6c757d;
}
.tour__pagination .pagination .page-item:first-child .page-link,
.tour__pagination .pagination .page-item:last-child .page-link {
  font-weight: 500;
  font-size: 1rem;
  padding: 0.5rem 0.75rem;
  min-width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.tour__pagination .pagination .page-item:first-child .page-link:hover,
.tour__pagination .pagination .page-item:last-child .page-link:hover {
  background-color: #e3f2fd;
  border-color: #3498db;
  color: #3498db;
  text-decoration: none;
  transform: none;
  box-shadow: none;
}
.tour__pagination-info {
  margin-top: 1.5rem;
}
.tour__pagination-info small {
  font-size: 0.875rem;
  color: #6c757d;
  display: block;
  margin-bottom: 0.5rem;
}
.tour__quick-jump .form-control {
  border-radius: 8px;
  border: 1px solid #ced4da;
  text-align: center;
  font-weight: 500;
}
.tour__quick-jump .form-control:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}
.tour__quick-jump .btn {
  border-radius: 8px;
  font-weight: 500;
}
.tour__quick-jump .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.2);
}
.tour__quick-jump small {
  font-weight: 500;
  color: #495057;
}
@media (max-width: 768px) {
  .tour {
    padding: 1rem;
  }
  .tour__header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  .tour__title {
    font-size: 1.5rem;
  }
  .tour__table--wrapper {
    overflow-x: auto;
  }
  .tour__table {
    min-width: 600px;
  }
  .tour__table thead th,
  .tour__table tbody td {
    padding: 0.75rem;
    font-size: 0.8rem;
  }
  .tour__btn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }
  .tour__btn--sm {
    padding: 0.4rem 0.8rem;
    font-size: 0.75rem;
  }
}
.tour .tour-checkbox,
.tour #selectAll {
  width: 18px;
  height: 18px;
  cursor: pointer;
  margin: 0;
  border: 1px solid #ccc;
}
.tour .tour-checkbox:focus,
.tour #selectAll:focus {
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}
.tour th:first-child,
.tour td:first-child {
  text-align: center;
  padding: 0.75rem 0.5rem;
}
.tour th:first-child input[type=checkbox],
.tour td:first-child input[type=checkbox] {
  margin: 0 auto;
  display: block;
}
.tour #selectAll:indeterminate {
  background-color: #3498db;
  border-color: #3498db;
}
.tour #selectAll:indeterminate::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 2px;
  background-color: white;
}
.tour #deleteSelectedBtn {
  transition: all 0.3s ease;
}
.tour #deleteSelectedBtn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.tour #deleteSelectedBtn:not(:disabled) {
  background-color: #dc3545;
  border-color: #dc3545;
  color: white;
}
.tour #deleteSelectedBtn:not(:disabled):hover {
  background-color: #c82333;
  border-color: #bd2130;
}
.tour .tour__status-toggle,
.tour .tour__highlight-toggle {
  transition: all 0.3s ease;
  border: none;
  font-size: 0.875rem;
  padding: 1rem;
  white-space: nowrap;
  border-radius: 20px;
}
.tour .tour__status-toggle:hover,
.tour .tour__highlight-toggle:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.tour .tour__status-toggle {
  min-width: 85px;
  width: 85px;
  text-align: center;
  padding: 0.4rem 0.5rem;
}
.tour .tour__status-toggle.btn-success {
  background-color: #28a745;
  color: white;
  border: 1px solid #28a745;
}
.tour .tour__status-toggle.btn-secondary {
  background-color: #dc3545;
  color: white;
  border: 1px solid #dc3545;
}
.tour .tour__highlight-toggle {
  min-width: 85px;
  width: 85px;
  text-align: center;
  padding: 0.4rem 0.5rem;
}
.tour .tour__highlight-toggle.btn-warning {
  background-color: #f1c40f;
  color: #2c3e50;
  border: 1px solid #f1c40f;
}
.tour .tour__highlight-toggle.btn-outline-warning {
  border-color: #e3f2fd;
  color: #2c3e50;
  background-color: #e3f2fd;
  border: 1px solid #e3f2fd;
}
.tour tbody tr {
  transition: background-color 0.2s ease;
}
.tour tbody tr:hover {
  background-color: #eaf6fb;
}
.tour .tour-row--selected {
  background-color: #e7f3ff;
}
.tour .tour-row--selected:hover {
  background-color: #d1ecf1;
}
.tour .tour-row--deleting {
  position: relative;
}
.tour .tour-row--deleting::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(220, 53, 69, 0.1);
  z-index: 1;
  animation: deleteProgress 2s infinite;
}
.tour .tour-row--deleting td {
  position: relative;
  z-index: 2;
  opacity: 0.6;
}
@keyframes deleteProgress {
  0% {
    background: rgba(220, 53, 69, 0.05);
  }
  50% {
    background: rgba(220, 53, 69, 0.15);
  }
  100% {
    background: rgba(220, 53, 69, 0.05);
  }
}

.tour-form {
  background: white;
  border-radius: 12px;
  box-shadow: 0px 0px 30px 9px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin: 2rem auto;
}
.tour-form__header {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
  padding: 2rem;
  text-align: left;
}
.tour-form__header h3 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
}
.tour-form__body {
  padding: 2rem;
}
.tour-form__body .section-title {
  font-weight: 600;
  color: #2c3e50;
}
.tour-form .form-label {
  font-size: 1.4rem;
  font-weight: 500;
  color: #2c3e50;
  margin: 1rem 0;
  display: block;
}
.tour-form .form-control,
.tour-form .form-select {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}
.tour-form .form-control:focus,
.tour-form .form-select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  outline: none;
}
.tour-form .btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1.2rem;
  font-weight: 500;
  text-decoration: none;
  background: #3498db;
  color: #ffffff;
  transition: all 0.3s ease;
}
.tour-form .btn:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: transparent;
  color: #040404;
  border-color: #3498db;
}
.tour-form .btn-primary {
  background: #3498db;
  border: 1px solid #3498db;
}
.tour-form .btn-primary:hover {
  color: #040404;
  background: transparent;
}
.tour-form .btn-secondary {
  background: #6c757d;
  border: none;
}
.tour-form .btn-secondary:hover {
  background: #5a6268;
  color: white;
}
.tour-form .input-group .btn-outline-secondary {
  border-color: #ced4da;
  color: white;
}
.tour-form .input-group .btn-outline-secondary:hover {
  background-color: #ffffff;
  border-color: #3498db;
  color: #040404;
}
.tour-form .input-group .btn-outline-secondary:focus {
  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.25);
}
.tour-form #codeMessage {
  font-size: 0.875rem;
  margin-top: 0.25rem;
}
.tour-form #codeMessage.text-danger {
  color: #dc3545 !important;
}
.tour-form #codeMessage.text-success {
  color: #198754 !important;
}
.tour-form .form-control.is-invalid {
  border-color: #dc3545;
}
.tour-form .form-control.is-invalid:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}
.tour-form .form-control.is-valid {
  border-color: #198754;
}
.tour-form .form-control.is-valid:focus {
  border-color: #198754;
  box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
}
.tour-form .price-block {
  background: #f8f9fa;
  border: 2px solid #e9ecef !important;
  border-radius: 12px !important;
  padding: 1.5rem !important;
  margin-bottom: 1.5rem !important;
  transition: all 0.3s ease;
}
.tour-form .price-block:hover {
  border-color: #dee2e6 !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
.tour-form .price-block-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.tour-form .price-block .btn-outline-danger {
  border-color: #3498db;
  color: white;
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}
.tour-form .price-block .btn-outline-danger:hover {
  background-color: transparent;
  color: #040404;
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}
.tour-form .price-block .btn-outline-danger:focus {
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}
.tour-form .price-block .btn-outline-danger i {
  font-size: 0.875rem;
}
.tour-form .price-block .btn-outline-danger {
  opacity: 1;
  transition: opacity 0.3s ease, transform 0.3s ease;
}
.tour-form .price-block .btn-outline-danger[style*="display: none"] {
  opacity: 0;
}
.tour-form .price-block h5 {
  color: #495057;
  font-weight: 600;
  margin-bottom: 0;
}
.tour-form .price-block.removing {
  opacity: 0;
  transition: opacity 0.3s ease, transform 0.3s ease;
}
.tour-form .single-block-message {
  background-color: #e3f2fd;
  border: 1px solid #2196f3;
  border-radius: 8px;
  padding: 0.75rem 1rem;
  margin-top: 1rem;
  font-size: 0.875rem;
  color: #1976d2;
}
.tour-form .single-block-message i {
  color: #2196f3;
}
.tour-form .single-block-message small {
  margin: 0;
  line-height: 1.4;
}
.tour-form .itinerary-item {
  background: #f8f9fa;
  border: 2px solid #e9ecef !important;
  border-radius: 12px !important;
  padding: 1.5rem !important;
  margin-bottom: 1.5rem !important;
  transition: all 0.3s ease;
}
.tour-form .itinerary-item:hover {
  border-color: #dee2e6 !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
.tour-form .itinerary-item .itinerary-delete-btn {
  opacity: 1;
  transition: opacity 0.3s ease, transform 0.3s ease;
}
.tour-form .itinerary-item .itinerary-delete-btn[style*="display: none"] {
  opacity: 0;
}
.tour-form .itinerary-item.removing {
  opacity: 0;
  transition: opacity 0.3s ease, transform 0.3s ease;
}
.tour-form .form-control:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
  outline: 0;
}
.tour-form .form-control[name*=Price], .tour-form .form-control[name*=price] {
  font-weight: 500;
}
.tour-form .form-control[name*=Price]::placeholder, .tour-form .form-control[name*=price]::placeholder {
  color: #6c757d;
  opacity: 0.7;
}
.tour-form .form-control[name*=discount] {
  font-weight: 500;
}
.tour-form .form-control[name*=discount]::placeholder {
  color: #6c757d;
  opacity: 0.7;
}
.tour-form .form-control[name*=stock] {
  font-weight: 500;
}
.tour-form .form-control[name*=stock]::placeholder {
  color: #6c757d;
  opacity: 0.7;
}
.tour-form .form-control.is-invalid {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}
.tour-form .form-control.is-valid {
  border-color: #28a745;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}
.tour-form select option {
  z-index: 9999 !important;
}
.tour-form:focus-within {
  z-index: 1060;
}
.tour-form .btn-outline-info {
  border-color: #3498db;
  color: #3498db;
  background-color: transparent;
  transition: all 0.3s ease;
  border-radius: 6px;
}
.tour-form .btn-outline-info:hover {
  background-color: #3498db;
  border-color: #3498db;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(52, 152, 219, 0.2);
}
.tour-form .btn-outline-info:focus {
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}
.tour-form .btn-outline-primary {
  border-color: #3498db;
  color: #ffffff;
  background-color: #3498db;
  transition: all 0.3s ease;
  border-radius: 6px;
}
.tour-form .btn-outline-primary:hover {
  background-color: transparent;
  border-color: #3498db;
  color: #3498db;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(52, 152, 219, 0.2);
}
.tour-form .btn-outline-primary:focus {
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}
.tour-form .btn-outline-danger {
  border-color: #dc3545;
  color: #ffffff;
  background-color: #dc3545;
  transition: all 0.3s ease;
  border-radius: 6px;
}
.tour-form .btn-outline-danger i {
  font-size: 1rem;
}
.tour-form .btn-outline-danger:hover {
  background-color: transparent;
  border-color: #dc3545;
  color: #dc3545;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.2);
}
.tour-form .badge.bg-primary {
  background-color: #3498db !important;
  color: white !important;
}
.tour-form .badge.bg-secondary {
  background-color: #3498db !important;
  color: white !important;
}
.tour-form .badge.bg-info {
  background-color: #3498db !important;
  color: white !important;
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.8);
  }
}
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    max-height: 1000px;
    transform: translateY(0);
  }
}
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@media (max-width: 768px) {
  .tour__search-section .card {
    border-radius: 12px;
    margin: 0 1rem;
  }
  .tour__search-section .card .card-header {
    padding: 1.5rem;
  }
  .tour__search-section .card .card-header .d-flex {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  .tour__search-section .card .card-header .d-flex h6 {
    font-size: 1.1rem;
    justify-content: center;
  }
  .tour__search-section .card .card-header .d-flex h6 .badge {
    margin-left: 0.5rem;
    margin-top: 0.25rem;
  }
  .tour__search-section .card .card-header .d-flex .btn-outline-secondary {
    align-self: center;
    padding: 0.5rem 1rem;
  }
  .tour__search-section .card .card-body {
    padding: 1.5rem;
  }
  .tour__search-section .card .card-body#advancedFilterContent.show {
    padding: 1.5rem;
  }
  .tour__search-section .card .card-body #tourFilterForm .row {
    margin-bottom: 1.25rem;
  }
  .tour__search-section .card .card-body #tourFilterForm .form-label {
    font-size: 0.85rem;
    margin-bottom: 0.6rem;
  }
  .tour__search-section .card .card-body #tourFilterForm .form-control,
  .tour__search-section .card .card-body #tourFilterForm .form-select {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    border-radius: 10px;
  }
  .tour__search-section .card .card-body #tourFilterForm .input-group .btn-primary {
    padding: 0.75rem 1.25rem;
  }
  .tour__search-section .card .card-body .quick-filter-buttons {
    flex-direction: column;
    gap: 1rem !important;
  }
  .tour__search-section .card .card-body .quick-filter-buttons .btn {
    width: 100%;
    padding: 1rem;
    font-size: 0.9rem;
    justify-content: center;
  }
}
@media (max-width: 576px) {
  .tour__search-section .card {
    margin: 0 0.5rem;
    border-radius: 10px;
  }
  .tour__search-section .card .card-header {
    padding: 1.25rem;
  }
  .tour__search-section .card .card-body {
    padding: 1.25rem;
  }
  .tour__search-section .card .card-body#advancedFilterContent.show {
    padding: 1.25rem;
  }
  .tour__search-section .card .card-body .form-control,
  .tour__search-section .card .card-body .form-select {
    padding: 0.65rem 0.9rem;
    font-size: 0.85rem;
  }
}
.tour__search-section .card .form-control:focus,
.tour__search-section .card .form-select:focus,
.tour__search-section .card .btn:focus {
  outline: 3px solid rgba(52, 152, 219, 0.3);
  outline-offset: 2px;
}
.tour__search-section .card .btn:focus:not(:focus-visible) {
  outline: none;
}

@media (prefers-reduced-motion: reduce) {
  .tour__search-section .card {
    transition: none;
  }
  .tour__search-section .card:hover {
    transform: none;
  }
  .tour__search-section .card .card-header::before,
  .tour__search-section .card .btn::before {
    display: none;
  }
  .tour__search-section .card .btn:hover {
    transform: none;
  }
  .tour__search-section .card #advancedFilterContent {
    transition: opacity 0.2s ease;
  }
}
.tour-detail {
  margin-left: 240px;
  padding: 2rem;
  height: 100%;
  min-width: calc(100% - 260px);
}
.tour-detail__btn {
  padding: 0.75rem 2.5rem;
  border: 1px solid transparent;
  outline: none;
  border-radius: 5px;
  font-weight: 500;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.2rem;
  line-height: 1.5;
}
.tour-detail__btn:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  text-decoration: none;
}
.tour-detail__btn--primary {
  background: #3498db;
  color: white;
}
.tour-detail__btn--primary:hover {
  background: #f8f9fa;
  color: #040404;
  border-color: #3498db;
}
.tour-detail__btn--secondary {
  background: #6c757d;
  color: white;
}
.tour-detail__btn--secondary:hover {
  background: #5a6268;
  color: white;
}
.tour-detail__btn--warning {
  border: 1px solid #ccc;
  background: #ffc107;
  color: #212529;
}
.tour-detail__btn--warning:hover {
  background: #ffffff;
  color: #959595;
}
.tour-detail__btn--danger {
  border: 1px solid #e53935;
  color: #e53935;
}
.tour-detail__btn--danger:hover {
  background: #ff5252;
  color: white;
}
.tour-detail__btn--sm {
  padding: 0.8rem 1rem;
  font-size: 0.8rem;
}
.tour-detail__btn--add {
  padding: 0.75rem 2.5rem;
  font-size: 1.2rem;
}
.tour-detail__table--wrapper {
  background: white;
  box-shadow: 0px 0px 30px 9px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border-radius: 10px;
  margin-bottom: 2rem;
}
.tour-detail__table {
  width: 100%;
  margin: 0;
  background: white;
}
.tour-detail__table th {
  background: #3498db;
  color: white;
  padding: 2rem;
  text-align: center;
  font-weight: 600;
  font-size: 1.2rem;
}
.tour-detail__table td {
  padding: 1.5rem;
  border-bottom: 1px solid #e9ecef;
  vertical-align: middle;
  font-size: 1.2rem;
  text-align: center;
  background: white;
}
.tour-detail__table td:nth-child(3) {
  text-align: center;
}
.tour-detail__section-title {
  color: #2c3e50;
  font-weight: 600;
  font-size: 2rem;
  margin-bottom: 30px;
  margin-top: 45px;
  text-align: center;
}
.tour-detail__badge {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}
.tour-detail__badge--active {
  background-color: #d4edda;
  color: #155724;
}
.tour-detail__badge--inactive {
  background-color: #f8d7da;
  color: #721c24;
}
.tour-detail__additional-info {
  margin-bottom: 50px;
}
.tour-detail__info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
  margin-top: 30px;
}
.tour-detail__info-card {
  display: flex;
  align-items: center;
  padding: 1rem;
  background: #ffffff;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border-left: 5px solid #3498db;
  position: relative;
  overflow: hidden;
}
.tour-detail__info-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 123, 255, 0.03) 0%, rgba(0, 86, 179, 0.03) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}
.tour-detail__info-card > * {
  position: relative;
  z-index: 2;
}
.tour-detail__info-card-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #3498db;
  border-radius: 50%;
  margin-right: 20px;
  transition: all 0.3s ease;
}
.tour-detail__info-card-icon i {
  color: white;
  font-size: 1.4rem;
  transition: transform 0.3s ease;
}
.tour-detail__info-card-content {
  flex: 1;
}
.tour-detail__info-card-title {
  color: #2c3e50;
  font-weight: 600;
  font-size: 1.4rem;
  margin-bottom: 5px;
}
.tour-detail__info-card-desc {
  color: #6c757d;
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}
.tour-detail__info-card-desc .text-muted {
  color: #94a3b8 !important;
  font-style: italic;
}
.tour-detail__info-card-desc .text-muted i {
  color: #cbd5e1;
  margin-right: 6px;
}
.tour-detail__info-card-desc .text-muted:hover {
  color: #64748b !important;
}
.tour-detail__info-card-desc .text-muted:hover i {
  color: #94a3b8;
}
.tour-detail__itinerary {
  margin-bottom: 50px;
}
.tour-detail__itinerary-list {
  margin-top: 30px;
}
.tour-detail__itinerary-item {
  margin-bottom: 20px;
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  overflow: hidden;
}
.tour-detail__itinerary-item:hover {
  box-shadow: 0 15px 25px rgba(0, 0, 0, 0.13);
}
.tour-detail__itinerary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1px solid #e9ecef;
}
.tour-detail__itinerary-header:hover {
  background: linear-gradient(135deg, rgba(52, 152, 219, 0.1) 0%, rgba(4, 4, 4, 0.1) 100%);
}
.tour-detail__itinerary-header.active {
  background: #3498db;
  color: white;
}
.tour-detail__itinerary-header.active .tour-detail__itinerary-day {
  color: white;
}
.tour-detail__itinerary-header.active .tour-detail__itinerary-day::before {
  color: white;
}
.tour-detail__itinerary-header.active .tour-detail__itinerary-icon {
  color: white;
}
.tour-detail__itinerary-day {
  font-weight: 600;
  font-size: 1.4rem;
  color: #040404;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: color 0.3s ease;
}
.tour-detail__itinerary-day::before {
  content: "\f017";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  font-size: 1.4rem;
  color: #3498db;
  transition: color 0.3s ease;
}
.tour-detail__itinerary-icon {
  font-size: 1.2rem;
  color: #3498db;
  transition: all 0.3s ease;
}
.tour-detail__itinerary-icon i {
  transition: transform 0.3s ease;
}
.tour-detail__itinerary-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}
.tour-detail__itinerary-content.active {
  max-height: 500px;
}
.tour-detail__itinerary-body {
  padding: 25px;
  background: #ffffff;
  border-top: 1px solid #f1f3f4;
}
.tour-detail__itinerary-desc {
  color: #6c757d;
  line-height: 1.6;
  margin: 0;
  font-size: 1.2rem;
}
.tour-detail__images {
  margin-bottom: 50px;
}
.tour-detail__images-grid {
  display: grid;
  grid-template-columns: repeat(2, 40%);
  justify-content: space-around;
  margin-top: 30px;
}
.tour-detail__image-item {
  overflow: hidden;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}
.tour-detail__image {
  width: 100%;
  height: 300px;
  object-fit: cover;
  transition: transform 0.3s ease;
}
.tour-detail__pricing {
  margin-bottom: 30px;
}
.tour-detail__empty-state {
  text-align: center;
  padding: 60px 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 2px dashed #dee2e6;
}
.tour-detail__empty-state i {
  font-size: 48px;
  color: #6c757d;
  margin-bottom: 20px;
  display: block;
}
.tour-detail__empty-state h3 {
  color: #495057;
  margin-bottom: 10px;
  font-weight: 600;
}
.tour-detail__empty-state p {
  color: #6c757d;
  margin: 0;
  font-size: 14px;
}
.tour-detail__table-wrapper {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}
.tour-detail__table {
  width: 100%;
  border-collapse: collapse;
  margin: 0;
}
.tour-detail__table-head {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}
.tour-detail__table-th {
  padding: 18px 15px;
  text-align: left;
  font-weight: 600;
  color: #495057;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: none;
  border-bottom: 2px solid #dee2e6;
}
.tour-detail__table-row {
  transition: all 0.3s ease;
}
.tour-detail__table-row:hover {
  background-color: #f8f9fa;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.tour-detail__table-row:not(:last-child) {
  border-bottom: 1px solid #f1f3f4;
}
.tour-detail__table-td {
  padding: 18px 15px;
  vertical-align: middle;
  border: none;
  color: #495057;
  font-size: 14px;
}
.tour-detail__table-actions {
  display: flex;
  gap: 8px;
}
.tour-detail__btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  line-height: 1;
}
.tour-detail__btn i {
  margin-right: 8px;
  font-size: 12px;
}
.tour-detail__btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  text-decoration: none;
}
.tour-detail__btn--primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
}
.tour-detail__btn--primary:hover {
  color: white;
}
.tour-detail__btn--secondary {
  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
  color: white;
}
.tour-detail__btn--secondary:hover {
  color: white;
}
.tour-detail__btn--warning {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
  color: #212529;
}
.tour-detail__btn--warning:hover {
  color: #212529;
}
.tour-detail__btn--success {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
}
.tour-detail__btn--success:hover {
  color: white;
}
.tour-detail__btn--danger {
  background: linear-gradient(135deg, #dc3545 0%, #bd2130 100%);
  color: white;
}
.tour-detail__btn--danger:hover {
  color: white;
}
.tour-detail__btn--sm {
  padding: 8px 12px;
  font-size: 12px;
}
.tour-detail__btn--sm i {
  margin-right: 4px;
  font-size: 10px;
}
.tour-detail__modal .modal-content {
  border: none;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}
.tour-detail__modal .modal-header {
  border-bottom: 1px solid #f1f3f4;
  padding: 20px 25px;
}
.tour-detail__modal .modal-header .modal-title {
  font-weight: 600;
  color: #2c3e50;
}
.tour-detail__modal .modal-body {
  padding: 25px;
}
.tour-detail__modal .modal-footer {
  border-top: 1px solid #f1f3f4;
  padding: 20px 25px;
}
.tour-detail__modal-image {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
}
.tour-detail__form-control {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}
.tour-detail__form-control:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  outline: none;
}
.tour-detail__form-label {
  font-weight: 600;
  color: #2c3e50;
  margin: 1rem 0;
  display: block;
}
@media (max-width: 768px) {
  .tour-detail {
    margin-left: 0 !important;
    padding: 1rem;
  }
  .tour-detail__header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
    padding: 1rem;
  }
  .tour-detail__title {
    font-size: 1.5rem;
  }
  .tour-detail__table--wrapper {
    overflow-x: auto;
  }
  .tour-detail__table {
    min-width: 600px;
  }
  .tour-detail__table thead th,
  .tour-detail__table tbody td {
    padding: 0.75rem;
    font-size: 0.8rem;
  }
  .tour-detail__btn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }
  .tour-detail__btn--sm {
    padding: 0.4rem 0.8rem;
    font-size: 0.75rem;
  }
  .tour-detail__btn--add {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }
  .tour-detail__info-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  .tour-detail__images-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  .tour-detail__pricing-header {
    padding: 1rem;
  }
  .tour-detail__itinerary-header {
    padding: 15px 20px;
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
  .tour-detail__itinerary-day {
    font-size: 1rem;
  }
  .tour-detail__itinerary-body {
    padding: 20px;
  }
  .tour-detail__itinerary-desc {
    font-size: 0.9rem;
  }
}
.tour-detail__alert {
  animation: tour-detail-slideInRight 0.3s ease-out;
}
.tour-detail__toast {
  border-width: 2px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}
.tour-detail__toast .toast-header {
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.tour-detail__toast .toast-header strong {
  font-weight: 600;
}
.tour-detail__toast .toast-header i {
  font-size: 16px;
}
.tour-detail__toast .toast-body {
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.98);
  font-weight: 500;
  color: #495057;
}
.tour-detail__toast.border-success {
  border-color: #28a745;
}
.tour-detail__toast.border-success .toast-header {
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(255, 255, 255, 0.95) 100%);
}
.tour-detail__toast.border-danger {
  border-color: #dc3545;
}
.tour-detail__toast.border-danger .toast-header {
  background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(255, 255, 255, 0.95) 100%);
}
.tour-detail__toast.border-warning {
  border-color: #ffc107;
}
.tour-detail__toast.border-warning .toast-header {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 255, 255, 0.95) 100%);
}
.tour-detail__toast.border-info {
  border-color: #17a2b8;
}
.tour-detail__toast.border-info .toast-header {
  background: linear-gradient(135deg, rgba(23, 162, 184, 0.1) 0%, rgba(255, 255, 255, 0.95) 100%);
}

@media (max-width: 768px) {
  .tour-detail {
    margin-left: 0 !important;
    padding: 15px;
  }
  .tour-detail__info-row {
    grid-template-columns: 1fr;
    gap: 15px;
    padding: 20px;
  }
  .tour-detail__info-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  .tour-detail__images-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  .tour-detail__table-wrapper {
    overflow-x: auto;
  }
  .tour-detail__table {
    min-width: 800px;
  }
  .tour-detail__table-th, .tour-detail__table-td {
    padding: 12px 10px;
    font-size: 12px;
  }
}
@keyframes tour-detail-slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
@keyframes tour-detail-expandContent {
  from {
    max-height: 0;
    opacity: 0;
  }
  to {
    max-height: 500px;
    opacity: 1;
  }
}
@keyframes tour-detail-collapseContent {
  from {
    max-height: 500px;
    opacity: 1;
  }
  to {
    max-height: 0;
    opacity: 0;
  }
}
.tour-detail-sidebar-item {
  margin-bottom: 20px;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  border-left: 5px solid #007bff;
  box-shadow: 0 2px 10px rgba(0, 123, 255, 0.1);
  transition: all 0.3s ease;
}
.tour-detail-sidebar-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 123, 255, 0.15);
}
.tour-detail-sidebar-item h6 {
  color: #2c3e50;
  margin-bottom: 12px;
  font-weight: 600;
  font-size: 14px;
}
.tour-detail-sidebar-item h6 i {
  color: #007bff;
  margin-right: 8px;
}
.tour-detail-sidebar-item p {
  color: #6c757d;
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

.tour-detail-sidebar-nav {
  position: sticky;
  top: 20px;
  max-height: calc(100vh - 40px);
  overflow-y: auto;
}
.tour-detail-sidebar-nav .nav-link {
  color: #6c757d;
  padding: 12px 20px;
  border-radius: 8px;
  margin-bottom: 8px;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}
.tour-detail-sidebar-nav .nav-link:hover {
  background-color: #e3f2fd;
  color: #1976d2;
  border-color: #bbdefb;
}
.tour-detail-sidebar-nav .nav-link.active {
  background-color: #007bff;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.tour-detail-image.cursor-pointer {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
  overflow: hidden;
}
.tour-detail-image.cursor-pointer:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.tour-detail-image-item {
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}
.tour-detail-image-item img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: transform 0.3s ease;
}
.tour-detail-image-item:hover img {
  transform: scale(1.1);
}

.tour-detail-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}
.tour-detail-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}
.tour-detail-card .card-header {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  border-radius: 12px 12px 0 0 !important;
  border: none;
  padding: 20px;
}
.tour-detail-card .card-header h5 {
  margin: 0;
  font-weight: 600;
}
.tour-detail-card .card-header h5 i {
  margin-right: 10px;
}
.tour-detail-card .card-body {
  padding: 25px;
}

.tour-detail-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}
.tour-detail-table thead {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}
.tour-detail-table thead th {
  border: none;
  color: #495057;
  font-weight: 600;
  padding: 15px 12px;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}
.tour-detail-table tbody tr {
  transition: all 0.3s ease;
}
.tour-detail-table tbody tr:hover {
  background-color: #f8f9fa;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.tour-detail-table tbody tr td {
  border: none;
  padding: 15px 12px;
  vertical-align: middle;
  border-bottom: 1px solid #f1f3f4;
}

.tour-detail-btn {
  border-radius: 8px;
  padding: 8px 16px;
  font-weight: 500;
  transition: all 0.3s ease;
}
.tour-detail-btn.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}
.tour-detail-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.tour-detail-btn.btn-primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border: none;
}
.tour-detail-btn.btn-warning {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
  border: none;
  color: #212529;
}
.tour-detail-btn.btn-danger {
  background: linear-gradient(135deg, #dc3545 0%, #bd2130 100%);
  border: none;
}

.tour-detail-badge {
  padding: 8px 12px;
  border-radius: 20px;
  font-weight: 500;
  font-size: 11px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tour-detail-modal .modal-content {
  border: none;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}
.tour-detail-modal .modal-header {
  border-bottom: 1px solid #f1f3f4;
  padding: 20px 25px;
}
.tour-detail-modal .modal-header .modal-title {
  font-weight: 600;
  color: #2c3e50;
}
.tour-detail-modal .modal-body {
  padding: 25px;
}
.tour-detail-modal .modal-footer {
  border-top: 1px solid #f1f3f4;
  padding: 20px 25px;
}

.tour-detail-form-control {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 12px 15px;
  transition: all 0.3s ease;
}
.tour-detail-form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.tour-detail-form-label {
  font-weight: 500;
  color: #495057;
  margin-bottom: 8px;
}

.tour-detail-itinerary-day {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  border-left: 4px solid #28a745;
}
.tour-detail-itinerary-day h6 {
  color: #28a745;
  font-weight: 600;
  margin-bottom: 10px;
}
.tour-detail-itinerary-day h6 i {
  margin-right: 8px;
}
.tour-detail-itinerary-day p {
  color: #6c757d;
  line-height: 1.6;
  margin: 0;
}

.category {
  margin-left: 240px;
  padding: 2rem;
  height: 100%;
  min-width: calc(100% - 260px);
  overflow: auto;
}
.category__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: white;
  padding: 1.5rem 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}
.category__items-per-page-form .form-label {
  font-size: 0.9rem;
  font-weight: 500;
  white-space: nowrap;
}
.category__items-per-page-form .form-select {
  border-radius: 8px;
  border: 1px solid #ced4da;
  min-width: 70px;
}
.category__items-per-page-form .form-select:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
.category__items-per-page-form .d-inline-flex {
  align-items: center;
  gap: 0.5rem;
}
.category__search-section {
  margin-bottom: 2rem;
}
.category__search-section .category__search-form .input-group {
  max-width: 400px;
}
.category__search-section .category__search-form .input-group > button {
  border-top-right-radius: 8px !important;
  border-bottom-right-radius: 8px !important;
}
.category__search-section .category__search-form .input-group .form-control {
  padding: 1rem;
  font-size: 1rem;
  border-radius: 8px;
}
.category__search-section .category__search-form .input-group .form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
.category__search-section .category__search-form .input-group .btn {
  padding: 1rem;
  font-size: 1.2rem;
  background: #3498db;
  color: #ffffff;
  border-radius: 8px;
  border: 1px solid #3498db;
}
.category__search-section .category__search-form .input-group .btn:hover {
  background: transparent;
  color: #040404;
  border-color: #3498db;
}
.category__search-section .category__search-form .input-group .btn-outline-danger {
  position: absolute;
  right: 75px;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  color: #ccc;
  border: none;
}
.category__search-section .category__search-form .input-group .btn-outline-danger:hover {
  color: #5b5b5b;
}
.category__search-section .category__info {
  font-size: 0.9rem;
  color: #666;
}
.category__title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.category__btn {
  padding: 0.75rem 2.5rem;
  border: 1px solid transparent;
  outline: none;
  border-radius: 5px;
  font-weight: 500;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.2rem;
  line-height: 1.5;
}
.category__btn:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.category__btn--primary {
  background: #3498db;
  color: white;
}
.category__btn--primary:hover {
  background: #f8f9fa;
  color: #040404;
  border-color: #3498db;
}
.category__btn--secondary {
  background: #6c757d;
  color: white;
}
.category__btn--secondary:hover {
  background: #5a6268;
  color: white;
}
.category__btn--warning {
  border: 1px solid #ccc;
}
.category__btn--warning:hover {
  background: #ffffff;
  color: #959595;
}
.category__btn--danger {
  border: 1px solid #e53935;
  color: #e53935;
}
.category__btn--danger:hover {
  background: #ff5252;
  color: white;
}
.category__btn--sm {
  padding: 0.8rem 1rem;
  font-size: 0.8rem;
}
.category__table--wrapper {
  background: white;
  box-shadow: 0px 0px 30px 9px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border-radius: 10px;
}
.category__table {
  width: 100%;
  margin: 0;
  background: white;
}
.category__table th {
  background: #3498db;
  color: white;
  padding: 2rem;
  text-align: center;
  font-weight: 600;
  font-size: 1.2rem;
}
.category__table td {
  padding: 1.5rem;
  border-bottom: 1px solid #e9ecef;
  vertical-align: middle;
  font-size: 1.2rem;
  text-align: center;
}
.category__table td:first-child {
  font-weight: 600;
}
.category__table td:nth-child(3) {
  text-align: left;
}
.category__table-empty {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  padding: 10rem !important;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}
.category__badge {
  padding: 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  border: none;
  cursor: default;
  transition: all 0.3s ease;
}
.category__badge--success {
  background: #28a745;
  color: #fff;
}
.category__badge--inactive {
  background: #dc3545;
  color: #fff;
}
.category__badge--toggle {
  cursor: pointer;
  transition: all 0.3s ease;
}
.category__badge--toggle:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.category__badge--toggle:active {
  transform: translateY(0);
}
.category__badge--toggle:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}
.category__user-info {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #495057;
  font-size: 0.875rem;
  font-weight: 500;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  border: 1px solid #dee2e6;
  transition: all 0.3s ease;
}
.category__user-info:hover {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.category__user-info i {
  color: #6c757d;
  font-size: 0.875rem;
}
.category__form--delete {
  display: inline-block;
  margin-left: 0.5rem;
}
.category .status-changing {
  animation: pulse 0.5s ease-in-out;
}
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}
@media (max-width: 768px) {
  .category {
    padding: 1rem;
  }
  .category__header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  .category__title {
    font-size: 1.5rem;
  }
  .category__table--wrapper {
    overflow-x: auto;
  }
  .category__table {
    min-width: 600px;
  }
  .category__table thead th,
  .category__table tbody td {
    padding: 0.75rem;
    font-size: 0.8rem;
  }
  .category__btn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }
  .category__btn--sm {
    padding: 0.4rem 0.8rem;
    font-size: 0.75rem;
  }
}
.category__pagination {
  margin-top: 2rem;
}
.category__pagination .pagination .page-item {
  margin: 0 2px;
}
.category__pagination .pagination .page-item .page-link {
  border-radius: 6px;
  border: 1px solid #dee2e6;
  color: #3498db;
  padding: 0.5rem 0.75rem;
  font-weight: 500;
  transition: all 0.2s ease;
  min-width: 40px;
  text-align: center;
  background-color: #fff;
}
.category__pagination .pagination .page-item .page-link:hover {
  background-color: #e9ecef;
  border-color: #adb5bd;
  color: #0056b3;
  text-decoration: none;
}
.category__pagination .pagination .page-item .page-link:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  z-index: 3;
}
.category__pagination .pagination .page-item.active .page-link {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
  font-weight: 600;
}
.category__pagination .pagination .page-item.active .page-link:hover {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}
.category__pagination .pagination .page-item.disabled .page-link {
  color: #6c757d;
  background-color: #fff;
  border-color: #dee2e6;
  cursor: not-allowed;
  opacity: 0.65;
}
.category__pagination .pagination .page-item.disabled .page-link:hover {
  background-color: #fff;
  border-color: #dee2e6;
  color: #6c757d;
}
.category__pagination .pagination .page-item:first-child .page-link,
.category__pagination .pagination .page-item:last-child .page-link {
  font-weight: bold;
  font-size: 1.1rem;
}
.category__pagination-info {
  margin-top: 1.5rem;
}
.category__pagination-info small {
  font-size: 0.875rem;
  color: #6c757d;
  display: block;
  margin-bottom: 0.5rem;
}

.category-form {
  background: white;
  border-radius: 12px;
  box-shadow: 0px 0px 30px 9px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin: 2rem auto;
}
.category-form__header {
  background: #3498db;
  color: white;
  padding: 2rem;
  text-align: left;
}
.category-form__header h3 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
}
.category-form__body {
  padding: 2rem;
}
.category-form .form-label {
  font-weight: 600;
  color: #2c3e50;
  margin: 1rem 0;
  display: block;
}
.category-form .form-control,
.category-form .form-select {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}
.category-form .form-control:focus,
.category-form .form-select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  outline: none;
}
.category-form .btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1.2rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
}
.category-form .btn:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.category-form .btn-primary {
  background: #3498db;
  border: 1px solid #3498db;
}
.category-form .btn-primary:hover {
  color: #040404;
  background: transparent;
}
.category-form .btn-secondary {
  background: #6c757d;
  border: none;
}
.category-form .btn-secondary:hover {
  background: #5a6268;
}

.departure {
  margin-left: 240px;
  padding: 2rem;
  height: 100%;
  min-width: calc(100% - 260px);
  overflow: auto;
}
.departure__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: white;
  padding: 1.5rem 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}
.departure__search-section {
  margin-bottom: 2rem;
}
.departure__search-section .departure__search-form .input-group {
  max-width: 400px;
}
.departure__search-section .departure__search-form .input-group > button {
  border-top-right-radius: 8px !important;
  border-bottom-right-radius: 8px !important;
}
.departure__search-section .departure__search-form .input-group .form-control {
  padding: 1rem;
  font-size: 1rem;
  border-radius: 8px;
}
.departure__search-section .departure__search-form .input-group .form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
.departure__search-section .departure__search-form .input-group .btn {
  padding: 1rem;
  font-size: 1.2rem;
  background: #3498db;
  color: #ffffff;
  border-radius: 8px;
  border: 1px solid #3498db;
}
.departure__search-section .departure__search-form .input-group .btn:hover {
  background: transparent;
  color: #040404;
  border-color: #3498db;
}
.departure__search-section .departure__search-form .input-group .btn-outline-danger {
  position: absolute;
  right: 75px;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  color: #ccc;
  border: none;
}
.departure__search-section .departure__search-form .input-group .btn-outline-danger:hover {
  color: #5b5b5b;
}
.departure__search-section .departure__info {
  font-size: 0.9rem;
  color: #666;
}
.departure__title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.departure__actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}
.departure__btn--delete-selected {
  animation: slideIn 0.3s ease-out;
}
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
.departure__btn {
  padding: 0.75rem 2.5rem;
  border: 1px solid transparent;
  outline: none;
  border-radius: 5px;
  font-weight: 500;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.2rem;
  line-height: 1.5;
}
.departure__btn:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.departure__btn--primary {
  background: #3498db;
  color: white;
}
.departure__btn--primary:hover {
  background: #f8f9fa;
  color: #040404;
  border-color: #3498db;
}
.departure__btn--secondary {
  background: #6c757d;
  color: white;
}
.departure__btn--secondary:hover {
  background: #5a6268;
  color: white;
}
.departure__btn--warning {
  border: 1px solid #ccc;
}
.departure__btn--warning:hover {
  background: #ffffff;
  color: #959595;
}
.departure__btn--danger {
  border: 1px solid #e53935;
  color: #e53935;
}
.departure__btn--danger:hover {
  background: #ff5252;
  color: white;
}
.departure__btn--sm {
  padding: 0.8rem 1rem;
  font-size: 0.8rem;
}
.departure__table--wrapper {
  background: white;
  box-shadow: 0px 0px 30px 9px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border-radius: 10px;
}
.departure__table {
  width: 100%;
  margin: 0;
  background: white;
}
.departure__table th {
  background: #3498db;
  color: white;
  padding: 2rem;
  text-align: center;
  font-weight: 600;
  font-size: 1.2rem;
}
.departure__table td {
  padding: 1.5rem;
  border-bottom: 1px solid #e9ecef;
  vertical-align: middle;
  font-size: 1.2rem;
  text-align: center;
}
.departure__table td:first-child {
  font-weight: 600;
}
.departure__table td:nth-child(4) {
  text-align: left;
}
.departure__table-empty {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  padding: 3rem !important;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}
.departure__table-empty span {
  display: inline-block;
  padding: 10rem;
}
.departure__badge {
  padding: 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  border: none;
  cursor: default;
  transition: all 0.3s ease;
}
.departure__badge--success {
  background: #28a745;
  color: #fff;
}
.departure__badge--inactive {
  background: #dc3545;
  color: #fff;
}
.departure__badge--toggle {
  cursor: pointer;
  transition: all 0.3s ease;
}
.departure__badge--toggle:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.departure__badge--toggle:active {
  transform: translateY(0);
}
.departure__badge--toggle:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}
.departure__user-info {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #495057;
  font-size: 0.875rem;
  font-weight: 500;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  border: 1px solid #dee2e6;
  transition: all 0.3s ease;
}
.departure__user-info:hover {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.departure__user-info i {
  color: #6c757d;
  font-size: 0.875rem;
}
.departure__form--delete {
  display: inline-block;
  margin-left: 0.5rem;
}
.departure .status-changing {
  animation: pulse 0.5s ease-in-out;
}
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}
.departure__items-per-page-form .form-label {
  font-size: 0.9rem;
  font-weight: 500;
  white-space: nowrap;
}
.departure__items-per-page-form .form-select {
  border-radius: 8px;
  border: 1px solid #ced4da;
  min-width: 70px;
}
.departure__items-per-page-form .form-select:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
.departure__items-per-page-form .d-inline-flex {
  align-items: center;
  gap: 0.5rem;
}
.departure__pagination {
  margin-top: 2rem;
}
.departure__pagination .pagination .page-item {
  margin: 0 2px;
}
.departure__pagination .pagination .page-item .page-link {
  border-radius: 6px;
  border: 1px solid #dee2e6;
  color: #3498db;
  padding: 0.5rem 0.75rem;
  font-weight: 500;
  transition: all 0.2s ease;
  min-width: 40px;
  text-align: center;
  background-color: #fff;
}
.departure__pagination .pagination .page-item .page-link:hover {
  background-color: #e9ecef;
  border-color: #adb5bd;
  color: #0056b3;
  text-decoration: none;
}
.departure__pagination .pagination .page-item .page-link:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  z-index: 3;
}
.departure__pagination .pagination .page-item.active .page-link {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
  font-weight: 600;
}
.departure__pagination .pagination .page-item.active .page-link:hover {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}
.departure__pagination .pagination .page-item.disabled .page-link {
  color: #6c757d;
  background-color: #fff;
  border-color: #dee2e6;
  cursor: not-allowed;
  opacity: 0.65;
}
.departure__pagination .pagination .page-item.disabled .page-link:hover {
  background-color: #fff;
  border-color: #dee2e6;
  color: #6c757d;
}
.departure__pagination .pagination .page-item:first-child .page-link,
.departure__pagination .pagination .page-item:last-child .page-link {
  font-weight: bold;
  font-size: 1.1rem;
}
.departure__pagination-info {
  margin-top: 1.5rem;
}
.departure__pagination-info small {
  font-size: 0.875rem;
  color: #6c757d;
  display: block;
  margin-bottom: 0.5rem;
}
.departure__quick-jump .form-control {
  border-radius: 8px;
  border: 1px solid #ced4da;
  text-align: center;
  font-weight: 500;
}
.departure__quick-jump .form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
.departure__quick-jump .btn {
  border-radius: 8px;
  font-weight: 500;
}
.departure__quick-jump .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
}
.departure__quick-jump small {
  font-weight: 500;
  color: #495057;
}
@media (max-width: 768px) {
  .departure {
    padding: 1rem;
  }
  .departure__header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  .departure__title {
    font-size: 1.5rem;
  }
  .departure__table--wrapper {
    overflow-x: auto;
  }
  .departure__table {
    min-width: 600px;
  }
  .departure__table thead th,
  .departure__table tbody td {
    padding: 0.75rem;
    font-size: 0.8rem;
  }
  .departure__btn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }
  .departure__btn--sm {
    padding: 0.4rem 0.8rem;
    font-size: 0.75rem;
  }
}
.departure .departure-checkbox,
.departure #selectAll {
  transform: scale(1.2);
  margin: 0;
  cursor: pointer;
  border: 1px solid #ccc;
}
.departure th:first-child,
.departure td:first-child {
  width: 50px;
  text-align: center;
  padding: 1rem 0.5rem !important;
}

.departure-form {
  background: white;
  border-radius: 12px;
  box-shadow: 0px 0px 30px 9px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin: 2rem auto;
}
.departure-form__header {
  background: #3498db;
  color: white;
  padding: 2rem;
  text-align: left;
}
.departure-form__header h3 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
}
.departure-form__body {
  padding: 2rem;
}
.departure-form .form-label {
  font-weight: 600;
  color: #2c3e50;
  margin: 1rem 0;
  display: block;
}
.departure-form .form-control,
.departure-form .form-select {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}
.departure-form .form-control:focus,
.departure-form .form-select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  outline: none;
}
.departure-form .btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1.2rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
}
.departure-form .btn:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.departure-form .btn-primary {
  background: #3498db;
  border: 1px solid #3498db;
}
.departure-form .btn-primary:hover {
  color: #040404;
  background: transparent;
}
.departure-form .btn-secondary {
  background: #6c757d;
  border: none;
}
.departure-form .btn-secondary:hover {
  background: #5a6268;
}

.destination {
  margin-left: 240px;
  padding: 2rem;
  height: 100%;
  min-width: calc(100% - 260px);
}
.destination__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: white;
  padding: 1.5rem 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}
.destination__search-section {
  margin-bottom: 2rem;
}
.destination__search-section .destination__search-form .input-group {
  max-width: 400px;
}
.destination__search-section .destination__search-form .input-group > button {
  border-top-right-radius: 8px !important;
  border-bottom-right-radius: 8px !important;
}
.destination__search-section .destination__search-form .input-group .form-control {
  padding: 1rem;
  font-size: 1rem;
  border-radius: 8px;
}
.destination__search-section .destination__search-form .input-group .form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
.destination__search-section .destination__search-form .input-group .btn {
  padding: 1rem;
  font-size: 1.2rem;
  background: #3498db;
  color: #ffffff;
  border-radius: 8px;
  border: 1px solid #3498db;
}
.destination__search-section .destination__search-form .input-group .btn:hover {
  background: transparent;
  color: #040404;
  border-color: #3498db;
}
.destination__search-section .destination__search-form .input-group .btn-outline-danger {
  position: absolute;
  right: 75px;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  color: #ccc;
  border: none;
}
.destination__search-section .destination__search-form .input-group .btn-outline-danger:hover {
  color: #5b5b5b;
}
.destination__search-section .destination__info {
  font-size: 0.9rem;
  color: #666;
}
.destination__title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.destination__actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}
.destination__btn--delete-selected {
  animation: slideIn 0.3s ease-out;
}
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
.destination__btn {
  padding: 0.75rem 2.5rem;
  border: 1px solid transparent;
  outline: none;
  border-radius: 5px;
  font-weight: 500;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.2rem;
  line-height: 1.5;
}
.destination__btn:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.destination__btn--primary {
  background: #3498db;
  color: white;
}
.destination__btn--primary:hover {
  background: #f8f9fa;
  color: #040404;
  border-color: #3498db;
}
.destination__btn--secondary {
  background: #6c757d;
  color: white;
}
.destination__btn--secondary:hover {
  background: #5a6268;
  color: white;
}
.destination__btn--warning {
  border: 1px solid #ccc;
}
.destination__btn--warning:hover {
  background: #ffffff;
  color: #959595;
}
.destination__btn--danger {
  border: 1px solid #e53935;
  color: #e53935;
}
.destination__btn--danger:hover {
  background: #ff5252;
  color: white;
}
.destination__btn--sm {
  padding: 0.8rem 1rem;
  font-size: 0.8rem;
}
.destination__table--wrapper {
  background: white;
  box-shadow: 0px 0px 30px 9px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border-radius: 10px;
}
.destination__table {
  width: 100%;
  margin: 0;
  background: white;
}
.destination__table th {
  background: #3498db;
  color: white;
  padding: 2rem;
  text-align: center;
  font-weight: 600;
  font-size: 1.2rem;
  white-space: nowrap;
}
.destination__table td {
  padding: 1.5rem;
  border-bottom: 1px solid #e9ecef;
  vertical-align: middle;
  font-size: 1.2rem;
  text-align: center;
  background: white;
}
.destination__table td:nth-child(2) {
  text-overflow: ellipsis;
  overflow: hidden;
}
.destination__table-empty {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  padding: 3rem !important;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}
.destination__table-empty span {
  display: inline-block;
  padding: 10rem;
  color: #6c757d;
}
.destination__badge {
  padding: 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  border: none;
  cursor: default;
  transition: all 0.3s ease;
}
.destination__badge--success {
  background: #28a745;
  color: #fff;
}
.destination__badge--inactive {
  background: #dc3545;
  color: #fff;
}
.destination__badge--toggle {
  cursor: pointer;
  transition: all 0.3s ease;
}
.destination__badge--toggle:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.destination__badge--toggle:active {
  transform: translateY(0);
}
.destination__badge--toggle:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}
.destination__user-info {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #495057;
  font-size: 0.875rem;
  font-weight: 500;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  border: 1px solid #dee2e6;
  transition: all 0.3s ease;
}
.destination__user-info:hover {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.destination__user-info i {
  color: #6c757d;
  font-size: 0.875rem;
}
.destination__form--delete {
  display: inline-block;
  margin-left: 0.5rem;
}
.destination .status-changing {
  animation: pulse 0.5s ease-in-out;
}
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}
.destination__items-per-page-form .form-label {
  font-size: 0.9rem;
  font-weight: 500;
  white-space: nowrap;
}
.destination__items-per-page-form .form-select {
  border-radius: 8px;
  border: 1px solid #ced4da;
  min-width: 70px;
}
.destination__items-per-page-form .form-select:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
.destination__items-per-page-form .d-inline-flex {
  align-items: center;
  gap: 0.5rem;
}
.destination__pagination {
  margin-top: 2rem;
}
.destination__pagination .pagination .page-item {
  margin: 0 2px;
}
.destination__pagination .pagination .page-item .page-link {
  border-radius: 6px;
  border: 1px solid #dee2e6;
  color: #3498db;
  padding: 0.5rem 0.75rem;
  font-weight: 500;
  transition: all 0.2s ease;
  min-width: 40px;
  text-align: center;
  background-color: #fff;
}
.destination__pagination .pagination .page-item .page-link:hover {
  background-color: #e9ecef;
  border-color: #adb5bd;
  color: #0056b3;
  text-decoration: none;
}
.destination__pagination .pagination .page-item .page-link:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  z-index: 3;
}
.destination__pagination .pagination .page-item.active .page-link {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
  font-weight: 600;
}
.destination__pagination .pagination .page-item.active .page-link:hover {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}
.destination__pagination .pagination .page-item.disabled .page-link {
  color: #6c757d;
  background-color: #fff;
  border-color: #dee2e6;
  cursor: not-allowed;
  opacity: 0.65;
}
.destination__pagination .pagination .page-item.disabled .page-link:hover {
  background-color: #fff;
  border-color: #dee2e6;
  color: #6c757d;
}
.destination__pagination .pagination .page-item:first-child .page-link,
.destination__pagination .pagination .page-item:last-child .page-link {
  font-weight: bold;
  font-size: 1.1rem;
}
.destination__pagination-info {
  margin-top: 1.5rem;
}
.destination__pagination-info small {
  font-size: 0.875rem;
  color: #6c757d;
  display: block;
  margin-bottom: 0.5rem;
}
.destination__quick-jump .form-control {
  border-radius: 8px;
  border: 1px solid #ced4da;
  text-align: center;
  font-weight: 500;
}
.destination__quick-jump .form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
.destination__quick-jump .btn {
  border-radius: 8px;
  font-weight: 500;
}
.destination__quick-jump .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
}
.destination__quick-jump small {
  font-weight: 500;
  color: #495057;
}
@media (max-width: 768px) {
  .destination {
    padding: 1rem;
  }
  .destination__header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  .destination__title {
    font-size: 1.5rem;
  }
  .destination__table--wrapper {
    overflow-x: auto;
  }
  .destination__table {
    min-width: 600px;
  }
  .destination__table thead th,
  .destination__table tbody td {
    padding: 0.75rem;
    font-size: 0.8rem;
  }
  .destination__btn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }
  .destination__btn--sm {
    padding: 0.4rem 0.8rem;
    font-size: 0.75rem;
  }
}
.destination .destination-checkbox,
.destination #selectAll {
  transform: scale(1.2);
  margin: 0;
  cursor: pointer;
  border: 1px solid #ccc;
}
.destination th:first-child,
.destination td:first-child {
  width: 50px;
  text-align: center;
  padding: 1.5rem !important;
  padding-left: 2rem !important;
}

.destination-form {
  background: white;
  border-radius: 12px;
  box-shadow: 0px 0px 30px 9px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin: 2rem auto;
}
.destination-form__header {
  background: #3498db;
  color: white;
  padding: 2rem;
  text-align: left;
}
.destination-form__header h3 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
}
.destination-form__body {
  padding: 2rem;
}
.destination-form .form-label {
  font-weight: 600;
  color: #2c3e50;
  margin: 1rem 0;
  display: block;
}
.destination-form .form-control,
.destination-form .form-select {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}
.destination-form .form-control:focus,
.destination-form .form-select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  outline: none;
}
.destination-form .btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1.2rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
}
.destination-form .btn:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.destination-form .btn-primary {
  background: #3498db;
  border: 1px solid #3498db;
}
.destination-form .btn-primary:hover {
  color: #040404;
  background: transparent;
}
.destination-form .btn-secondary {
  background: #6c757d;
  border: none;
}
.destination-form .btn-secondary:hover {
  background: #5a6268;
}

.transportation {
  margin-left: 240px;
  padding: 2rem;
  height: 100%;
  min-width: calc(100% - 260px);
  overflow: auto;
}
.transportation__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: white;
  padding: 1.5rem 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}
.transportation__search-section {
  margin-bottom: 2rem;
}
.transportation__search-section .transportation__search-form .input-group {
  max-width: 400px;
}
.transportation__search-section .transportation__search-form .input-group > button {
  border-top-right-radius: 8px !important;
  border-bottom-right-radius: 8px !important;
}
.transportation__search-section .transportation__search-form .input-group .form-control {
  padding: 1rem;
  font-size: 1rem;
  border-radius: 8px;
}
.transportation__search-section .transportation__search-form .input-group .form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
.transportation__search-section .transportation__search-form .input-group .btn {
  padding: 1rem;
  font-size: 1.2rem;
  background: #3498db;
  color: #ffffff;
  border-radius: 8px;
  border: 1px solid #3498db;
}
.transportation__search-section .transportation__search-form .input-group .btn:hover {
  background: transparent;
  color: #040404;
  border-color: #3498db;
}
.transportation__search-section .transportation__search-form .input-group .btn-outline-danger {
  position: absolute;
  right: 75px;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  color: #ccc;
  border: none;
}
.transportation__search-section .transportation__search-form .input-group .btn-outline-danger:hover {
  color: #5b5b5b;
}
.transportation__search-section .transportation__info {
  font-size: 0.9rem;
  color: #666;
}
.transportation__title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.transportation__actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}
.transportation__btn--delete-selected {
  animation: slideIn 0.3s ease-out;
}
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
.transportation__btn {
  padding: 0.75rem 2.5rem;
  border: 1px solid transparent;
  outline: none;
  border-radius: 5px;
  font-weight: 500;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.2rem;
  line-height: 1.5;
}
.transportation__btn:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.transportation__btn--primary {
  background: #3498db;
  color: white;
}
.transportation__btn--primary:hover {
  background: #f8f9fa;
  color: #040404;
  border-color: #3498db;
}
.transportation__btn--secondary {
  background: #6c757d;
  color: white;
}
.transportation__btn--secondary:hover {
  background: #5a6268;
  color: white;
}
.transportation__btn--warning {
  border: 1px solid #ccc;
}
.transportation__btn--warning:hover {
  background: #ffffff;
  color: #959595;
}
.transportation__btn--danger {
  border: 1px solid #e53935;
  color: #e53935;
}
.transportation__btn--danger:hover {
  background: #ff5252;
  color: white;
}
.transportation__btn--sm {
  padding: 0.8rem 1rem;
  font-size: 0.8rem;
}
.transportation__table--wrapper {
  background: white;
  box-shadow: 0px 0px 30px 9px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border-radius: 10px;
}
.transportation__table {
  width: 100%;
  margin: 0;
  background: white;
}
.transportation__table th {
  background: #3498db;
  color: white;
  padding: 2rem;
  text-align: center;
  font-weight: 600;
  font-size: 1.2rem;
}
.transportation__table td {
  padding: 1.5rem;
  border-bottom: 1px solid #e9ecef;
  vertical-align: middle;
  font-size: 1.2rem;
  text-align: center;
}
.transportation__table td:first-child {
  font-weight: 600;
}
.transportation__table td:nth-child(4) {
  text-align: left;
}
.transportation__table-empty {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  padding: 3rem !important;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}
.transportation__table-empty span {
  display: inline-block;
  padding: 10rem;
}
.transportation__badge {
  padding: 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  border: none;
  cursor: default;
  transition: all 0.3s ease;
}
.transportation__badge--success {
  background: #28a745;
  color: #fff;
}
.transportation__badge--inactive {
  background: #dc3545;
  color: #fff;
}
.transportation__badge--toggle {
  cursor: pointer;
  transition: all 0.3s ease;
}
.transportation__badge--toggle:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.transportation__badge--toggle:active {
  transform: translateY(0);
}
.transportation__badge--toggle:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}
.transportation__user-info {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #495057;
  font-size: 0.875rem;
  font-weight: 500;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  border: 1px solid #dee2e6;
  transition: all 0.3s ease;
}
.transportation__user-info:hover {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.transportation__user-info i {
  color: #6c757d;
  font-size: 0.875rem;
}
.transportation__form--delete {
  display: inline-block;
  margin-left: 0.5rem;
}
.transportation .status-changing {
  animation: pulse 0.5s ease-in-out;
}
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}
.transportation__items-per-page-form .form-label {
  font-size: 0.9rem;
  font-weight: 500;
  white-space: nowrap;
}
.transportation__items-per-page-form .form-select {
  border-radius: 8px;
  border: 1px solid #ced4da;
  min-width: 70px;
}
.transportation__items-per-page-form .form-select:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
.transportation__items-per-page-form .d-inline-flex {
  align-items: center;
  gap: 0.5rem;
}
.transportation__pagination {
  margin-top: 2rem;
}
.transportation__pagination .pagination .page-item {
  margin: 0 2px;
}
.transportation__pagination .pagination .page-item .page-link {
  border-radius: 6px;
  border: 1px solid #dee2e6;
  color: #3498db;
  padding: 0.5rem 0.75rem;
  font-weight: 500;
  transition: all 0.2s ease;
  min-width: 40px;
  text-align: center;
  background-color: #fff;
}
.transportation__pagination .pagination .page-item .page-link:hover {
  background-color: #e9ecef;
  border-color: #adb5bd;
  color: #0056b3;
  text-decoration: none;
}
.transportation__pagination .pagination .page-item .page-link:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  z-index: 3;
}
.transportation__pagination .pagination .page-item.active .page-link {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
  font-weight: 600;
}
.transportation__pagination .pagination .page-item.active .page-link:hover {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}
.transportation__pagination .pagination .page-item.disabled .page-link {
  color: #6c757d;
  background-color: #fff;
  border-color: #dee2e6;
  cursor: not-allowed;
  opacity: 0.65;
}
.transportation__pagination .pagination .page-item.disabled .page-link:hover {
  background-color: #fff;
  border-color: #dee2e6;
  color: #6c757d;
}
.transportation__pagination .pagination .page-item:first-child .page-link,
.transportation__pagination .pagination .page-item:last-child .page-link {
  font-weight: bold;
  font-size: 1.1rem;
}
.transportation__pagination-info {
  margin-top: 1.5rem;
}
.transportation__pagination-info small {
  font-size: 0.875rem;
  color: #6c757d;
  display: block;
  margin-bottom: 0.5rem;
}
.transportation__quick-jump .form-control {
  border-radius: 8px;
  border: 1px solid #ced4da;
  text-align: center;
  font-weight: 500;
}
.transportation__quick-jump .form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
.transportation__quick-jump .btn {
  border-radius: 8px;
  font-weight: 500;
}
.transportation__quick-jump .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
}
.transportation__quick-jump small {
  font-weight: 500;
  color: #495057;
}
@media (max-width: 768px) {
  .transportation {
    padding: 1rem;
  }
  .transportation__header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  .transportation__title {
    font-size: 1.5rem;
  }
  .transportation__table--wrapper {
    overflow-x: auto;
  }
  .transportation__table {
    min-width: 600px;
  }
  .transportation__table thead th,
  .transportation__table tbody td {
    padding: 0.75rem;
    font-size: 0.8rem;
  }
  .transportation__btn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }
  .transportation__btn--sm {
    padding: 0.4rem 0.8rem;
    font-size: 0.75rem;
  }
}
.transportation .transportation-checkbox,
.transportation #selectAll {
  transform: scale(1.2);
  margin: 0;
  cursor: pointer;
  border: 1px solid #ccc;
}
.transportation th:first-child,
.transportation td:first-child {
  width: 50px;
  text-align: center;
  padding: 1rem 0.5rem !important;
}

.transportation-form {
  background: white;
  border-radius: 12px;
  box-shadow: 0px 0px 30px 9px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin: 2rem auto;
}
.transportation-form__header {
  background: #3498db;
  color: white;
  padding: 2rem;
  text-align: left;
}
.transportation-form__header h3 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
}
.transportation-form__body {
  padding: 2rem;
}
.transportation-form .form-label {
  font-weight: 600;
  color: #2c3e50;
  margin: 1rem 0;
  display: block;
}
.transportation-form .form-control,
.transportation-form .form-select {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}
.transportation-form .form-control:focus,
.transportation-form .form-select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  outline: none;
}
.transportation-form .btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1.2rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
}
.transportation-form .btn:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.transportation-form .btn-primary {
  background: #3498db;
  border: 1px solid #3498db;
}
.transportation-form .btn-primary:hover {
  color: #040404;
  background: transparent;
}
.transportation-form .btn-secondary {
  background: #6c757d;
  border: none;
}
.transportation-form .btn-secondary:hover {
  background: #5a6268;
}

.account {
  margin-left: 240px;
  padding: 2rem;
  height: 100%;
  min-width: calc(100% - 260px);
}
.account__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: white;
  padding: 1.5rem 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}
.account__search-section {
  margin-bottom: 2rem;
}
.account__search-section .account__search-form .input-group {
  max-width: 400px;
}
.account__search-section .account__search-form .input-group > button {
  border-top-right-radius: 8px !important;
  border-bottom-right-radius: 8px !important;
}
.account__search-section .account__search-form .input-group .form-control {
  padding: 1rem;
  font-size: 1rem;
  border-radius: 8px;
}
.account__search-section .account__search-form .input-group .form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
.account__search-section .account__search-form .input-group .btn {
  padding: 1rem;
  font-size: 1.2rem;
  background: #3498db;
  color: #ffffff;
  border-radius: 8px;
  border: 1px solid #3498db;
}
.account__search-section .account__search-form .input-group .btn:hover {
  background: transparent;
  color: #040404;
  border-color: #3498db;
}
.account__search-section .account__search-form .input-group .btn-outline-danger {
  position: absolute;
  right: 75px;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  color: #ccc;
  border: none;
}
.account__search-section .account__search-form .input-group .btn-outline-danger:hover {
  color: #5b5b5b;
}
.account__search-section .account__info {
  font-size: 0.9rem;
  color: #666;
}
.account__title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.account__actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}
.account__btn--delete-selected {
  animation: slideIn 0.3s ease-out;
}
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
.account__btn {
  padding: 0.75rem 2.5rem;
  border: 1px solid transparent;
  outline: none;
  border-radius: 5px;
  font-weight: 500;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.2rem;
  line-height: 1.5;
}
.account__btn:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.account__btn--primary {
  background: #3498db;
  color: white;
}
.account__btn--primary:hover {
  background: #f8f9fa;
  color: #040404;
  border-color: #3498db;
}
.account__btn--secondary {
  background: #6c757d;
  color: white;
}
.account__btn--secondary:hover {
  background: #5a6268;
  color: white;
}
.account__btn--warning {
  border: 1px solid #ccc;
}
.account__btn--warning:hover {
  background: #ffffff;
  color: #959595;
}
.account__btn--danger {
  border: 1px solid #e53935;
  color: #e53935;
}
.account__btn--danger:hover {
  background: #ff5252;
  color: white;
}
.account__btn--sm {
  padding: 0.8rem 1rem;
  font-size: 0.8rem;
}
.account__table--wrapper {
  background: white;
  box-shadow: 0px 0px 30px 9px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border-radius: 10px;
}
.account__table {
  width: 100%;
  margin: 0;
  background: white;
}
.account__table th {
  background: #3498db;
  color: white;
  padding: 2rem;
  text-align: center;
  font-weight: 600;
  font-size: 1.2rem;
}
.account__table td {
  padding: 1.5rem;
  border-bottom: 1px solid #e9ecef;
  vertical-align: middle;
  font-size: 1.2rem;
  text-align: center;
  background: white;
}
.account__table td:nth-child(3) {
  text-align: center;
}
.account__table-empty {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  padding: 3rem !important;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}
.account__table-empty span {
  display: inline-block;
  padding: 10rem;
  color: #6c757d;
}
.account__badge {
  padding: 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  border: none;
  cursor: default;
  transition: all 0.3s ease;
}
.account__badge--success {
  background: #28a745;
  color: #fff;
}
.account__badge--inactive {
  background: #dc3545;
  color: #fff;
}
.account__badge--toggle {
  cursor: pointer;
  transition: all 0.3s ease;
}
.account__badge--toggle:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.account__badge--toggle:active {
  transform: translateY(0);
}
.account__badge--toggle:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}
.account .status-changing {
  animation: pulse 0.8s ease-in-out infinite;
}
@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
.account__user-info {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #495057;
  font-size: 0.875rem;
  font-weight: 500;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  border: 1px solid #dee2e6;
  transition: all 0.3s ease;
}
.account__user-info:hover {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.account__user-info i {
  color: #6c757d;
  font-size: 0.875rem;
}
.account__form--delete {
  display: inline-block;
  margin-left: 0.5rem;
}
.account .status-changing {
  animation: pulse 0.5s ease-in-out;
}
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}
.account__items-per-page-form .form-label {
  font-size: 0.9rem;
  font-weight: 500;
  white-space: nowrap;
}
.account__items-per-page-form .form-select {
  border-radius: 8px;
  border: 1px solid #ced4da;
  min-width: 70px;
}
.account__items-per-page-form .form-select:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
.account__items-per-page-form .d-inline-flex {
  align-items: center;
  gap: 0.5rem;
}
.account__pagination {
  margin-top: 2rem;
}
.account__pagination .pagination .page-item {
  margin: 0 2px;
}
.account__pagination .pagination .page-item .page-link {
  border-radius: 6px;
  border: 1px solid #dee2e6;
  color: #3498db;
  padding: 0.5rem 0.75rem;
  font-weight: 500;
  transition: all 0.2s ease;
  min-width: 40px;
  text-align: center;
  background-color: #fff;
}
.account__pagination .pagination .page-item .page-link:hover {
  background-color: #e9ecef;
  border-color: #adb5bd;
  color: #0056b3;
  text-decoration: none;
}
.account__pagination .pagination .page-item .page-link:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  z-index: 3;
}
.account__pagination .pagination .page-item.active .page-link {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
  font-weight: 600;
}
.account__pagination .pagination .page-item.active .page-link:hover {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}
.account__pagination .pagination .page-item.disabled .page-link {
  color: #6c757d;
  background-color: #fff;
  border-color: #dee2e6;
  cursor: not-allowed;
  opacity: 0.65;
}
.account__pagination .pagination .page-item.disabled .page-link:hover {
  background-color: #fff;
  border-color: #dee2e6;
  color: #6c757d;
}
.account__pagination .pagination .page-item:first-child .page-link,
.account__pagination .pagination .page-item:last-child .page-link {
  font-weight: bold;
  font-size: 1.1rem;
}
.account__pagination-info {
  margin-top: 1.5rem;
}
.account__pagination-info small {
  font-size: 0.875rem;
  color: #6c757d;
  display: block;
  margin-bottom: 0.5rem;
}
.account__quick-jump .form-control {
  border-radius: 8px;
  border: 1px solid #ced4da;
  text-align: center;
  font-weight: 500;
}
.account__quick-jump .form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
.account__quick-jump .btn {
  border-radius: 8px;
  font-weight: 500;
}
.account__quick-jump .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
}
.account__quick-jump small {
  font-weight: 500;
  color: #495057;
}
@media (max-width: 768px) {
  .account {
    padding: 1rem;
  }
  .account__header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  .account__title {
    font-size: 1.5rem;
  }
  .account__table--wrapper {
    overflow-x: auto;
  }
  .account__table {
    min-width: 600px;
  }
  .account__table thead th,
  .account__table tbody td {
    padding: 0.75rem;
    font-size: 0.8rem;
  }
  .account__btn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }
  .account__btn--sm {
    padding: 0.4rem 0.8rem;
    font-size: 0.75rem;
  }
}
.account .account-checkbox,
.account #selectAll {
  transform: scale(1.2);
  margin: 0;
  cursor: pointer;
  border: 1px solid #ccc;
}
.account th:first-child,
.account td:first-child {
  width: 50px;
  text-align: center;
  padding: 1rem !important;
  padding-left: 3rem !important;
}
.account td:first-child {
  color: #000000;
}

.account-form {
  background: white;
  border-radius: 12px;
  box-shadow: 0px 0px 30px 9px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin: 2rem auto;
}
.account-form__header {
  background: #3498db;
  color: white;
  padding: 2rem;
  text-align: left;
}
.account-form__header h3 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
}
.account-form__body {
  padding: 2rem;
}
.account-form .form-label {
  font-weight: 600;
  color: #2c3e50;
  margin: 1rem 0;
  display: block;
}
.account-form .form-control,
.account-form .form-select {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}
.account-form .form-control:focus,
.account-form .form-select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  outline: none;
}
.account-form .btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1.2rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
}
.account-form .btn:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.account-form .btn-primary {
  background: #3498db;
  border: 1px solid #3498db;
}
.account-form .btn-primary:hover {
  color: #040404;
  background: transparent;
}
.account-form .btn-secondary {
  background: #6c757d;
  border: none;
}
.account-form .btn-secondary:hover {
  background: #5a6268;
}

.role {
  margin-left: 240px;
  padding: 2rem;
  height: 100%;
  min-width: calc(100% - 260px);
  overflow: auto;
}
.role__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: white;
  padding: 1.5rem 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}
.role__items-per-page-form .form-label {
  font-size: 0.9rem;
  font-weight: 500;
  white-space: nowrap;
}
.role__items-per-page-form .form-select {
  border-radius: 8px;
  border: 1px solid #ced4da;
  min-width: 70px;
}
.role__items-per-page-form .form-select:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
.role__items-per-page-form .d-inline-flex {
  align-items: center;
  gap: 0.5rem;
}
.role__search-section {
  margin-bottom: 2rem;
}
.role__search-section .role__search-form .input-group {
  max-width: 400px;
}
.role__search-section .role__search-form .input-group > button {
  border-top-right-radius: 8px !important;
  border-bottom-right-radius: 8px !important;
}
.role__search-section .role__search-form .input-group .form-control {
  padding: 1rem;
  font-size: 1rem;
  border-radius: 8px;
}
.role__search-section .role__search-form .input-group .form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
.role__search-section .role__search-form .input-group .btn {
  padding: 1rem;
  font-size: 1.2rem;
  background: #3498db;
  color: #ffffff;
  border-radius: 8px;
  border: 1px solid #3498db;
}
.role__search-section .role__search-form .input-group .btn:hover {
  background: transparent;
  color: #040404;
  border-color: #3498db;
}
.role__search-section .role__search-form .input-group .btn-outline-danger {
  position: absolute;
  right: 75px;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  color: #ccc;
  border: none;
}
.role__search-section .role__search-form .input-group .btn-outline-danger:hover {
  color: #5b5b5b;
}
.role__search-section .role__info {
  font-size: 0.9rem;
  color: #666;
}
.role__title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.role__btn {
  padding: 0.75rem 2.5rem;
  border: 1px solid transparent;
  outline: none;
  border-radius: 5px;
  font-weight: 500;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.2rem;
  line-height: 1.5;
}
.role__btn:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.role__btn--primary {
  background: #3498db;
  color: white;
}
.role__btn--primary:hover {
  background: #f8f9fa;
  color: #040404;
  border-color: #3498db;
}
.role__btn--secondary {
  background: #6c757d;
  color: white;
}
.role__btn--secondary:hover {
  background: #5a6268;
  color: white;
}
.role__btn--warning {
  border: 1px solid #ccc;
}
.role__btn--warning:hover {
  background: #ffffff;
  color: #959595;
}
.role__btn--danger {
  border: 1px solid #e53935;
  color: #e53935;
}
.role__btn--danger:hover {
  background: #ff5252;
  color: white;
}
.role__btn--sm {
  padding: 0.8rem 1rem;
  font-size: 0.8rem;
}
.role__table--wrapper {
  background: white;
  box-shadow: 0px 0px 30px 9px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border-radius: 10px;
}
.role__table {
  width: 100%;
  margin: 0;
  background: white;
}
.role__table th {
  background: #3498db;
  color: white;
  padding: 2rem;
  text-align: center;
  font-weight: 600;
  font-size: 1.2rem;
  white-space: nowrap;
}
.role__table td {
  padding: 1.5rem;
  border-bottom: 1px solid #e9ecef;
  vertical-align: middle;
  font-size: 1.2rem;
  text-align: center;
  white-space: nowrap;
}
.role__table td:nth-child(3) {
  text-align: left;
  text-overflow: ellipsis;
  overflow: hidden;
}
.role__table-empty {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  padding: 10rem !important;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}
.role__badge {
  padding: 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  border: none;
  cursor: default;
  transition: all 0.3s ease;
}
.role__badge--success {
  background: #28a745;
  color: #fff;
}
.role__badge--inactive {
  background: #dc3545;
  color: #fff;
}
.role__badge--toggle {
  cursor: pointer;
  transition: all 0.3s ease;
}
.role__badge--toggle:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.role__badge--toggle:active {
  transform: translateY(0);
}
.role__badge--toggle:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}
.role__user-info {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #495057;
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  border: 1px solid #dee2e6;
  transition: all 0.3s ease;
}
.role__user-info:hover {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.role__user-info i {
  color: #6c757d;
  font-size: 0.875rem;
}
.role__form--delete {
  display: inline-block;
  margin-left: 0.5rem;
}
.role .status-changing {
  animation: pulse 0.5s ease-in-out;
}
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}
@media (max-width: 768px) {
  .role {
    padding: 1rem;
  }
  .role__header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  .role__title {
    font-size: 1.5rem;
  }
  .role__table--wrapper {
    overflow-x: auto;
  }
  .role__table {
    min-width: 600px;
  }
  .role__table thead th,
  .role__table tbody td {
    padding: 0.75rem;
    font-size: 0.8rem;
  }
  .role__btn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }
  .role__btn--sm {
    padding: 0.4rem 0.8rem;
    font-size: 0.75rem;
  }
}
.role__pagination {
  margin-top: 2rem;
}
.role__pagination .pagination .page-item {
  margin: 0 2px;
}
.role__pagination .pagination .page-item .page-link {
  border-radius: 6px;
  border: 1px solid #dee2e6;
  color: #3498db;
  padding: 0.5rem 0.75rem;
  font-weight: 500;
  transition: all 0.2s ease;
  min-width: 40px;
  text-align: center;
  background-color: #fff;
}
.role__pagination .pagination .page-item .page-link:hover {
  background-color: #e9ecef;
  border-color: #adb5bd;
  color: #0056b3;
  text-decoration: none;
}
.role__pagination .pagination .page-item .page-link:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  z-index: 3;
}
.role__pagination .pagination .page-item.active .page-link {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
  font-weight: 600;
}
.role__pagination .pagination .page-item.active .page-link:hover {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}
.role__pagination .pagination .page-item.disabled .page-link {
  color: #6c757d;
  background-color: #fff;
  border-color: #dee2e6;
  cursor: not-allowed;
  opacity: 0.65;
}
.role__pagination .pagination .page-item.disabled .page-link:hover {
  background-color: #fff;
  border-color: #dee2e6;
  color: #6c757d;
}
.role__pagination .pagination .page-item:first-child .page-link,
.role__pagination .pagination .page-item:last-child .page-link {
  font-weight: bold;
  font-size: 1.1rem;
}
.role__pagination-info {
  margin-top: 1.5rem;
}
.role__pagination-info small {
  font-size: 0.875rem;
  color: #6c757d;
  display: block;
  margin-bottom: 0.5rem;
}

.role-form {
  background: white;
  border-radius: 12px;
  box-shadow: 0px 0px 30px 9px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin: 2rem auto;
}
.role-form__header {
  background: #3498db;
  color: white;
  padding: 2rem;
  text-align: left;
}
.role-form__header h3 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
}
.role-form__body {
  padding: 2rem;
}
.role-form .form-label {
  font-weight: 600;
  color: #2c3e50;
  margin: 1rem 0;
  display: block;
}
.role-form .form-control,
.role-form .form-select {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}
.role-form .form-control:focus,
.role-form .form-select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  outline: none;
}
.role-form .btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1.2rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
}
.role-form .btn:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.role-form .btn-primary {
  background: #3498db;
  border: 1px solid #3498db;
}
.role-form .btn-primary:hover {
  color: #040404;
  background: transparent;
}
.role-form .btn-secondary {
  background: #6c757d;
  border: none;
}
.role-form .btn-secondary:hover {
  background: #5a6268;
}

.permissions {
  margin-left: 240px;
  padding: 2rem;
  height: 100%;
  min-width: calc(100% - 260px);
  overflow: auto;
}
.permissions__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: white;
  padding: 1.5rem 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}
.permissions__title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.permissions__actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}
.permissions__btn--delete-selected {
  animation: slideIn 0.3s ease-out;
}
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
.permissions__btn {
  padding: 0.75rem 2.5rem;
  border: 1px solid transparent;
  outline: none;
  border-radius: 5px;
  font-weight: 500;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.2rem;
  line-height: 1.5;
}
.permissions__btn:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.permissions__btn--primary {
  background: #3498db;
  color: white;
}
.permissions__btn--primary:hover {
  background: #f8f9fa;
  color: #040404;
  border-color: #3498db;
}
.permissions__btn--secondary {
  background: #6c757d;
  color: white;
}
.permissions__btn--secondary:hover {
  background: #5a6268;
  color: white;
}
.permissions__btn--warning {
  border: 1px solid #ccc;
}
.permissions__btn--warning:hover {
  background: #ffffff;
  color: #959595;
}
.permissions__btn--danger {
  border: 1px solid #e53935;
  color: #e53935;
}
.permissions__btn--danger:hover {
  background: #ff5252;
  color: white;
}
.permissions__btn--sm {
  padding: 0.8rem 1rem;
  font-size: 0.8rem;
}
.permissions__table--wrapper {
  width: 100%;
  margin: 0;
  background: white;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  transition: box-shadow 0.3s ease;
}
.permissions__table--wrapper:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}
.permissions__table--wrapper table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}
.permissions__table--wrapper th {
  background: #3498db;
  color: white;
  padding: 2rem;
  font-weight: 600;
  font-size: 1.4rem;
  white-space: nowrap;
  border: none;
}
.permissions__table--wrapper td {
  padding: 1.5rem;
  border-bottom: 1px solid #e9ecef;
  vertical-align: middle;
  font-size: 1.2rem;
  background: white;
  letter-spacing: 0.01rem;
  border-left: none;
  border-right: none;
  background: none !important;
}
.permissions__table--wrapper td:first-child {
  font-weight: 600;
}
.permissions__table--wrapper td:nth-child(3) {
  text-align: center;
}
.permissions__table .module-cell {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  font-weight: 700;
  color: #495057;
  vertical-align: middle;
  border-right: 3px solid #dee2e6 !important;
  position: relative;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}
.permissions__table .module-cell::after {
  content: "";
  position: absolute;
  right: -2px;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(135deg, #6c5ce7, #a29bfe);
  box-shadow: 0 0 8px rgba(108, 92, 231, 0.3);
}
.permissions__table .module-group-start {
  border-top: 3px solid #6c5ce7 !important;
  box-shadow: 0 -2px 8px rgba(108, 92, 231, 0.1);
}
.permissions__table .module-group-start td {
  border-top: 3px solid #6c5ce7 !important;
}
.permissions__table tr:nth-child(even) {
  background-color: #f8f9fa;
}
.permissions__table tr:hover {
  background-color: #e3f2fd;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
  transition: all 0.2s ease;
}
.permissions__table input[type=checkbox] {
  width: 18px;
  height: 18px;
  border: 2px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}
.permissions__table input[type=checkbox]:checked {
  background-color: #6c5ce7;
  border-color: #6c5ce7;
  box-shadow: 0 0 10px rgba(108, 92, 231, 0.3);
}
.permissions__table input[type=checkbox]:hover {
  border-color: #6c5ce7;
  box-shadow: 0 0 5px rgba(108, 92, 231, 0.2);
}

.order {
  margin-left: 230px;
  padding: 2rem;
  height: 100%;
  width: calc(100% - 240px);
  overflow-x: hidden;
}
.order__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: white;
  padding: 2.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}
.order__controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}
.order__controls .search-container .input-group {
  min-width: 400px;
}
.order__controls .search-container .input-group > button {
  border-top-right-radius: 8px !important;
  border-bottom-right-radius: 8px !important;
}
.order__controls .search-container .input-group .form-control {
  padding: 1rem;
  font-size: 1rem;
  border-radius: 8px;
}
.order__controls .search-container .input-group .form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
.order__controls .search-container .input-group .btn {
  padding: 1rem;
  font-size: 1.2rem;
  background: #3498db;
  color: #ffffff;
  border-radius: 8px;
  border: 1px solid #3498db;
}
.order__controls .search-container .input-group .btn:hover {
  background: transparent;
  color: #040404;
  border-color: #3498db;
}
.order__controls .search-container .input-group .btn-outline-danger {
  position: absolute;
  right: 75px;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  color: #ccc;
  border: none;
}
.order__controls .search-container .input-group .btn-outline-danger:hover {
  color: #5b5b5b;
}
.order__controls .search-container .input-group .btn-outline-danger i {
  margin-right: 0.5rem;
}
.order__title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.order__filter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 0rem;
}
.order__filter .status-tabs {
  display: flex;
  gap: 1rem;
}
.order__filter .status-select {
  min-width: 150px;
}
.order__filter .status-select .form-select {
  padding: 0.75rem 1rem;
  border-radius: 5px;
  font-weight: 500;
  font-size: 1rem;
  border: 1px solid #ced4da;
  cursor: pointer;
}
.order__filter .status-select .form-select:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
.order__btn {
  padding: 0.75rem 2rem;
  border: 1px solid transparent;
  outline: none;
  border-radius: 5px;
  font-weight: 500;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  line-height: 1.5;
}
.order__btn:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.order__btn--outline {
  background: #fff;
  color: #6c757d;
  border: 1px solid #6c757d;
}
.order__btn--outline:hover, .order__btn--outline.active {
  background: #6c757d;
  color: white;
}
.order__btn--outline-warning {
  background: #fff;
  color: #ffc107;
  border: 1px solid #ffc107;
}
.order__btn--outline-warning:hover, .order__btn--outline-warning.active {
  background: #ffc107;
  color: #212529;
}
.order__btn--outline-success {
  background: #fff;
  color: #28a745;
  border: 1px solid #28a745;
}
.order__btn--outline-success:hover, .order__btn--outline-success.active {
  background: #28a745;
  color: white;
}
.order__btn--outline-danger {
  background: #fff;
  color: #dc3545;
  border: 1px solid #dc3545;
}
.order__btn--outline-danger:hover, .order__btn--outline-danger.active {
  background: #dc3545;
  color: white;
}
.order__btn--primary {
  background: #3498db;
  color: white;
}
.order__btn--primary:hover {
  background: #f8f9fa;
  color: #040404;
  border-color: #3498db;
}
.order__btn--secondary {
  background: #6c757d;
  color: white;
}
.order__btn--secondary:hover {
  background: #5a6268;
  color: white;
}
.order__btn--warning {
  border: 1px solid #ccc;
}
.order__btn--warning:hover {
  background: #ffffff;
  color: #959595;
}
.order__btn--danger {
  border: 1px solid #e53935;
  color: #e53935;
}
.order__btn--danger:hover {
  background: #ff5252;
  color: white;
}
.order__btn--sm {
  padding: 0.5rem 0.75rem;
  font-size: 0.8rem;
}
.order__table--wrapper {
  background: white;
  box-shadow: 0px 0px 30px 9px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  overflow: hidden;
  width: 100%;
}
.order__table {
  width: 100%;
  margin: 0;
  background: white;
  table-layout: fixed;
  border-collapse: collapse;
}
.order__table th {
  background: #3498db;
  color: white;
  padding: 1.2rem 0.5rem;
  text-align: center;
  font-weight: 600;
  font-size: 0.9rem;
  position: sticky;
  top: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.order__table td {
  padding: 0.8rem 0.5rem;
  border-bottom: 1px solid #e9ecef;
  vertical-align: middle;
  font-size: 0.85rem;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.order__table td:first-child {
  font-weight: 600;
}
.order__table-empty {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  padding: 5rem !important;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}
.order__badge {
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  border: none;
  cursor: default;
  transition: all 0.3s ease;
  white-space: nowrap;
}
.order__badge--pending {
  background: #ffc107;
  color: #212529;
}
.order__badge--confirmed {
  background: #28a745;
  color: #fff;
}
.order__badge--cancelled {
  background: #dc3545;
  color: #fff;
}
.order__badge--paid {
  background: #007bff;
  color: #fff;
}
.order__badge--unpaid {
  background: #6c757d;
  color: #fff;
}
.order__badge--completed {
  background: #28a745;
  color: #fff;
}
.order__pagination {
  margin-top: 2rem;
  display: flex;
  justify-content: center;
}
.order__pagination .pagination .page-item .page-link {
  padding: 0.5rem 1rem;
  color: #3498db;
  border: 1px solid #dee2e6;
  font-weight: 500;
}
.order__pagination .pagination .page-item .page-link:hover {
  background-color: #e9ecef;
}
.order__pagination .pagination .page-item.active .page-link {
  background-color: #3498db;
  border-color: #3498db;
  color: white;
}
.order__details {
  padding: 1rem 0;
}
.order__details-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}
.order__details-title {
  font-size: 1.4rem;
  font-weight: 600;
  color: #2c3e50;
}
.order__details-section {
  margin-bottom: 1.5rem;
  padding: 1.5rem;
  border-radius: 8px;
  background: #f8f9fa;
}
.order__details-label {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #495057;
}
.order__details-value {
  font-size: 0.95rem;
}
.order__details-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
}
.order__details-table th {
  background: #e9ecef;
  color: #495057;
  font-weight: 600;
  text-align: left;
  padding: 0.75rem;
}
.order__details-table td {
  padding: 0.75rem;
  border-bottom: 1px solid #dee2e6;
}
.order__details-table-total {
  font-weight: 600;
  text-align: right;
  padding: 1rem 0.75rem;
  border-top: 2px solid #495057;
}

.order-detail {
  padding: 1rem 0;
}
.order-detail__section {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 2rem;
}
.order-detail__section-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #e9ecef;
}
.order-detail__grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}
.order-detail__row {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}
.order-detail__label {
  font-weight: 500;
  color: #6c757d;
  font-size: 0.9rem;
}
.order-detail__value {
  font-weight: 500;
  color: #212529;
  font-size: 1rem;
}
.order-detail__tour {
  display: flex;
  gap: 2rem;
}
.order-detail__tour-info {
  flex: 1;
}
.order-detail__tour-image {
  flex: 1;
  max-width: 400px;
}
.order-detail__tour-image img {
  width: 100%;
  height: auto;
  border-radius: 8px;
  object-fit: cover;
}
.order-detail__actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
}

@media (max-width: 992px) {
  .order-detail__grid {
    grid-template-columns: 1fr;
  }
  .order-detail__tour {
    flex-direction: column;
  }
  .order-detail__tour-image {
    max-width: 100%;
  }
  .order-detail__actions {
    flex-wrap: wrap;
    justify-content: center;
  }
  .order-detail__actions .order__btn {
    flex: 1 0 auto;
    text-align: center;
    justify-content: center;
  }
}
@media (max-width: 1400px) {
  .order__table td, .order__table th {
    padding: 0.7rem 0.4rem;
    font-size: 0.8rem;
  }
  .order__badge {
    padding: 0.3rem 0.6rem;
    font-size: 0.7rem;
  }
}
@media (max-width: 992px) {
  .order {
    margin-left: 0;
    width: 100%;
    padding: 1rem;
  }
  .order__header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
  }
  .order__controls {
    width: 100%;
  }
  .order__controls .search-container .input-group {
    min-width: auto;
    width: 100%;
  }
  .order__filter {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  .order__filter .status-select {
    width: 100%;
  }
}
body .select2-container {
  width: 100% !important;
}
body .select2-container .select2-selection {
  border: 2px solid #e1e5e9;
  border-radius: 0.375rem;
  min-height: 38px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
body .select2-container .select2-selection:focus, body .select2-container .select2-selection:focus-within {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
  outline: 0;
}
body .select2-container .select2-selection--single {
  padding: 0.375rem 0.75rem;
}
body .select2-container .select2-selection--single .select2-selection__rendered {
  color: #040404;
  padding: 0;
  margin: 0;
  font-size: 1.2rem;
  line-height: 2.2 !important;
}
body .select2-container .select2-selection--single .select2-selection__arrow {
  height: 100%;
  right: 0.75rem;
}
body .select2-container .select2-selection--single .select2-selection__arrow b {
  border-color: #6c757d transparent transparent transparent;
  border-style: solid;
  border-width: 5px 4px 0 4px;
  height: 0;
  left: 50%;
  margin-left: -4px;
  margin-top: -2px;
  position: absolute;
  top: 50%;
  width: 0;
}
body .select2-container .select2-selection--single .select2-selection__placeholder {
  color: #6c757d;
  font-size: 1.2rem;
}
body .select2-container .select2-selection--multiple {
  padding: 0.2rem 0.5rem;
}
body .select2-container .select2-selection--multiple .select2-selection__rendered {
  margin: 0;
  padding: 0;
}
body .select2-container .select2-selection--multiple .select2-selection__rendered .select2-selection__choice {
  background-color: #007bff;
  border: 1px solid #007bff;
  border-radius: 0.25rem;
  color: #fff;
  font-size: 0.875rem;
  margin: 0.2rem 0.2rem 0.2rem 0;
  padding: 0.25rem 0.5rem;
}
body .select2-container .select2-selection--multiple .select2-selection__rendered .select2-selection__choice .select2-selection__choice__remove {
  color: #fff;
  float: right;
  font-weight: bold;
  margin-left: 0.5rem;
}
body .select2-container .select2-selection--multiple .select2-selection__rendered .select2-selection__choice .select2-selection__choice__remove:hover {
  color: #f8f9fa;
}
body .select2-container--open .select2-selection {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}
body .select2-container--disabled .select2-selection {
  background-color: #e9ecef;
  opacity: 1;
  cursor: not-allowed;
}

.select2-dropdown {
  border: 1px solid #ced4da;
  border-radius: 0.375rem;
  background-color: #fff;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}
.select2-dropdown .select2-search {
  display: none;
}
.select2-dropdown .select2-results .select2-results__options {
  max-height: 200px;
  overflow-y: auto;
}
.select2-dropdown .select2-results .select2-results__options .select2-results__option {
  padding: 0.5rem 0.75rem;
  font-size: 1.2rem;
  color: #212529;
  cursor: pointer;
}
.select2-dropdown .select2-results .select2-results__options .select2-results__option:hover, .select2-dropdown .select2-results .select2-results__options .select2-results__option[aria-selected=true] {
  background-color: #f8f9fa;
}
.select2-dropdown .select2-results .select2-results__options .select2-results__option--highlighted[aria-selected] {
  background-color: #007bff;
  color: #fff;
}
.select2-dropdown .select2-results .select2-results__options .select2-results__option--disabled {
  color: #6c757d;
  cursor: not-allowed;
}
.select2-dropdown .select2-results .select2-results__options .select2-results__group {
  color: #6c757d;
  font-weight: 600;
  padding: 0.5rem 0.75rem;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}
.select2-dropdown .select2-results .select2-results__message {
  color: #6c757d;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
}

.is-invalid + .select2-container .select2-selection {
  border-color: #dc3545;
}
.is-invalid + .select2-container .select2-selection:focus, .is-invalid + .select2-container .select2-selection:focus-within {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.is-valid + .select2-container .select2-selection {
  border-color: #198754;
}
.is-valid + .select2-container .select2-selection:focus, .is-valid + .select2-container .select2-selection:focus-within {
  border-color: #198754;
  box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
}

.form-select-sm + .select2-container .select2-selection {
  min-height: 31px;
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.form-select-lg + .select2-container .select2-selection {
  min-height: 48px;
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
}

@media (prefers-color-scheme: dark) {
  .select2-container .select2-selection {
    background-color: #212529;
    border-color: #495057;
    color: #fff;
  }
  .select2-container .select2-selection--single .select2-selection__rendered {
    color: #fff;
  }
  .select2-container .select2-selection--single .select2-selection__placeholder {
    color: #adb5bd;
  }
  .select2-container--disabled .select2-selection {
    background-color: #343a40;
  }
  .select2-dropdown {
    background-color: #212529;
    border-color: #495057;
  }
  .select2-dropdown .select2-search .select2-search__field {
    background-color: #212529;
    border-color: #495057;
    color: #fff;
  }
  .select2-dropdown .select2-search .select2-search__field::placeholder {
    color: #adb5bd;
  }
  .select2-dropdown .select2-results .select2-results__options .select2-results__option {
    color: #fff;
  }
  .select2-dropdown .select2-results .select2-results__options .select2-results__option:hover, .select2-dropdown .select2-results .select2-results__options .select2-results__option[aria-selected=true] {
    background-color: #343a40;
  }
  .select2-dropdown .select2-results .select2-results__options .select2-results__option--highlighted[aria-selected] {
    background-color: #0d6efd;
  }
  .select2-dropdown .select2-results .select2-results__options .select2-results__option--disabled {
    color: #6c757d;
  }
  .select2-dropdown .select2-results .select2-results__options .select2-results__group {
    color: #adb5bd;
  }
  .select2-dropdown .select2-results .select2-results__message {
    color: #adb5bd;
  }
}

/*# sourceMappingURL=main.css.map */
