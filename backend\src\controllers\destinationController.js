const Destination = require("../models/destinationModel");
const { cloudinary } = require("../config/cloudinary");
const {
    deleteImageFromCloudinary,
    deleteMultipleImagesFromCloudinary,
} = require("../utils/imageUtils");
const { generateSlug, checkNameExists } = require("../utils/slugGenerator");

// Helper function để validate parent destination
async function validateParentDestination(parentId) {
    if (!parentId || parentId.trim() === "") {
        return { isValid: true, parentId: null };
    }

    const mongoose = require("mongoose");
    if (!mongoose.Types.ObjectId.isValid(parentId)) {
        return { isValid: false, error: "Điểm đến cha không hợp lệ" };
    }

    // Kiểm tra parent có tồn tại không
    const parentExists = await Destination.findById(parentId);
    if (!parentExists) {
        return { isValid: false, error: "Điểm đến cha không tồn tại" };
    }

    return { isValid: true, parentId: parentId };
}

// Hiển thị danh sách điểm đến với phân trang
exports.list = async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 5;
        const search = req.query.search || "";
        const skip = (page - 1) * limit;

        // Build search query
        let searchQuery = {};
        if (search) {
            searchQuery = {
                $or: [
                    { name: { $regex: search, $options: "i" } },
                    { info: { $regex: search, $options: "i" } },
                    { createdBy: { $regex: search, $options: "i" } },
                ],
            };
        }

        const [destinations, total] = await Promise.all([
            Destination.find(searchQuery)
                .populate("parent")
                .skip(skip)
                .limit(limit)
                .sort({ _id: 1 }), // Sort from old to new (new records at bottom)
            Destination.countDocuments(searchQuery),
        ]);

        console.log("Destinations found:", destinations.length);
        console.log("Total count:", total);
        console.log("Sample destination:", destinations[0]);

        const totalPages = Math.ceil(total / limit);

        // Pagination data
        const pagination = {
            current: page,
            total: totalPages,
            limit: limit,
            hasPrev: page > 1,
            hasNext: page < totalPages,
            startIndex: skip,
            endIndex: Math.min(skip + limit, total),
            totalItems: total,
        };

        res.render("destination", {
            destinations,
            pagination,
            search,
            currentPage: page,
            totalPages,
            message: req.flash("success"),
            error: req.flash("error"),
            userPermissions: res.locals.userPermissions // Đảm bảo luôn truyền userPermissions
        });
    } catch (error) {
        console.error("Error in destination list:", error);
        req.flash("error", "Có lỗi xảy ra khi tải danh sách điểm đến");
        res.redirect("/destination");
    }
};

// Hiển thị form thêm mới
exports.showAddForm = async (req, res) => {
    try {
        // Truy vấn tất cả điểm đến hiện có để làm parent options
        const parentDestinations = await Destination.find({}).sort({ name: 1 }); // Sắp xếp theo tên để dễ tìm

        console.log(
            "Total destinations in database:",
            parentDestinations.length
        );
        console.log(
            "Available parent destinations:",
            parentDestinations.map((p) => ({
                id: p._id,
                name: p.name,
                parent: p.parent,
            }))
        );

        // Kiểm tra nếu database rỗng
        if (parentDestinations.length === 0) {
            console.warn(
                "Database không có điểm đến nào. Cần kiểm tra lại database."
            );
        }

        res.render("destination/add", {
            parentDestinations, // Sử dụng tên biến rõ ràng
            parents: parentDestinations, // Giữ lại để tương thích với view hiện tại
            message: req.flash("success"),
            error: req.flash("error"),
            userPermissions: res.locals.userPermissions // Đảm bảo luôn truyền userPermissions
        });
    } catch (error) {
        console.error("Error in showAddForm:", error);
        req.flash("error", "Có lỗi xảy ra khi tải form thêm mới");
        res.redirect("/destination");
    }
};

// Xử lý thêm mới (có upload ảnh)
exports.create = async (req, res) => {
    try {
        console.log("Create request body:", req.body);
        console.log("Create request file:", req.file);

        const { name, info, parent } = req.body;

        // Check if it's an AJAX request
        const isAjax = req.xhr || req.headers.accept?.indexOf("json") > -1;

        // Validate required fields
        if (!name || !name.trim()) {
            const errorMessage = "Tên điểm đến không được để trống";
            if (isAjax) {
                return res
                    .status(400)
                    .json({ success: false, message: errorMessage });
            }
            req.flash("error", errorMessage);
            return res.redirect("/destination/add");
        }

        if (name.trim().length < 2) {
            const errorMessage = "Tên điểm đến phải có ít nhất 2 ký tự";
            if (isAjax) {
                return res
                    .status(400)
                    .json({ success: false, message: errorMessage });
            }
            req.flash("error", errorMessage);
            return res.redirect("/destination/add");
        }

        // Check if destination name already exists
        const trimmedName = name.trim();
        console.log(
            "Checking for existing destination with name:",
            trimmedName
        );

        const nameExists = await checkNameExists(Destination, trimmedName);

        if (nameExists) {
            const errorMessage = "Tên điểm đến đã tồn tại";
            if (isAjax) {
                return res
                    .status(400)
                    .json({ success: false, message: errorMessage });
            }
            req.flash("error", errorMessage);
            return res.redirect("/destination/add");
        }

        let image = "";
        if (req.file) {
            // Cloudinary automatically provides the secure URL
            image = req.file.path; // This will be the Cloudinary URL
        }

        // Validate parent destination
        const parentValidation = await validateParentDestination(parent);
        if (!parentValidation.isValid) {
            const errorMessage = parentValidation.error;
            if (isAjax) {
                return res
                    .status(400)
                    .json({ success: false, message: errorMessage });
            }
            req.flash("error", errorMessage);
            return res.redirect("/destination/add");
        }

        // Generate unique slug
        const { slug, fullSlug } = generateSlug(trimmedName, "destination");

        const newDestination = {
            name: trimmedName,
            slug,
            fullSlug,
            info: info ? info.trim() : "",
            image,
            status: "Hoạt động", // Mặc định là Hoạt động
            parent: parentValidation.parentId,
            createdBy: req.user?.fullName || "System",
            updatedBy: req.user?.fullName || "System",
        };

        console.log("Creating destination with data:", newDestination);

        await Destination.create(newDestination);

        const successMessage = "Thêm điểm đến thành công!";

        // Check if this is an AJAX request
        if (isAjax) {
            return res.json({ success: true, message: successMessage });
        }

        req.flash("success", successMessage);
        res.redirect("/destination");
    } catch (error) {
        console.error("Error in create:", error);

        const isAjax = req.xhr || req.headers.accept?.indexOf("json") > -1;

        // Handle specific errors
        let errorMessage = "Có lỗi xảy ra khi tạo điểm đến";

        if (error.code === 11000) {
            if (error.keyPattern && error.keyPattern.name) {
                errorMessage = "Tên điểm đến đã tồn tại";
            } else if (error.keyPattern && error.keyPattern.slug) {
                errorMessage = "Slug điểm đến đã tồn tại";
            } else if (error.keyPattern && error.keyPattern.fullSlug) {
                errorMessage = "FullSlug điểm đến đã tồn tại";
            } else {
                errorMessage = "Dữ liệu đã tồn tại trong hệ thống";
            }
        } else if (error.name === "ValidationError") {
            const firstError = Object.values(error.errors)[0];
            errorMessage = firstError
                ? firstError.message
                : "Dữ liệu không hợp lệ";
        }

        // Check if this is an AJAX request
        if (isAjax) {
            return res
                .status(500)
                .json({ success: false, message: errorMessage });
        }

        req.flash("error", errorMessage);
        res.redirect("/destination/add");
    }
};

// Hiển thị form sửa
exports.showEditForm = async (req, res) => {
    try {
        const destination = await Destination.findById(req.params.id);
        if (!destination) {
            req.flash("error", "Không tìm thấy điểm đến");
            return res.redirect("/destination");
        }

        // Truy vấn tất cả điểm đến trừ chính nó để làm parent options
        const parentDestinations = await Destination.find({
            _id: { $ne: req.params.id },
        }).sort({ name: 1 });

        console.log(
            "Total parent options for edit:",
            parentDestinations.length
        );
        console.log("Current destination:", {
            id: destination._id,
            name: destination.name,
        });
        console.log(
            "Available parent destinations:",
            parentDestinations.map((p) => ({
                id: p._id,
                name: p.name,
            }))
        );

        // Kiểm tra nếu không có điểm đến nào khác
        if (parentDestinations.length === 0) {
            console.warn(
                "Không có điểm đến nào khác để làm parent cho:",
                destination.name
            );
        }

        res.render("destination/edit", {
            destination,
            parentDestinations, // Sử dụng tên biến rõ ràng
            parents: parentDestinations, // Giữ lại để tương thích với view hiện tại
            message: req.flash("success"),
            error: req.flash("error"),
            userPermissions: res.locals.userPermissions // Đảm bảo luôn truyền userPermissions
        });
    } catch (error) {
        console.error("Error in showEditForm:", error);
        req.flash("error", "Có lỗi xảy ra khi tải form chỉnh sửa");
        res.redirect("/destination");
    }
};

// Xử lý cập nhật
exports.update = async (req, res) => {
    try {
        console.log("Update request body:", req.body);
        console.log("Update request file:", req.file);

        const { name, info, parent } = req.body;
        const destinationId = req.params.id;

        // Check if it's an AJAX request
        const isAjax = req.xhr || req.headers.accept?.indexOf("json") > -1;

        // Validate required fields
        if (!name || !name.trim()) {
            const errorMessage = "Tên điểm đến không được để trống";
            if (isAjax) {
                return res
                    .status(400)
                    .json({ success: false, message: errorMessage });
            }
            req.flash("error", errorMessage);
            return res.redirect("/destination/edit/" + destinationId);
        }

        if (name.trim().length < 2) {
            const errorMessage = "Tên điểm đến phải có ít nhất 2 ký tự";
            if (isAjax) {
                return res
                    .status(400)
                    .json({ success: false, message: errorMessage });
            }
            req.flash("error", errorMessage);
            return res.redirect("/destination/edit/" + destinationId);
        }

        // Check if destination name already exists (excluding current destination)
        const trimmedName = name.trim();
        console.log(
            "Checking for existing destination with name (excluding current):",
            trimmedName,
            "destinationId:",
            destinationId
        );

        const nameExists = await checkNameExists(
            Destination,
            trimmedName,
            destinationId
        );

        if (nameExists) {
            const errorMessage = "Tên điểm đến đã tồn tại";
            if (isAjax) {
                return res
                    .status(400)
                    .json({ success: false, message: errorMessage });
            }
            req.flash("error", errorMessage);
            return res.redirect("/destination/edit/" + destinationId);
        }

        // Validate parent destination
        const parentValidation = await validateParentDestination(parent);
        if (!parentValidation.isValid) {
            const errorMessage = parentValidation.error;
            if (isAjax) {
                return res
                    .status(400)
                    .json({ success: false, message: errorMessage });
            }
            req.flash("error", errorMessage);
            return res.redirect("/destination/edit/" + destinationId);
        }

        // Generate unique slug
        const { slug, fullSlug } = generateSlug(trimmedName, "destination");

        let updateData = {
            name: trimmedName,
            slug,
            fullSlug,
            info: info ? info.trim() : "",
            parent: parentValidation.parentId,
            updatedBy: req.user?.fullName || "System",
        };

        if (req.file) {
            // Get old image URL to delete from Cloudinary if needed
            const oldDestination = await Destination.findById(destinationId);
            if (oldDestination && oldDestination.image) {
                // Delete old image from Cloudinary
                await deleteImageFromCloudinary(oldDestination.image);
            }

            updateData.image = req.file.path; // Cloudinary URL
        }

        console.log("Updating destination with data:", updateData);

        await Destination.findByIdAndUpdate(destinationId, updateData);

        const successMessage = "Cập nhật điểm đến thành công!";

        // Check if this is an AJAX request
        if (isAjax) {
            return res.json({ success: true, message: successMessage });
        }

        req.flash("success", successMessage);
        res.redirect("/destination");
    } catch (error) {
        console.error("Error in update:", error);

        const isAjax = req.xhr || req.headers.accept?.indexOf("json") > -1;

        // Handle specific errors
        let errorMessage = "Có lỗi xảy ra khi cập nhật điểm đến";

        if (error.code === 11000) {
            if (error.keyPattern && error.keyPattern.name) {
                errorMessage = "Tên điểm đến đã tồn tại";
            } else if (error.keyPattern && error.keyPattern.slug) {
                errorMessage = "Slug điểm đến đã tồn tại";
            } else if (error.keyPattern && error.keyPattern.fullSlug) {
                errorMessage = "FullSlug điểm đến đã tồn tại";
            } else {
                errorMessage = "Dữ liệu đã tồn tại trong hệ thống";
            }
        } else if (error.name === "ValidationError") {
            const firstError = Object.values(error.errors)[0];
            errorMessage = firstError
                ? firstError.message
                : "Dữ liệu không hợp lệ";
        }

        // Check if this is an AJAX request
        if (req.xhr || req.headers.accept.indexOf("json") > -1) {
            return res.status(500).json({
                success: false,
                message:
                    "Có lỗi xảy ra khi cập nhật điểm đến: " + error.message,
            });
        }

        req.flash(
            "error",
            "Có lỗi xảy ra khi cập nhật điểm đến: " + error.message
        );
        res.redirect("/destination/edit/" + req.params.id);
    }
};

// Xử lý xóa
exports.delete = async (req, res) => {
    try {
        console.log("Delete request for ID:", req.params.id);

        // Get destination to delete associated image
        const destination = await Destination.findById(req.params.id);
        if (destination && destination.image) {
            await deleteImageFromCloudinary(destination.image);
        }

        await Destination.findByIdAndDelete(req.params.id);

        // Check if this is an AJAX request
        if (req.xhr || req.headers.accept.indexOf("json") > -1) {
            return res.json({
                success: true,
                message: "Xóa điểm đến thành công!",
            });
        }

        req.flash("success", "Xóa điểm đến thành công!");
        res.redirect("/destination");
    } catch (error) {
        console.error("Error in delete:", error);

        // Check if this is an AJAX request
        if (req.xhr || req.headers.accept.indexOf("json") > -1) {
            return res.status(500).json({
                success: false,
                message: "Có lỗi xảy ra khi xóa điểm đến: " + error.message,
            });
        }

        req.flash("error", "Có lỗi xảy ra khi xóa điểm đến: " + error.message);
        res.redirect("/destination");
    }
};

// Toggle status
exports.toggleStatus = async (req, res) => {
    try {
        const destination = await Destination.findById(req.params.id);
        if (!destination) {
            return res
                .status(404)
                .json({ success: false, message: "Không tìm thấy điểm đến" });
        }

        const newStatus =
            destination.status === "Hoạt động" ? "Tạm dừng" : "Hoạt động";
        destination.status = newStatus;
        destination.updatedBy = req.user?.fullName || "System";
        await destination.save();

        res.json({
            success: true,
            status: newStatus,
            message: `Đã ${
                newStatus === "Hoạt động" ? "kích hoạt" : "tạm dừng"
            } điểm đến thành công`,
        });
    } catch (error) {
        console.error("Error toggling status:", error);
        res.status(500).json({
            success: false,
            message: "Có lỗi xảy ra khi thay đổi trạng thái",
        });
    }
};

// Delete multiple
exports.deleteMultiple = async (req, res) => {
    try {
        const { ids } = req.body;
        if (!ids || !Array.isArray(ids) || ids.length === 0) {
            return res
                .status(400)
                .json({ success: false, message: "Danh sách ID không hợp lệ" });
        }

        // Get destinations to delete associated images
        const destinations = await Destination.find({ _id: { $in: ids } });

        // Delete images from Cloudinary
        const imageUrls = destinations
            .map((dest) => dest.image)
            .filter(Boolean);
        if (imageUrls.length > 0) {
            await deleteMultipleImagesFromCloudinary(imageUrls);
        }

        const result = await Destination.deleteMany({ _id: { $in: ids } });

        res.json({
            success: true,
            message: `Đã xóa thành công ${result.deletedCount} điểm đến`,
        });
    } catch (error) {
        console.error("Error deleting multiple:", error);
        res.status(500).json({
            success: false,
            message: "Có lỗi xảy ra khi xóa điểm đến",
        });
    }
};

//-- API Methods --

// Lấy danh sách điểm đến API
exports.getAll = async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const search = req.query.search || "";
        const skip = (page - 1) * limit;

        // Build search query
        let searchQuery = {};
        if (search) {
            searchQuery = {
                $or: [
                    { name: { $regex: search, $options: "i" } },
                    { info: { $regex: search, $options: "i" } },
                    { createdBy: { $regex: search, $options: "i" } },
                ],
            };
        }

        const [destinations, total] = await Promise.all([
            Destination.find(searchQuery)
                .populate("parent")
                .skip(skip)
                .limit(limit)
                .sort({ _id: 1 }),
            Destination.countDocuments(searchQuery),
        ]);

        const totalPages = Math.ceil(total / limit);

        res.status(200).json({
            success: true,
            data: destinations,
            pagination: {
                current: page,
                total: totalPages,
                limit,
                totalItems: total,
            },
        });
    } catch (error) {
        console.error("Error in getAll destinations API:", error);
        res.status(500).json({
            success: false,
            message: "Lỗi khi tải danh sách điểm đến",
        });
    }
};

// Lấy thông tin điểm đến theo ID API
exports.getById = async (req, res) => {
    try {
        const destination = await Destination.findById(req.params.id).populate("parent");
        
        if (!destination) {
            return res.status(404).json({
                success: false,
                message: "Không tìm thấy điểm đến",
            });
        }

        res.status(200).json({
            success: true,
            data: destination,
        });
    } catch (error) {
        console.error("Error in getById destination API:", error);
        res.status(500).json({
            success: false,
            message: "Lỗi khi tải thông tin điểm đến",
        });
    }
};

// Tạo điểm đến mới qua API
exports.apiCreate = async (req, res) => {
    try {
        const { name, info, parent } = req.body;

        // Validate input
        if (!name || !name.trim()) {
            return res.status(400).json({
                success: false,
                message: "Tên điểm đến không được để trống",
            });
        }

        // Check if name already exists
        const nameExists = await checkNameExists(name, Destination);
        if (nameExists) {
            return res.status(400).json({
                success: false,
                message: "Tên điểm đến đã tồn tại",
            });
        }

        // Validate parent if provided
        const parentValidation = await validateParentDestination(parent);
        if (!parentValidation.isValid) {
            return res.status(400).json({
                success: false,
                message: parentValidation.error,
            });
        }

        // Handle image upload
        let imageUrl = "";
        let publicId = "";
        
        if (req.file && req.file.path) {
            imageUrl = req.file.path;
            publicId = req.file.filename;
        }

        // Generate slug
        const slug = generateSlug(name);

        const newDestination = new Destination({
            name: name.trim(),
            slug,
            info: info ? info.trim() : "",
            parent: parentValidation.parentId,
            image: imageUrl,
            cloudinaryId: publicId,
            status: "Hoạt động",
            createdBy: req.session.user ? req.session.user.name : "Admin",
        });

        await newDestination.save();

        res.status(201).json({
            success: true,
            message: "Thêm điểm đến thành công",
            data: newDestination,
        });
    } catch (error) {
        console.error("Error in apiCreate destination:", error);
        
        // Delete uploaded image if there was an error
        if (req.file && req.file.path && req.file.filename) {
            await deleteImageFromCloudinary(req.file.filename);
        }
        
        res.status(500).json({
            success: false,
            message: "Có lỗi xảy ra khi thêm điểm đến",
        });
    }
};

// Cập nhật điểm đến qua API
exports.apiUpdate = async (req, res) => {
    try {
        const { name, info, parent, status } = req.body;
        const destinationId = req.params.id;

        // Validate input
        if (!name || !name.trim()) {
            return res.status(400).json({
                success: false,
                message: "Tên điểm đến không được để trống",
            });
        }

        // Check if destination exists
        const existingDestination = await Destination.findById(destinationId);
        if (!existingDestination) {
            return res.status(404).json({
                success: false,
                message: "Không tìm thấy điểm đến",
            });
        }

        // Check if name already exists (excluding current destination)
        const nameExists = await Destination.findOne({ 
            name: name.trim(), 
            _id: { $ne: destinationId } 
        });
        
        if (nameExists) {
            return res.status(400).json({
                success: false,
                message: "Tên điểm đến đã tồn tại",
            });
        }

        // Validate parent if provided
        const parentValidation = await validateParentDestination(parent);
        if (!parentValidation.isValid) {
            return res.status(400).json({
                success: false,
                message: parentValidation.error,
            });
        }
        
        // Check if parent is set to itself
        if (parentValidation.parentId && parentValidation.parentId.toString() === destinationId) {
            return res.status(400).json({
                success: false,
                message: "Không thể đặt điểm đến làm cha của chính nó",
            });
        }

        // Handle image upload
        let imageUrl = existingDestination.image;
        let publicId = existingDestination.cloudinaryId;
        
        if (req.file && req.file.path) {
            // Delete old image if it exists
            if (existingDestination.cloudinaryId) {
                await deleteImageFromCloudinary(existingDestination.cloudinaryId);
            }
            
            imageUrl = req.file.path;
            publicId = req.file.filename;
        }

        // Update destination
        const updatedDestination = await Destination.findByIdAndUpdate(
            destinationId,
            {
                name: name.trim(),
                slug: generateSlug(name),
                info: info ? info.trim() : "",
                parent: parentValidation.parentId,
                image: imageUrl,
                cloudinaryId: publicId,
                status: status || existingDestination.status,
                updatedBy: req.session.user ? req.session.user.name : "Admin",
                updatedAt: new Date(),
            },
            { new: true }
        );

        res.status(200).json({
            success: true,
            message: "Cập nhật điểm đến thành công",
            data: updatedDestination,
        });
    } catch (error) {
        console.error("Error in apiUpdate destination:", error);
        
        // If there was a new file uploaded but update failed, delete it
        if (req.file && req.file.path && req.file.filename) {
            await deleteImageFromCloudinary(req.file.filename);
        }
        
        res.status(500).json({
            success: false,
            message: "Có lỗi xảy ra khi cập nhật điểm đến",
        });
    }
};

// Xóa điểm đến qua API
exports.apiDelete = async (req, res) => {
    try {
        const destinationId = req.params.id;
        
        // Check if destination exists
        const destination = await Destination.findById(destinationId);
        if (!destination) {
            return res.status(404).json({
                success: false,
                message: "Không tìm thấy điểm đến",
            });
        }
        
        // Check if there are child destinations
        const childDestinations = await Destination.find({ parent: destinationId });
        if (childDestinations.length > 0) {
            return res.status(400).json({
                success: false,
                message: "Không thể xóa điểm đến này vì có điểm đến con phụ thuộc",
            });
        }

        // Delete image from Cloudinary if it exists
        if (destination.cloudinaryId) {
            await deleteImageFromCloudinary(destination.cloudinaryId);
        }
        
        // Delete destination
        await Destination.findByIdAndDelete(destinationId);
        
        res.status(200).json({
            success: true,
            message: "Xóa điểm đến thành công",
        });
    } catch (error) {
        console.error("Error in apiDelete destination:", error);
        res.status(500).json({
            success: false,
            message: "Có lỗi xảy ra khi xóa điểm đến",
        });
    }
};

// Thay đổi trạng thái điểm đến qua API
exports.apiToggleStatus = async (req, res) => {
    try {
        const destinationId = req.params.id;
        
        // Check if destination exists
        const destination = await Destination.findById(destinationId);
        if (!destination) {
            return res.status(404).json({
                success: false,
                message: "Không tìm thấy điểm đến",
            });
        }
        
        // Toggle status
        const newStatus = destination.status === "Hoạt động" ? "Không hoạt động" : "Hoạt động";
        
        const updatedDestination = await Destination.findByIdAndUpdate(
            destinationId,
            {
                status: newStatus,
                updatedBy: req.session.user ? req.session.user.name : "Admin",
                updatedAt: new Date(),
            },
            { new: true }
        );
        
        res.status(200).json({
            success: true,
            message: `Đã ${newStatus === "Hoạt động" ? "kích hoạt" : "vô hiệu hóa"} điểm đến thành công`,
            data: updatedDestination,
        });
    } catch (error) {
        console.error("Error in apiToggleStatus destination:", error);
        res.status(500).json({
            success: false,
            message: "Có lỗi xảy ra khi thay đổi trạng thái điểm đến",
        });
    }
};
