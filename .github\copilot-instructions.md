# Hướng dẫn tùy chỉnh cho GitHub Copilot

## Ng<PERSON> cảnh dự án

Chúng ta đang xây dựng một trang web đặt tour du lịch có tích hợp chatbot AI để hỗ trợ khách hàng. Vui lòng tuân thủ nghiêm ngặt các hướng dẫn và quy tắc dưới đây khi đưa ra gợi ý.

---

##  Quy tắc chung

- **Ngôn ngữ:** Luôn luôn phản hồi, gi<PERSON>i thích và viết comment bằng **tiếng Việt**.
- **Tài liệu thiết kế:** Luôn tuân thủ tuyệt đối các yêu cầu và cấu trúc đã được định nghĩa trong file **"detailed design"** của dự án. Trước khi gợi ý code, hãy ưu tiên tham khảo tài liệu này.
- **<PERSON><PERSON><PERSON> nhất quán:** <PERSON><PERSON> trì sự nhất quán trong cách đặt tên, cấu trúc thư mục và phong cách code trên toàn bộ dự án.

---

## 💻 Frontend: ReactJS 19 (Vite + Tailwind CSS)

- **Framework:** Sử dụng **React 19** và các tính năng mới nhất (ví dụ: Actions, `use` hook).
- **Build tool:** Dự án sử dụng **Vite**. Các cấu hình và plugin phải tương thích với Vite.
- **Styling:**
    - Sử dụng **Tailwind CSS** cho tất cả các styling. Hạn chế tối đa việc viết CSS thuần hoặc inline styles.
    - Tận dụng các tính năng của Tailwind như `@apply`, `theme()` và các file cấu hình (`tailwind.config.js`).
- **Cấu trúc thư mục:**
    - `src/components/`: Cho các component tái sử dụng.
        - `ui/`: Các component giao diện cơ bản (Button, Input, Card,...).
        - `common/`: Các component chung cho toàn trang (Navbar, Footer,...).
        - `features/`: Các component phức tạp liên quan đến một tính năng cụ thể.
    - `src/pages/`: Cho các trang chính của ứng dụng.
    - `src/hooks/`: Cho các custom hook.
    - `src/lib/` hoặc `src/utils/`: Cho các hàm tiện ích.
    - `src/services/`: Cho các hàm gọi API.
- **State Management:** Ưu tiên sử dụng React Context hoặc các hook có sẵn của React (`useState`, `useReducer`, `use`) cho state management đơn giản. Cân nhắc dùng Zustand hoặc Jotai cho các state phức tạp, toàn cục.
- **Component:** Viết component dưới dạng function component sử dụng arrow function. Đặt tên file component theo định dạng `PascalCase.jsx`.

---

## ⚙️ Backend: Node.js (Express + Mongoose)

- **Framework:** Sử dụng **Express.js**.
- **Database:** Sử dụng **MongoDB** với ODM là **Mongoose**.
- **Mô hình kiến trúc:** Tuân thủ nghiêm ngặt mô hình **MVC (Model-View-Controller)**.
    - `models/`: Chứa các Mongoose schema và model. Tên file theo định dạng `tenModel.model.js`.
    - `controllers/`: Chứa logic xử lý request và response. Tên file theo định dạng `tenController.controller.js`.
    - `routes/`: Định nghĩa các API endpoint. Tên file theo định dạng `tenRoute.route.js`.
    - `services/`: (Tùy chọn nhưng khuyến khích) Tách biệt business logic khỏi controller.
    - `utils/`: Chứa các hàm tiện ích chung.
- **Xử lý bất đồng bộ:** Luôn sử dụng `async/await` cho các tác vụ bất đồng bộ.
- **Bảo mật:**
    - Sử dụng biến môi trường (`.env`) để lưu trữ các thông tin nhạy cảm (API keys, connection strings, JWT secret).
    - Validate và sanitize dữ liệu đầu vào từ người dùng.
    - Triển khai cơ chế xác thực (authentication) bằng JWT (JSON Web Tokens).

---

## 🎨 CSS/SCSS Style

- **SCSS 7-1 Pattern:** Mặc dù frontend dùng Tailwind, nếu có sử dụng SCSS ở đâu đó (ví dụ: trong một phần riêng của dự án), hãy tuân thủ cấu trúc **7-1 pattern**:
    - `abstracts/`: (variables, functions, mixins)
    - `vendors/`: (third-party stylesheets)
    - `base/`: (reset, typography, base styles)
    - `layout/`: (header, footer, grid)
    - `components/`: (buttons, cards, forms)
    - `pages/`: (page-specific styles)
    - `themes/`: (theme styles)
    - `main.scss`: File chính để import tất cả các file khác.

---

## 🌐 Quy tắc thiết kế RESTful API

- **Endpoint Naming:**
    - Sử dụng danh từ số nhiều để đặt tên cho resources (ví dụ: `/tours`, `/users`, `/orders`).
    - Sử dụng các HTTP methods một cách chính xác:
        - `GET`: Lấy dữ liệu.
        - `POST`: Tạo mới dữ liệu.
        - `PUT` / `PATCH`: Cập nhật dữ liệu.
        - `DELETE`: Xóa dữ liệu.
- **Versioning:** Đặt phiên bản API trong URL (ví dụ: `/api/v1/tours`).
- **Response Format:**
    - Luôn trả về dữ liệu dưới dạng JSON.
    - Sử dụng cấu trúc response nhất quán:
      ```json
      {
        "success": true, // hoặc false
        "message": "Thông báo thành công hoặc lỗi",
        "data": { ... } // hoặc null
      }
      ```
- **Status Codes:** Sử dụng các HTTP status code phù hợp:
    - `200 OK`: Request thành công.
    - `201 Created`: Tạo mới resource thành công.
    - `400 Bad Request`: Dữ liệu gửi lên không hợp lệ.
    - `401 Unauthorized`: Chưa xác thực.
    - `403 Forbidden`: Không có quyền truy cập.
    - `404 Not Found`: Không tìm thấy resource.
    - `500 Internal Server Error`: Lỗi từ phía server.