import { baseApi } from './baseApi'

export const orderApi = {
  // Đặt tour
  bookTour: async (orderData) => {
    try {
      const response = await baseApi.post('/api/orders/book-tour', orderData)
      return response.data
    } catch (error) {
      throw error
    }
  },

  // <PERSON><PERSON><PERSON> orders của user
  getUserOrders: async () => {
    try {
      const response = await baseApi.get('/api/orders/user')
      return response.data
    } catch (error) {
      throw error
    }
  },

  // Lấy chi tiết order
  getOrderDetail: async (orderId) => {
    try {
      const response = await baseApi.get(`/api/orders/${orderId}`)
      return response.data
    } catch (error) {
      throw error
    }
  },

  // Hủy order
  cancelOrder: async (orderId) => {
    try {
      const response = await baseApi.put(`/api/orders/${orderId}/cancel`)
      return response.data
    } catch (error) {
      throw error
    }
  },

  // Cập nhật trạng thái thanh toán
  updatePaymentStatus: async (orderId, paymentData) => {
    try {
      const response = await baseApi.put(`/api/orders/${orderId}/payment`, paymentData)
      return response.data
    } catch (error) {
      throw error
    }
  }
}

export default orderApi
