const nodemailer = require('nodemailer');

/**
 * Utility functions for sending emails
 */

// Create email transporter
const createTransporter = () => {
    // Use environment variables for configuration
    return nodemailer.createTransport({
        host: process.env.EMAIL_HOST || 'smtp.gmail.com',
        port: process.env.EMAIL_PORT || 587,
        secure: process.env.EMAIL_SECURE === 'true',
        auth: {
            user: process.env.EMAIL_USER,
            pass: process.env.EMAIL_PASSWORD
        }
    });
};

/**
 * Send OTP code to email
 * @param {string} email - Email address to send OTP to
 * @param {string} otp - OTP code
 * @returns {Promise} - Promise resolving to send info
 */
const sendOTPEmail = async (email, otp) => {
    try {
        const transporter = createTransporter();
        
        const mailOptions = {
            from: `"VietTravel" <${process.env.EMAIL_USER}>`,
            to: email,
            subject: 'M<PERSON> xác thực đăng ký tài khoản',
            html: `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
                    <div style="text-align: center; margin-bottom: 20px;">
                        <h2 style="color: #3498db;">Xác thực địa chỉ email</h2>
                    </div>
                    <div style="margin-bottom: 20px;">
                        <p>Xin chào,</p>
                        <p>Cảm ơn bạn đã đăng ký tài khoản tại VietTravel. Để hoàn tất quá trình đăng ký, vui lòng sử dụng mã xác thực sau:</p>
                    </div>
                    <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center; margin-bottom: 20px;">
                        <h1 style="font-size: 32px; letter-spacing: 5px; margin: 0; color: #3498db;">${otp}</h1>
                    </div>
                    <div style="margin-bottom: 20px;">
                        <p>Mã xác thực này sẽ hết hạn sau 5 phút.</p>
                        <p>Nếu bạn không yêu cầu mã này, vui lòng bỏ qua email này.</p>
                    </div>
                    <div style="border-top: 1px solid #e0e0e0; padding-top: 20px; text-align: center; color: #7f8c8d; font-size: 12px;">
                        <p>© ${new Date().getFullYear()} VietTravel. Tất cả các quyền được bảo lưu.</p>
                        <p>Đây là email tự động, vui lòng không trả lời email này.</p>
                    </div>
                </div>
            `
        };
        
        return await transporter.sendMail(mailOptions);
    } catch (error) {
        console.error('Error sending OTP email:', error);
        throw error;
    }
};

/**
 * Generate a random OTP code of specified length
 * @param {number} length - Length of OTP code (default: 6)
 * @returns {string} OTP code
 */
const generateOTP = (length = 6) => {
    // Generate a random number with specified length
    const min = Math.pow(10, length - 1);
    const max = Math.pow(10, length) - 1;
    return Math.floor(min + Math.random() * (max - min + 1)).toString();
};

/**
 * Validate email format
 * @param {string} email - Email to validate
 * @returns {boolean} Whether the email is valid
 */
const validateEmail = (email) => {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email);
};

/**
 * Check if OTP requests for an email should be rate limited
 * @param {Array} attempts - Array of previous attempts with timestamps
 * @param {number} maxAttempts - Maximum number of attempts allowed
 * @param {number} timeWindow - Time window in hours
 * @returns {boolean} Whether the request should be rate limited
 */
const isRateLimited = (attempts = [], maxAttempts = 3, timeWindow = 1) => {
    if (!attempts.length) return false;
    
    // Filter attempts within the time window
    const now = Date.now();
    const windowStart = now - (timeWindow * 60 * 60 * 1000); // Convert hours to milliseconds
    const recentAttempts = attempts.filter(timestamp => timestamp > windowStart);
    
    return recentAttempts.length >= maxAttempts;
};

module.exports = {
    sendOTPEmail,
    generateOTP,
    validateEmail,
    isRateLimited
}; 