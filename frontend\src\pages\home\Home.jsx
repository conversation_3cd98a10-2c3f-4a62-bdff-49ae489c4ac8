import React, { useState, useEffect, useRef, useCallback } from "react";
import Slider from "react-slick";
import moment from "moment";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import Banner from "../../assets/images/TopBannerWeb.png";
import Oto from "../../assets/images/oto.png";
import TourTet from "../../assets/images/tourtTet.png";
import <PERSON><PERSON> from "../../assets/images/dimond.png";
import TieuChuan from "../../assets/images/tieuchuan.png";
import TietKiem from "../../assets/images/tietkiem.png";
import GiaTot from "../../assets/images/giatot.png";
import Slider1 from "../../assets/images/slider1.png";
import Slider2 from "../../assets/images/slider2.png";
import Slider3 from "../../assets/images/slider3.png";
import Slider4 from "../../assets/images/slider4.png";
import Slider5 from "../../assets/images/slider5.png";
import Vitri from "../../assets/images/vitri.png";
import Code from "../../assets/images/code.png";
import { useNavigate } from "react-router-dom";
import { Calendar, theme } from "antd";
import { debounce } from "lodash";
import { tourApi } from "../../api/tourApi";

const Home = () => {
  const images = [Slider1, Slider2, Slider3, Slider4, Slider5];
  const [reverse, setReverse] = useState(false);
  const [showCalendar, setShowCalendar] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [tourFeature, setTourFeature] = useState([]);
  const [featuredTours, setFeaturedTours] = useState([]);
  const [loading, setLoading] = useState(false);
  const [calendarSearch, setCalendarSearch] = useState("");
  const [selectedBudget, setSelectedBudget] = useState(null);
  const sliderRef = useRef(null);
  const navigate = useNavigate();
  const [destinations, setDestination] = useState([]);
  const [filterSearch, setFilterSearch] = useState({
    text: "",
    fromDate: "",
    budgetId: "",
  });
  const [dataSource, setDataSource] = useState({
    tours: 'loading', // 'api', 'mock', 'loading'
    destinations: 'loading'
  });

  const { token } = theme.useToken();
  const wrapperStyle = {
    width: 400,
    border: `1px solid ${token.colorBorderSecondary}`,
    borderRadius: token.borderRadiusLG,
  };



  // Test API connectivity
  const testApiConnectivity = async () => {
    try {
      const response = await fetch('http://localhost:3000/api/public/tours');
      if (response.ok) {
        const data = await response.json();
      } else {
        console.error('❌ API response not ok:', response.status);
      }
    } catch (error) {
      console.error('❌ API connection failed:', error);
    }
  };

  // Test khi component mount
  useEffect(() => {
    testApiConnectivity();
  }, []);

  // State để lưu destinations từ API
  const [allDestinations, setAllDestinations] = useState([]);

  const handleSearchInputChange = debounce((event) => {
    const value = event.target.value;
    setFilterSearch((prev) => ({ ...prev, text: value }));
    
    if (value && value.length >= 2) {
      // Gọi API để search destinations sử dụng public endpoint
      const searchDestinations = async () => {
        try {
          // Sử dụng destinations đã load sẵn hoặc gọi API nếu chưa có
          let destinationsToSearch = allDestinations;

          if (destinationsToSearch.length === 0) {
            const response = await tourApi.getDestinations();
            if (response && response.success && response.destinations) {
              destinationsToSearch = response.destinations;
              setAllDestinations(destinationsToSearch);
            }
          }

          if (destinationsToSearch.length > 0) {
            // Filter destinations từ API response
            const filteredDestinations = destinationsToSearch.filter(dest =>
              dest.name?.toLowerCase().includes(value.toLowerCase())
            );

            // Transform data để hiển thị
            const transformedDestinations = filteredDestinations.map(dest => ({
              id: dest._id,
              title: dest.name,
              description: dest.description || dest.info || '',
              image: dest.image || 'https://via.placeholder.com/50x50?text=No+Image'
            }));

            setDestination(transformedDestinations.slice(0, 10)); // Giới hạn 10 kết quả
          } else {
            setDestination([]);
          }
        } catch (error) {
          console.error('❌ Lỗi khi gọi API destinations:', error);
          setDestination([]);
        }
      };
      
      searchDestinations();
    } else {
      setDestination([]);
    }
  }, 300);
  // State để lưu categories từ API
  const [tourCategories, setTourCategories] = useState([]);

  // Default tour features với icons (fallback nếu không có categories từ API)
  const defaultTourFeatures = [
    {
      id: 1,
      title: "Tour Tiết Kiệm",
      description: "Giá tốt nhất thị trường",
      image: TietKiem,
      slug: "tour-tiet-kiem"
    },
    {
      id: 2,
      title: "Tour Giá Tốt",
      description: "Chất lượng cao, giá cả phải chăng",
      image: GiaTot,
      slug: "tour-gia-tot"
    },
    {
      id: 3,
      title: "Tour Tiêu Chuẩn",
      description: "Dịch vụ tiêu chuẩn quốc tế",
      image: TieuChuan,
      slug: "tour-tieu-chuan"
    },
    {
      id: 4,
      title: "Tour Cao Cấp",
      description: "Trải nghiệm đẳng cấp",
      image: Dimond,
      slug: "tour-cao-cap"
    }
  ];

  // Mock data cho featured tours
  const mockFeaturedTours = [
    {
      id: 1,
      title: "Du lịch Đà Nẵng - Hội An - Huế 4N3Đ",
      code: "DNHN001",
      slug: "da-nang-hoi-an-hue-4n3d",
      images: [
        { source: "https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=400&h=300&fit=crop" }
      ],
      departure: { title: "Hồ Chí Minh" },
      price: 13390000
    },
    {
      id: 2,
      title: "Du lịch Phú Quốc - Thiên đường biển đảo 3N2Đ",
      code: "PQ001",
      slug: "phu-quoc-thien-duong-bien-dao-3n2d",
      images: [
        { source: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop" }
      ],
      departure: { title: "Hồ Chí Minh" },
      price: 12230000
    },
    {
      id: 3,
      title: "Du lịch Sapa - Fansipan - Cát Cát 2N1Đ",
      code: "SA001",
      slug: "sapa-fansipan-cat-cat-2n1d",
      images: [
        { source: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop" }
      ],
      departure: { title: "Hà Nội" },
      price: 2000000
    }
  ];

  // Load basic data khi component mount
  useEffect(() => {
    const loadBasicData = async () => {
      try {

        
        // Load departures, destinations, categories song song
        const [departuresRes, destinationsRes, categoriesRes] = await Promise.allSettled([
          tourApi.getDepartures(),
          tourApi.getDestinations(),
          tourApi.getCategories()
        ]);
        
        // Log kết quả
        if (departuresRes.status === 'fulfilled') {
        } else {
          console.error('❌ Failed to load departures:', departuresRes.reason);
        }

        if (destinationsRes.status === 'fulfilled') {
        } else {
          console.error('❌ Failed to load destinations:', destinationsRes.reason);
        }

        if (categoriesRes.status === 'fulfilled') {
        } else {
          console.error('❌ Failed to load categories:', categoriesRes.reason);
        }
        
      } catch (error) {
        console.error('❌ Lỗi khi load dữ liệu cơ bản:', error);
      }
    };
    
    loadBasicData();
  }, []);

  useEffect(() => {
    // Fetch data từ API sử dụng public endpoints
    const fetchData = async () => {
      setLoading(true);
      try {
        // Gọi public API để lấy featured tours
        const response = await tourApi.getAllTours({ featured: true, limit: 3 });

        if (response && response.success && response.tours && response.tours.length > 0) {
          
          // Transform data để đảm bảo có đầy đủ thông tin
          const transformedTours = response.tours.map(tour => ({
            ...tour,
            id: tour._id, // Thêm id để tương thích
            images: tour.images && tour.images.length > 0 
              ? tour.images.map(img => ({ source: img })) // Transform images array
              : tour.image 
                ? [{ source: tour.image }] // Sử dụng image đơn nếu có
                : [{ source: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop" }],
            departure: tour.departure ? 
              (typeof tour.departure === 'object' 
                ? { title: tour.departure.name || tour.departure.title } 
                : { title: tour.departure }) :
              { title: "Chưa xác định" },
            price: tour.price || tour.totalPrice || 0,
            slug: tour.slug || `tour-${tour._id}`
          }));
          
          setFeaturedTours(transformedTours);
          setDataSource(prev => ({ ...prev, tours: 'api' }));
        } else {
          // Thử gọi API tours không có filter featured
          const allToursResponse = await tourApi.getAllTours({ limit: 3 });
          if (allToursResponse && allToursResponse.success && allToursResponse.tours && allToursResponse.tours.length > 0) {
            
            const transformedTours = allToursResponse.tours.map(tour => ({
              ...tour,
              id: tour._id,
              images: tour.images && tour.images.length > 0 
                ? tour.images.map(img => ({ source: img }))
                : tour.image 
                  ? [{ source: tour.image }]
                  : [{ source: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop" }],
              departure: tour.departure ? 
                (typeof tour.departure === 'object' 
                  ? { title: tour.departure.name || tour.departure.title } 
                  : { title: tour.departure }) :
                { title: "Chưa xác định" },
              price: tour.price || tour.totalPrice || 0,
              slug: tour.slug || `tour-${tour._id}`
            }));
            
            setFeaturedTours(transformedTours);
            setDataSource(prev => ({ ...prev, tours: 'api' }));
          } else {
            setFeaturedTours(mockFeaturedTours);
            setDataSource(prev => ({ ...prev, tours: 'mock' }));
          }
        }
      } catch (error) {
        console.error('❌ Lỗi khi gọi API tours:', error);
        // Chỉ sử dụng mock data khi có lỗi API
        setFeaturedTours(mockFeaturedTours);
        setDataSource(prev => ({ ...prev, tours: 'mock' }));
      } finally {
        setLoading(false);
      }
    };

    // Set tour feature categories (static data)
    setTourFeature(mockTourFeatures);
    
    // Fetch featured tours from API
    fetchData();
  }, []);

  // Xử lý click outside để đóng dropdown và calendar
  useEffect(() => {
    const handleClickOutside = (event) => {
      // Đóng calendar nếu click bên ngoài
      if (showCalendar && !event.target.closest('.calendar-container')) {
        setShowCalendar(false);
      }

      // Đóng dropdown nếu click bên ngoài
      if (showDropdown && !event.target.closest('.budget-container')) {
        setShowDropdown(false);
      }

      // Đóng destinations dropdown nếu click bên ngoài
      if (destinations.length > 0 && !event.target.closest('.destination-container')) {
        setDestination([]);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showCalendar, showDropdown, destinations]);



  const handleSearch = () => {
    // Kiểm tra nếu không có điểm đến
    if (!filterSearch.text.trim()) {
      alert('Vui lòng nhập điểm đến bạn muốn đi!');
      return;
    }

    // Tạo query parameters
    const params = new URLSearchParams();
    if (filterSearch.text) params.append('text', filterSearch.text);
    if (filterSearch.fromDate) params.append('fromDate', filterSearch.fromDate);
    if (filterSearch.budgetId) params.append('budgetId', filterSearch.budgetId);

    // Navigate đến trang tours với query parameters
    navigate(`/tours/du-lich?${params.toString()}`);
  };

  const formatDate = (date) => {
    const dateFormat = date.toLocaleString("vi-VN", {
      timeZone: "Asia/Ho_Chi_Minh",
      weekday: "long",
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
    });
    return dateFormat;
  };

  const handleChangeCalendar = (value) => {
    setShowCalendar(false);
    const date = formatDate(new Date(value));
    const formattedDate = date.split(", ")[1].split("/").reverse().join("-");
    setFilterSearch((prev) => ({
      ...prev,
      fromDate: formattedDate,
    }));
    setCalendarSearch(date);
  };

  const handleSelectBudget = (value) => {
    setFilterSearch((prev) => ({
      ...prev,
      budgetId: value,
    }));
    switch (value) {
      case 1:
        setSelectedBudget("Dưới 5 triệu");
        break;
      case 2:
        setSelectedBudget("Từ 5 - 10 triệu");
        break;
      case 3:
        setSelectedBudget("Từ 10 - 20 triệu");
        break;
      case 4:
        setSelectedBudget("Trên 20 triệu");
        break;
      default:
        setSelectedBudget("Chọn mức giá");
        break;
    }
    setShowDropdown(false);
  };
  useEffect(() => {
    const now = new Date();
    now.setDate(now.getDate() + 1);
    const tomorrow = formatDate(now);
    if (tomorrow) {
      setCalendarSearch(tomorrow);
    }
  }, []);

  const handleClickCalendar = useCallback(() => {
    setShowCalendar((prev) => !prev);
  }, []);

  const handleClickDropdown = useCallback(() => {
    setShowDropdown((prev) => !prev);
  }, []);

  const handleNavigate = (slug) => {
    // Navigate đến trang tours với slug
    navigate(`/tours/${slug}`);
  };

  const handleClickDestination = (destination) => {
    // Set destination vào input
    setFilterSearch((prev) => ({ ...prev, text: destination.title }));
    // Đóng dropdown
    setDestination([]);
  };

  const settings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 3,
    slidesToScroll: 3,
    autoplay: true,
    autoplaySpeed: 3000,
    rtl: reverse,
    afterChange: (current) => {
      const totalSlides = images.length;
      if (current === totalSlides - 1) {
        setReverse(true);
      } else if (current === 0) {
        setReverse(false);
      }
    },
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 3,
          infinite: true,
          dots: true,
        },
      },
      {
        breakpoint: 600,
        settings: {
          slidesToShow: 4,
          slidesToScroll: 4,
          initialSlide: 4,
        },
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        },
      },
    ],
  };

  useEffect(() => {
    if (sliderRef.current) {
      sliderRef.current.slickPause();
      setTimeout(() => {
        sliderRef.current.slickPlay();
      }, 3000);
    }
  }, []);
  
  return (
    <>
    <div className="w-full">
      {/* Home Banner */}
      <div className="w-full relative mb-[150px]">
        {/* Banner Image */}
        <div className="w-full">
          <img src={Banner} alt="" className="w-full cursor-pointer" />
        </div>
        
        {/* Banner Content */}
        <div 
          className="absolute left-[100px] w-[85%] h-[150px] z-[100] bg-white rounded-2xl shadow-[0_5px_15px_rgba(0,0,0,0.35)]"
          style={{ top: 'calc((100vw / 4.173913043478261) - 59px)' }}
        >
          {/* Banner Search Tab */}
          <div className="border-b border-[#e5e5e5] pb-0 flex justify-center items-center h-[30%] w-full">
            <div className="flex justify-center items-center mt-5 mb-5">
              <img src={Oto} alt="" />
              <p className="text-xl font-bold ml-2.5 text-[#0b5da7]">Tour trọn gói</p>
            </div>
          </div>

          {/* Banner Search Body */}
          <div className="w-full h-[70%] flex justify-center items-center">
            <div className="w-[90%] h-[85%] flex justify-between items-center">
              {/* BSB Input */}
              <div className="flex justify-start items-center gap-6 w-[90%]">
                
                {/* Input Container 1 - Destination */}
                <div className="w-[48%] relative destination-container">
                  <div className="flex flex-col relative">
                    <label htmlFor="" className="block m-0 text-[1.3rem] font-[750] text-[#171717]">
                      Bạn muốn đi đâu?<span style={{ color: "red" }}>*</span>
                    </label>
                    <div className="flex justify-start items-center flex-row gap-2.5 w-full">
                      <input
                        type="text"
                        placeholder="Tìm kiếm với bất kỳ địa điểm bạn yêu thích!"
                        onChange={handleSearchInputChange}
                        className="font-medium border-none p-1.5 pr-4 pb-4 pl-0 w-full outline-none"
                      />
                    </div>

                    {/* Destinations Dropdown */}
                    {destinations.length > 0 && (
                      <div className="mt-7 absolute top-full left-0 w-full z-10 bg-white shadow-[0px_4px_12px_rgba(0,0,0,0.1)] rounded-lg py-5 px-4">
                        {destinations.map((record, index) => (
                          <div
                            key={index}
                            className="flex items-center mt-2 cursor-pointer hover:bg-gray-50 p-2 rounded"
                            onClick={() => handleClickDestination(record)}
                          >
                            <img
                              src={record.image || 'https://via.placeholder.com/50x50?text=No+Image'}
                              alt={record.title}
                              className="w-[50px] h-[50px] rounded overflow-hidden object-cover"
                            />
                            <p className="ml-2 text-[15px] text-[#5d5d5d]">{record.title}</p>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                {/* Has Divider */}
                <div className="border-r border-[#e5e5e5] h-12 my-auto"></div>

                {/* Input Container 2 - Date */}
                <div className="w-[48%] relative calendar-container">
                  <div className="flex flex-col relative">
                    <label htmlFor="" className="block m-0 text-[1.3rem] font-[750] text-[#171717]">Ngày đi</label>
                    <div className="flex justify-start items-center flex-row gap-2.5 w-full cursor-pointer" onClick={handleClickCalendar}>
                      <span className="font-medium border-none p-1.5 pr-4 pb-4 pl-0 w-full">{calendarSearch}</span>
                    </div>
                  </div>

                  {/* Calendar */}
                  {showCalendar && (
                    <div style={wrapperStyle} className="mt-10 absolute top-full left-0 z-10 bg-white shadow-[0px_4px_12px_rgba(0,0,0,0.1)] rounded-lg">
                      <Calendar
                        fullscreen={false}
                        onChange={handleChangeCalendar}
                        disabledDate={(current) =>
                          current && current <= moment().endOf("day")
                        }
                      />
                    </div>
                  )}
                </div>

                {/* Has Divider */}
                <div className="border-r border-[#e5e5e5] h-12 my-auto"></div>

                {/* Input Container 3 - Budget */}
                <div className="w-[48%] relative budget-container">
                  <div
                    className="flex flex-col cursor-pointer"
                    onClick={handleClickDropdown}
                  >
                    <label htmlFor="" className="block m-0 text-[1.3rem] font-[750] text-[#171717]">Ngân sách: </label>
                    <p style={{ fontSize: "20px" }}>
                      {selectedBudget ? selectedBudget : "Chọn mức giá"}
                    </p>
                  </div>

                  {/* Dropdown */}
                  <div>
                    {showDropdown && (
                      <div className="flex flex-col absolute mt-10 top-full w-full py-5 px-2.5 bg-white shadow-[0px_4px_12px_rgba(0,0,0,0.1)] rounded-lg z-20">
                        <span
                          className="border border-[#ddd] py-1 px-2.5 w-auto mt-2.5 cursor-pointer rounded text-lg text-[#171717] hover:bg-gray-50 transition-colors duration-200"
                          onClick={() => handleSelectBudget(1)}
                        >
                          Dưới 5 triệu
                        </span>
                        <span
                          className="border border-[#ddd] py-1 px-2.5 w-auto mt-2.5 cursor-pointer rounded text-lg text-[#171717] hover:bg-gray-50 transition-colors duration-200"
                          onClick={() => handleSelectBudget(2)}
                        >
                          Từ 5 - 10 triệu
                        </span>
                        <span
                          className="border border-[#ddd] py-1 px-2.5 w-auto mt-2.5 cursor-pointer rounded text-lg text-[#171717] hover:bg-gray-50 transition-colors duration-200"
                          onClick={() => handleSelectBudget(3)}
                        >
                          Từ 10 - 20 triệu
                        </span>
                        <span
                          className="border border-[#ddd] py-1 px-2.5 w-auto mt-2.5 cursor-pointer rounded text-lg text-[#171717] hover:bg-gray-50 transition-colors duration-200"
                          onClick={() => handleSelectBudget(4)}
                        >
                          Trên 20 triệu
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* BSB Icon - Search Button */}
              <div className="w-[10%]">
                <button 
                  onClick={handleSearch}
                  className="w-full p-5 flex justify-center items-center rounded-lg border-none bg-[#0b5da7] text-white font-bold text-2xl transition-all duration-300 text-[1.6rem] cursor-pointer hover:bg-[#103658]"
                >
                  <i className="fa-solid fa-magnifying-glass"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Select Tour */}
      <div className="w-full flex justify-center items-center">
        <div className="w-[85%] flex justify-center items-center flex-col">
          
          {/* Select Tour Button */}
          <div className="flex flex-row justify-center items-center">
            <div className="flex flex-col justify-start items-center self-stretch p-3 px-6 text-center rounded text-[#171717] font-semibold gap-4 text-sm uppercase transition-all duration-300 ease-in-out hover:text-[#0b5da7]">
              <button onClick={() => handleNavigate("tet")} className="flex flex-row justify-center items-center gap-1 p-1 rounded-2xl bg-[#daefff] w-20 aspect-square border border-[#daefff] cursor-pointer">
                <img src={TourTet} alt="" className="w-14" />
              </button>
              <p>
                TOUR <br />
                TẾT
              </p>
            </div>
            <div className="flex flex-col justify-start items-center self-stretch p-3 px-6 text-center rounded text-[#171717] font-semibold gap-4 text-sm uppercase transition-all duration-300 ease-in-out hover:text-[#0b5da7]">
              <button onClick={() => handleNavigate("du-lich-cao-cap")} className="flex flex-row justify-center items-center gap-1 p-1 rounded-2xl bg-[#daefff] w-20 aspect-square border border-[#daefff] cursor-pointer">
                <img src={Dimond} alt="" className="w-14" />
              </button>
              <p>
                TOUR <br />
                CAO CẤP
              </p>
            </div>
            <div className="flex flex-col justify-start items-center self-stretch p-3 px-6 text-center rounded text-[#171717] font-semibold gap-4 text-sm uppercase transition-all duration-300 ease-in-out hover:text-[#0b5da7]">
              <button onClick={() => handleNavigate("du-lich-tieu-chuan")} className="flex flex-row justify-center items-center gap-1 p-1 rounded-2xl bg-[#daefff] w-20 aspect-square border border-[#daefff] cursor-pointer">
                <img src={TieuChuan} alt="" className="w-14" />
              </button>
              <p>
                TOUR <br />
                TIÊU CHUẨN
              </p>
            </div>
            <div className="flex flex-col justify-start items-center self-stretch p-3 px-6 text-center rounded text-[#171717] font-semibold gap-4 text-sm uppercase transition-all duration-300 ease-in-out hover:text-[#0b5da7]">
              <button onClick={() => handleNavigate("du-lich-tiet-kiem")} className="flex flex-row justify-center items-center gap-1 p-1 rounded-2xl bg-[#daefff] w-20 aspect-square border border-[#daefff] cursor-pointer">
                <img src={TietKiem} alt="" className="w-14" />
              </button>
              <p>
                TOUR <br />
                TIẾT KIỆM
              </p>
            </div>
            <div className="flex flex-col justify-start items-center self-stretch p-3 px-6 text-center rounded text-[#171717] font-semibold gap-4 text-sm uppercase transition-all duration-300 ease-in-out hover:text-[#0b5da7]">
              <button onClick={() => handleNavigate("du-lich-gia-tot")} className="flex flex-row justify-center items-center gap-1 p-1 rounded-2xl bg-[#daefff] w-20 aspect-square border border-[#daefff] cursor-pointer">
                <img src={GiaTot} alt="" className="w-14" />
              </button>
              <p>
                TOUR <br />
                GIÁ TỐT
              </p>
            </div>
          </div>

          {/* Slider */}
          <Slider {...settings} ref={sliderRef} className="w-[95%] h-[300px] mt-10 mb-[70px] flex justify-between items-center">
            {images.map((image, key) => (
              <div className="w-full object-cover" key={key}>
                <img src={image} alt="" className="w-[395px] rounded-[20px] cursor-pointer" />
              </div>
            ))}
          </Slider>
        </div>
      </div>

      {/* Tour Feature */}
      <div className="flex flex-col justify-center items-center w-full">
        <div className="py-20 pb-32 w-[85%]">
          
          {/* Tour Feature Header */}
          <div className="flex flex-col justify-between items-start gap-0">
            <div className="flex items-center gap-4">
              <h1 className="text-[2.2rem] font-[650] text-[#0b5da7] m-0 mb-2 uppercase leading-[5rem]">
                Khám phá sản phẩm vietravel
              </h1>
              {/* Data Source Indicator */}
              <div className="flex items-center gap-2 px-3 py-1 rounded-full text-xs font-medium">
                {dataSource.tours === 'api' && (
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full">
                    ✅ Dữ liệu thực từ API
                  </span>
                )}
                {dataSource.tours === 'mock' && (
                  <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">
                    ⚠️ Dữ liệu demo
                  </span>
                )}
                {dataSource.tours === 'loading' && (
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                    🔄 Đang tải...
                  </span>
                )}
              </div>
            </div>
            <div className="w-[12%] h-[3.5px] rounded-[2.5px] bg-[#0b5da7]"></div>
            <h2 className="w-[60%] text-[1.2rem] font-semibold text-[#171717] leading-[3rem] m-0 mb-8 font-inherit">
              Hãy tận hưởng trải nghiệm du lịch chuyên nghiệp, mang lại cho bạn
              những khoảnh khắc tuyệt vời và nâng tầm cuộc sống. Chúng tôi cam
              kết mang đến những chuyến đi đáng nhớ, giúp bạn khám phá thế giới
              theo cách hoàn hảo nhất.
            </h2>
          </div>

          {/* Tour Feature Content */}
          <div className="flex flex-row justify-center items-center w-full">
            <div className="flex items-center justify-between p-0 list-none w-full">
              
              {/* Tour Feature List */}
              <ul className="flex items-center justify-between p-0 list-none w-full">
                
                {loading ? (
                  // Loading state
                  [1, 2, 3].map((index) => (
                    <li key={index} className="mb-3 w-[30%]">
                      <div className="w-full h-[28rem] relative border border-transparent">
                        <div className="w-full h-full flex flex-col gap-0 overflow-hidden rounded-[1.6rem] relative">
                          <div className="w-full h-[80%] rounded-t-[2rem] bg-gray-200 animate-pulse"></div>
                          <div className="w-full h-[20%] bg-[#0b5da7] rounded-b-[2rem] z-[1]"></div>
                        </div>
                        <div className="absolute bottom-0 left-0 w-full rounded-[2rem] p-[1.1rem] z-[2] flex flex-col items-start justify-end">
                          <div className="bg-white rounded-2xl p-[1.1rem] flex flex-col justify-end gap-4 w-[80%] h-[50%] relative opacity-[0.93]">
                            <div className="flex flex-col gap-2">
                              <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                              <div className="h-3 bg-gray-200 rounded animate-pulse w-3/4"></div>
                              <div className="h-3 bg-gray-200 rounded animate-pulse w-1/2"></div>
                            </div>
                            <div className="flex flex-row justify-between items-end">
                              <div className="h-6 bg-gray-200 rounded animate-pulse w-1/2"></div>
                              <div className="h-4 bg-gray-200 rounded animate-pulse w-1/4"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </li>
                  ))
                ) : (
                  // Actual tours
                  featuredTours.map((tour) => (
                    <li key={tour.id} className="mb-3 w-[30%]">
                      <div className="w-full h-[28rem] relative border border-transparent">
                        
                        {/* Background */}
                        <div className="w-full h-full flex flex-col gap-0 overflow-hidden rounded-[1.6rem] relative">
                          <img
                            src={tour.images?.[0]?.source || tour.image || "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop"}
                            alt={tour.title}
                            onClick={() => navigate(`/tour-details/${tour.slug}`)}
                            className="w-full h-[80%] rounded-t-[2rem] transition-transform duration-300 ease-in-out object-cover cursor-pointer hover:scale-110"
                          />
                          <div className="w-full h-[20%] bg-[#0b5da7] rounded-b-[2rem] z-[1]"></div>
                        </div>

                        {/* Content */}
                        <div className="absolute bottom-0 left-0 w-full rounded-[2rem] p-[1.1rem] z-[2] flex flex-col items-start justify-end">
                          
                          {/* Preview */}
                          <div className="bg-white rounded-2xl p-[1.1rem] flex flex-col justify-end gap-4 transition-[height] duration-[400ms] ease-in-out w-[80%] h-[50%] relative opacity-[0.93]">
                            
                            {/* Preview Content */}
                            <div className="flex flex-col gap-2">
                              <p className="m-0 text-base font-bold overflow-hidden text-ellipsis [-webkit-line-clamp:2] [-webkit-box-orient:vertical] [display:-webkit-box]">
                                {tour.title}
                              </p>
                              <div className="flex flex-row items-center justify-start gap-1 text-[1.2rem] font-bold m-0">
                                <div className="flex items-center justify-center flex-row gap-1 font-bold">
                                  <img src={Vitri} alt="" className="scale-90" />
                                  <p className="text-[0.8rem] font-semibold m-0">Khởi hành: </p>
                                </div>
                                <span className="flex items-center justify-center text-[0.8rem] flex-row font-semibold text-[#0b5da7]">
                                  {tour.departure?.title || tour.departure?.name || "Chưa xác định"}
                                </span>
                              </div>
                              <div className="flex flex-row items-center justify-start gap-1 text-[1.2rem] font-bold m-0">
                                <div className="flex items-center justify-center flex-row gap-1 font-bold">
                                  <img src={Code} alt="" className="scale-90" />
                                  <p className="text-[0.8rem] font-semibold m-0">Mã chương trình: </p>
                                </div>
                                <span className="flex items-center justify-center text-[0.8rem] flex-row font-semibold text-[#0b5da7]">
                                  {tour.code || "N/A"}
                                </span>
                              </div>
                            </div>

                            {/* Preview Price */}
                            <div className="flex flex-row justify-between items-end bg-white">
                              <div className="flex flex-col w-[60%]">
                                <div className="flex flex-row items-center justify-start gap-2">
                                  <p className="m-0 font-bold text-base">Giá từ</p>
                                </div>
                                <div className="flex flex-row items-center justify-start">
                                  <span className="m-0 text-[#e01600] font-bold text-[1.1rem]">
                                    {tour.price ? `${tour.price.toLocaleString('vi-VN')}đ` : 'Liên hệ'}
                                  </span>
                                </div>
                              </div>
                              <div className="w-[35%] flex items-end">
                                <button
                                  onClick={() => navigate(`/tour-details/${tour.slug}`)}
                                  className="bg-transparent border-none cursor-pointer flex flex-row justify-evenly items-center text-[0.9rem] font-bold"
                                >
                                  Xem chi tiết
                                  <i className="fa-solid fa-arrow-right"></i>
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </li>
                  ))
                )}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
    </>
  )
}

export default Home
