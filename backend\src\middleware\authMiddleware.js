// Middleware kiểm tra đăng nhập cơ bản
exports.ensureAuthenticated = (req, res, next) => {
    if (req.session && req.session.user && req.session.user.status === 'Hoạt động') {
        // Set req.user từ session để sử dụng trong controller
        req.user = req.session.user;
        return next();
    }
    
    req.flash('error', '<PERSON>ui lòng đăng nhập để tiếp tục!');
    res.redirect('/login');
};

// Middleware kiểm tra vai trò đơn giản
exports.checkRole = (...allowedRoles) => {
    return (req, res, next) => {
        if (req.session && req.session.user && allowedRoles.includes(req.session.user.role)) {
            // Set req.user từ session để sử dụng trong controller
            req.user = req.session.user;
            return next();
        }
        
        req.flash('error', '<PERSON><PERSON><PERSON> không có quyền truy cập chức năng này!');
        res.redirect('/dashboard');
    };
};

// Middleware kiểm tra quyền cụ thể - hiện tại chỉ kiểm tra đăng nhập
// TODO: Implement permission system trong tương lai
exports.checkPermission = (permission) => {
    return (req, res, next) => {
        // Kiểm tra đăng nhập trước
        if (!req.session || !req.session.user || req.session.user.status !== 'Hoạt động') {
            req.flash('error', 'Vui lòng đăng nhập để tiếp tục!');
            return res.redirect('/login');
        }

        // Set req.user từ session
        req.user = req.session.user;
        // Trong tương lai có thể kiểm tra quyền cụ thể từ database
        if (permission === '') {
            // Route không yêu cầu quyền cụ thể, chỉ cần đăng nhập
            return next();
        }
        // Hiện tại cho phép tất cả user đã đăng nhập
        return next();
    };
};

// Middleware chặn truy cập login khi đã đăng nhập
exports.redirectIfAuthenticated = (req, res, next) => {
    if (req.session && req.session.user) {
        return res.redirect('/dashboard');
    }
    next();
};
