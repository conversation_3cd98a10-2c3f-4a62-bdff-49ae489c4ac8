<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chỉnh sửa điểm đến</title>
    <!-- Embed Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
        href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap"
        rel="stylesheet"
    />
    <!-- Bootstrap CSS -->
    <link
        href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"
        rel="stylesheet"
    />
    <!-- Font Awesome -->
    <link
        rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />        <!-- Select2 CSS -->
        <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
        <!-- CSS -->
        <link rel="stylesheet" href="/css/main.css" />
    </head>
<body>
    <div class="dashboard">
        <div class="container_fuild">
            <div class="dashboard__inner">
                <%- include('../partials/sidebar') %>
                
                <!-- Toast Container -->
                <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1055;">
                    <% if (message && message.length > 0) { %>
                    <div class="toast show" role="alert" aria-live="assertive" aria-atomic="true" data-bs-autohide="true" data-bs-delay="5000">
                        <div class="toast-header bg-success text-white">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong class="me-auto">Thành công</strong>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                        </div>
                        <div class="toast-body">
                            <%= message %>
                        </div>
                    </div>
                    <% } %>
                    
                    <% if (error && error.length > 0) { %>
                    <div class="toast show" role="alert" aria-live="assertive" aria-atomic="true" data-bs-autohide="true" data-bs-delay="5000">
                        <div class="toast-header bg-danger text-white">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <strong class="me-auto">Lỗi</strong>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                        </div>
                        <div class="toast-body">
                            <%= error %>
                        </div>
                    </div>
                    <% } %>
                </div>
                
                    <div class="destination">
                        <div class="destination-form">
                            <div class="destination-form__header">
                                <h3>Chỉnh sửa điểm đến</h3>
                            </div>
                        <div class="destination-form__body">
                            <% if (userPermissions && userPermissions.includes('UPDATE_DESTINATION')) { %>
                                <form method="POST" action="/destination/edit/<%= destination._id %>" enctype="multipart/form-data">
                                    <%- include('../partials/csrf-token') %>
                                    <div class="mb-4">
                                        <label for="name" class="form-label">
                                            Tên điểm đến
                                        </label>
                                        <input type="text" class="form-control" id="name" name="name" value="<%= destination.name %>" placeholder="Nhập tên điểm đến..." required>
                                    </div>
                                    <div class="mb-4">
                                        <label for="info" class="form-label">
                                            Thông tin
                                        </label>
                                        <textarea class="form-control" id="info" name="info" rows="4" placeholder="Nhập thông tin về điểm đến..."><%= destination.info || '' %></textarea>
                                    </div>
                                    <div class="mb-4">
                                        <label for="image" class="form-label">
                                            Hình ảnh
                                        </label>
                                        <input type="file" class="form-control" id="image" name="image" accept="image/*">
                                        <% if (destination.image) { %>
                                            <div class="mt-2">
                                                <img src="<%= destination.image %>" alt="<%= destination.name %>" style="width: 100px; height: 60px; object-fit: cover; border-radius: 4px;">
                                                <small class="text-muted d-block mt-2">Ảnh hiện tại</small>
                                            </div>
                                        <% } %>
                                    </div>
                                    <div class="mb-4">
                                        <label for="parent" class="form-label">
                                            Điểm đến cha (tùy chọn)
                                        </label>
                                        <select class="form-select" id="parent" name="parent">
                                            <% if (parentDestinations && parentDestinations.length > 0) { %>
                                                <option value="">-- Chọn điểm đến cha --</option>
                                                <% parentDestinations.forEach(parent => { %>
                                                    <option value="<%= parent._id %>" <%= destination.parent && destination.parent._id && destination.parent._id.toString() === parent._id.toString() ? 'selected' : '' %>>
                                                        <%= parent.name %>
                                                    </option>
                                                <% }) %>
                                            <% } else { %>
                                                <option value="">-- Chọn một tuỳ chọn... --</option>
                                            <% } %>
                                        </select>
                                        <% if (!parentDestinations || parentDestinations.length === 0) { %>
                                            <small class="text-muted">Không có điểm đến nào khác để làm cha.</small>
                                        <% } %>
                                    </div>
                                    <div class="d-flex justify-content-between gap-3">
                                        <a href="/destination" class="btn btn-secondary">
                                            <i class="fas fa-arrow-left me-2"></i>Quay lại
                                        </a>
                                        <button type="submit" class="btn btn-primary">
                                            Cập nhật
                                        </button>
                                    </div>
                                </form>
                            <% } else { %>
                                <div class="alert alert-danger mt-3">Bạn không có quyền chỉnh sửa điểm đến.</div>
                            <% } %>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/toast-auto-hide.js?v=<%= Date.now() %>"></script>        
    <script src="/js/selectEdit.js?v=<%= Date.now() %>"></script>
    
    <script>
        $(document).ready(function() {
            // Handle form submission with AJAX for better UX
            $('form').on('submit', function(e) {
                e.preventDefault();
                
                const form = this;
                const formData = new FormData(form);
                const submitBtn = $(form).find('button[type="submit"]');
                const originalText = submitBtn.html();
                
                // Show loading state
                submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Đang cập nhật...');
                
                // Submit form via AJAX
                $.ajax({
                    url: form.action,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    headers: {
                        'X-CSRF-Token': window.csrfToken || $('input[name="_csrf"]').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            // Show success toast
                            showToastNotification(response.message || 'Cập nhật điểm đến thành công!', 'success');
                            
                            // Reset dirty form state
                            if (typeof window.resetFormDirtyState === 'function') {
                                window.resetFormDirtyState();
                            }
                            
                            // Redirect after short delay
                            setTimeout(() => {
                                window.location.href = '/destination';
                            }, 1500);
                        } else {
                            showToastNotification(response.message || 'Có lỗi xảy ra', 'error');
                        }
                    },
                    error: function(xhr) {
                        let errorMessage = 'Có lỗi xảy ra khi cập nhật điểm đến';
                        
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        } else if (xhr.responseText) {
                            try {
                                const response = JSON.parse(xhr.responseText);
                                errorMessage = response.message || errorMessage;
                            } catch (e) {
                                console.error('Error parsing error response:', e);
                            }
                        }
                        
                        showToastNotification(errorMessage, 'error');
                    },
                    complete: function() {
                        // Restore button state
                        submitBtn.prop('disabled', false).html(originalText);
                    }
                });
            });
            
            // Initialize CSRF token for AJAX requests
            $.ajaxSetup({
                beforeSend: function(xhr, settings) {
                    if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                        const token = window.csrfToken || $('meta[name=csrf-token]').attr('content') || $('input[name="_csrf"]').val();
                        if (token) {
                            xhr.setRequestHeader("X-CSRF-Token", token);
                        }
                    }
                }
            });
        });
        
        // Add CSRF token to window for access
        var csrfToken = '<%= typeof csrfToken !== "undefined" ? csrfToken : "" %>';
        if (csrfToken) {
            window.csrfToken = csrfToken;
        }
    </script>
</body>
</html>
