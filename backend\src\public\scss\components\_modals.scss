body.modal-open {
  padding-right: 0 !important;
  overflow: hidden !important;
}

#deleteModal,#deleteMultipleModal,#permissionUpdateModal {
  .modal-content {
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    background-color: #fff;
    border: none;
    overflow: hidden;

    .modal-header {
      padding: 1.5rem;
      border-bottom: none;

      .modal-title {
        font-size: 1.4rem;
        font-weight: 600;
        color: #212529;
      }

      .btn-close {
        background: none;
        border: none;
        font-size: 1rem;
        opacity: 0.6;

        &:hover {
          opacity: 1;
        }
      }
    }

    .modal-body {
      padding: 1rem 1.5rem;

      p {
        margin-bottom: 1rem;
        font-size: 1.2rem;
        color: #212529;
      }

      .alert-warning {
        background-color: #fff3cd;
        border: 1px solid #ffeeba;
        color: #856404;
        font-size: 1.2rem;
        padding: 0.75rem 1rem;
        border-radius: 5px;

        i {
          font-size: 1.1rem;
          color: #ffc107;
        }

        small {
          margin-left: 0.5rem;
        }
      }
    }

    .modal-footer {
      padding: 1rem 1.5rem;
      border-top: none;
      display: flex;
      justify-content: flex-end;
      gap: 0.5rem;

      .btn {
        font-size: 1.1rem;
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-radius: 6px;

        i {
          margin-right: 0.4rem;
        }
      }

      .btn-secondary {
        background-color: #6c757d;
        color: #fff;

        &:hover {
          background-color: #5a6268;
        }
      }

      .btn-danger {
        background-color: #dc3545;
        color: #fff;

        &:hover {
          background-color: #c82333;
        }
      }
    }
  }
}