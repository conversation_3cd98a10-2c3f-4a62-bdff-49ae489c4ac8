import React from 'react'
import { Carousel } from 'antd';

function ImageSlider({ image, onCancel }) {
    const contentStyle = {
        margin: 0,
        color: '#fff',
        lineHeight: '160px',
        textAlign: 'center',
        background: '#364d79',
    };

    return (
        <div className='w-[650px] h-[600px] bg-white rounded-[20px]'>
            <div className="h-[10%] w-full flex flex-row justify-end items-center">
                <i className="fa-solid fa-xmark mr-5 cursor-pointer" onClick={onCancel}></i>
            </div>
            <div className="w-full h-[90%] flex justify-center items-center">
                <div className="w-[90%]">
                    <Carousel arrows infinite={false}>
                        {image.map((item, index) => (
                            <img 
                                key={index}
                                className='w-full h-full' 
                                style={contentStyle} 
                                src={item?.source} 
                                alt="" 
                            />
                        ))}
                    </Carousel>
                </div>
            </div>
        </div>
    )
}

export default ImageSlider