import { baseApi } from './baseApi'

export const userApi = {
  //Người dùng đăng nhập
  login: async (credentials) => {
    try {
      const response = await baseApi.post('/api/login', {
        email: credentials.username,
        password: credentials.password,
        recaptchaToken: credentials.recaptcha
      })
      return response.data
    } catch (error) {
      throw error
    }
  },

  // Đăng ký người dùng mới
  register: async (userData) => {
  try {
    // Tạo request config riêng không có Authorization header
    const response = await baseApi.post('/api/register', {
      fullName: userData.fullName,
      email: userData.email,
      username: userData.username,
      phone: userData.phone,
      password: userData.password,
      confirmPassword: userData.confirmPassword,
      recaptchaToken: userData.recaptcha
    }, {
      // Override headers để loại bỏ Authorization
      headers: {
        'Content-Type': 'application/json'
        // Không include Authorization header
      },
      transformRequest: [(data, headers) => {
        // Xóa Authorization header nếu có
        delete headers.Authorization
        return JSON.stringify(data)
      }]
    })
    return response.data
  } catch (error) {
    throw error
  }
},

  // Đăng xuất người dùng
  logout: async () => {
    try {
      const response = await baseApi.post('/auth/logout')
      return response.data
    } catch (error) {
      throw error
    }
  },

  // Lấy thông tin người dùng
  getProfile: async () => {
    try {
      const response = await baseApi.get('/auth/profile')
      return response.data
    } catch (error) {
      throw error
    }
  },

  // Cập nhật thông tin người dùng
  updateProfile: async (userData) => {
    try {
      const response = await baseApi.put('/auth/profile', userData)
      return response.data
    } catch (error) {
      throw error
    }
  },

  // Thay đổi mật khẩu
  changePassword: async (passwordData) => {
    try {
      const response = await baseApi.put('/auth/change-password', passwordData)
      return response.data
    } catch (error) {
      throw error
    }
  },

  // Quên mật khẩu
  forgotPassword: async (email, recaptcha) => {
    try {
      const response = await baseApi.post('/auth/forgot-password', { 
        email, 
        recaptcha 
      })
      return response.data
    } catch (error) {
      throw error
    }
  },

  // Đặt lại mật khẩu
  resetPassword: async (resetData) => {
    try {
      const response = await baseApi.post('/auth/reset-password', resetData)
      return response.data
    } catch (error) {
      throw error
    }
  },

  // Xác minh email
  verifyEmail: async (token) => {
    try {
      const response = await baseApi.post('/auth/verify-email', { token })
      return response.data
    } catch (error) {
      throw error
    }
  },

  //Resend email xác minh
  resendVerification: async (email) => {
    try {
      const response = await baseApi.post('/auth/resend-verification', { email })
      return response.data
    } catch (error) {
      throw error
    }
  },

  // Kiểm tra tính khả dụng của email
  checkEmailAvailability: async (email) => {
    try {
      const response = await baseApi.post('/auth/check-email', { email })
      return response.data
    } catch (error) {
      throw error
    }
  },

  // Kiểm tra tính khả dụng của số điện thoại
  checkPhoneAvailability: async (phone) => {
    try {
      const response = await baseApi.post('/auth/check-phone', { phone })
      return response.data
    } catch (error) {
      throw error
    }
  },

  // Nhận tất cả người dùng (chỉ quản trị viên)
  getAllUsers: async (params) => {
    try {
      const response = await baseApi.get('/users', { params })
      return response.data
    } catch (error) {
      throw error
    }
  },

  // Lấy người dùng theo ID
  getUserById: async (id) => {
    try {
      const response = await baseApi.get(`/users/${id}`)
      return response.data
    } catch (error) {
      throw error
    }
  },

  // Tạo người dùng (chỉ quản trị viên)
  createUser: async (userData) => {
    try {
      const response = await baseApi.post('/users', userData)
      return response.data
    } catch (error) {
      throw error
    }
  },

  // Cập nhật người dùng (chỉ quản trị viên)
  updateUser: async (id, userData) => {
    try {
      const response = await baseApi.put(`/users/${id}`, userData)
      return response.data
    } catch (error) {
      throw error
    }
  },

  // Xóa người dùng (chỉ quản trị viên)
  deleteUser: async (id) => {
    try {
      const response = await baseApi.delete(`/users/${id}`)
      return response.data
    } catch (error) {
      throw error
    }
  },

  // Xóa tài khoản hiện tại
  deleteCurrentUser: async () => {
    try {
      const response = await baseApi.delete('/auth/profile')
      return response.data
    } catch (error) {
      throw error
    }
  }
}

export default userApi