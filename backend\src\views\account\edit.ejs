<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chỉnh sửa t<PERSON><PERSON>n</title>
    <!-- Embed Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
        href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap"
        rel="stylesheet"
    />
    <!-- Bootstrap CSS -->
    <link
        href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"
        rel="stylesheet"
    />
    <!-- Font Awesome -->
    <link
        rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <!-- CSS -->
    <link rel="stylesheet" href="/css/main.css" />
</head>
<body>
    <div class="dashboard">
        <div class="container_fuild">
            <div class="dashboard__inner">
                <%- include('../partials/sidebar') %>
                
                <!-- Toast Container -->
                <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1055;">
                    <% if (message && message.length > 0) { %>
                    <div class="toast show" role="alert" aria-live="assertive" aria-atomic="true" data-bs-autohide="true" data-bs-delay="5000">
                        <div class="toast-header bg-success text-white">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong class="me-auto">Thành công</strong>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                        </div>
                        <div class="toast-body">
                            <%= message %>
                        </div>
                    </div>
                    <% } %>
                    
                    <% if (error && error.length > 0) { %>
                    <div class="toast show" role="alert" aria-live="assertive" aria-atomic="true" data-bs-autohide="true" data-bs-delay="5000">
                        <div class="toast-header bg-danger text-white">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <strong class="me-auto">Lỗi</strong>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                        </div>
                        <div class="toast-body">
                            <%= error %>
                        </div>
                    </div>
                    <% } %>
                </div>
                
                <div class="destination">
                    <div class="destination-form">
                        <div class="destination-form__header">
                            <h3>Chỉnh sửa tài khoản</h3>
                        </div>
                        <div class="destination-form__body">
                            <form method="POST" action="/account/edit/<%= user._id %>" enctype="multipart/form-data">
                                <%- include('../partials/csrf-token') %>
                                <div class="mb-4">
                                    <label for="fullName" class="form-label">
                                        Họ tên
                                    </label>
                                    <input type="text" class="form-control" id="fullName" name="fullName" value="<%= user.fullName %>" placeholder="Nhập họ tên..." required>
                                </div>
                                <div class="mb-4">
                                    <label for="email" class="form-label">
                                        Email
                                    </label>
                                    <input type="email" class="form-control" id="email" name="email" value="<%= user.email %>" placeholder="Nhập email..." required>
                                </div>
                                <div class="mb-4">
                                    <label for="username" class="form-label">
                                        Tên đăng nhập <small class="text-muted">(không bắt buộc)</small>
                                    </label>
                                    <input 
                                        type="text" 
                                        class="form-control" 
                                        id="username" 
                                        name="username" 
                                        value="<%= user.username || '' %>" 
                                        placeholder="Nhập tên đăng nhập (nếu cần)..."
                                    />
                                </div>
                                <div class="mb-4">
                                    <label for="role" class="form-label">
                                        Vai trò
                                    </label>
                                    <select class="form-select select2" id="role" name="role" required>
                                        <option value="">-- Chọn vai trò --</option>
                                        <% if (roles && roles.length > 0) { %>
                                            <% roles.forEach(role => { %>
                                                <option value="<%= role.name %>" <%= user.role && user.role.name === role.name ? 'selected' : '' %>><%= role.name %></option>
                                            <% }); %>
                                        <% } else { %>
                                            <option value="" disabled>Chưa có vai trò nào</option>
                                        <% } %>
                                    </select>
                                </div>
                                <div class="mb-4">
                                    <label for="avatar" class="form-label">
                                        Ảnh đại diện
                                    </label>
                                    <input type="file" class="form-control" id="avatar" name="avatar" accept="image/*" onchange="previewImage(this)">
                                    <% if (user.avatar) { %>
                                        <div class="mt-3">
                                            <small class="text-muted d-block mb-2">Ảnh hiện tại:</small>
                                            <div class="position-relative d-inline-block">
                                                <img 
                                                    src="<%= user.avatar %>" 
                                                    alt="<%= user.fullName %>" 
                                                    style="width: 80px; height: 80px; object-fit: cover; border-radius: 50%; border: 2px solid #ddd;"
                                                    onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
                                                >
                                                <div style="width: 80px; height: 80px; background: #e9ecef; border-radius: 50%; display: none; align-items: center; justify-content: center; border: 2px solid #ddd; position: absolute; top: 0; left: 0;">
                                                    <i class="fas fa-user text-muted" style="font-size: 2rem;"></i>
                                                </div>
                                            </div>
                                        </div>
                                    <% } else { %>
                                        <div class="mt-3">
                                            <small class="text-muted d-block mb-2">Chưa có ảnh đại diện</small>
                                            <div style="width: 80px; height: 80px; background: #e9ecef; border-radius: 50%; display: flex; align-items: center; justify-content: center; border: 2px solid #ddd;">
                                                <i class="fas fa-user text-muted" style="font-size: 2rem;"></i>
                                            </div>
                                        </div>
                                    <% } %>
                                </div>
                                <div class="d-flex justify-content-between gap-3">
                                    <a href="/account" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Quay lại
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Cập nhật
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/toast-auto-hide.js?v=<%= Date.now() %>"></script>        
    <script src="/js/selectEdit.js?v=<%= Date.now() %>"></script>
    <script>
        $(document).ready(function() {
            // Handle form submission with AJAX for better UX
            $('form').on('submit', function(e) {
                e.preventDefault();
                
                const form = this;
                const formData = new FormData(form);
                const submitBtn = $(form).find('button[type="submit"]');
                const originalText = submitBtn.html();
                
                // Show loading state
                submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Đang cập nhật...');
                
                // Submit form via AJAX
                $.ajax({
                    url: form.action,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    headers: {
                        'X-CSRF-Token': window.csrfToken || $('input[name="_csrf"]').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            // Show success toast
                            showToastNotification(response.message || 'Cập nhật tài khoản thành công!', 'success');
                            
                            // Reset dirty form state
                            if (typeof window.resetFormDirtyState === 'function') {
                                window.resetFormDirtyState();
                            }
                            
                            // Redirect after short delay
                            setTimeout(() => {
                                window.location.href = '/account';
                            }, 1500);
                        } else {
                            showToastNotification(response.message || 'Có lỗi xảy ra', 'error');
                        }
                    },
                    error: function(xhr) {
                        let errorMessage = 'Có lỗi xảy ra khi cập nhật tài khoản';
                        
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        } else if (xhr.responseText) {
                            try {
                                const response = JSON.parse(xhr.responseText);
                                errorMessage = response.message || errorMessage;

                                // Handle validation errors
                                if (response.validationErrors) {
                                    // Display validation errors on form fields
                                    Object.keys(response.validationErrors).forEach(field => {
                                        const input = $(`#${field}`);
                                        if (input.length) {
                                            input.addClass('is-invalid');
                                            
                                            // Find or create the feedback element
                                            let feedback = input.next('.invalid-feedback');
                                            if (!feedback.length) {
                                                input.after(`<div class="invalid-feedback">${response.validationErrors[field]}</div>`);
                                            } else {
                                                feedback.text(response.validationErrors[field]);
                                                feedback.show();
                                            }
                                        }
                                    });
                                }
                            } catch (e) {
                                console.error('Error parsing error response:', e);
                            }
                        }
                        
                        showToastNotification(errorMessage, 'error');
                    },
                    complete: function() {
                        // Restore button state
                        submitBtn.prop('disabled', false).html(originalText);
                    }
                });
            });
            
            // Initialize CSRF token for AJAX requests
            $.ajaxSetup({
                beforeSend: function(xhr, settings) {
                    if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                        const token = window.csrfToken || $('meta[name=csrf-token]').attr('content') || $('input[name="_csrf"]').val();
                        if (token) {
                            xhr.setRequestHeader("X-CSRF-Token", token);
                        }
                    }
                }
            });

            // Hide validation errors when user starts typing
            $('input, select').on('input change', function() {
                $(this).removeClass('is-invalid');
                const feedback = $(this).next('.invalid-feedback');
                if (feedback.length) {
                    feedback.hide();
                }
            });
        });
        
        // Add CSRF token to window for access
        var csrfToken = '<%= typeof csrfToken !== "undefined" ? csrfToken : "" %>';
        if (csrfToken) {
            window.csrfToken = csrfToken;
        }
        
        function previewImage(input) {
            const currentImageContainer = input.parentNode.querySelector('.mt-3');
            
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    // Tạo preview mới hoặc cập nhật ảnh hiện tại
                    let previewContainer = currentImageContainer;
                    
                    if (!previewContainer) {
                        previewContainer = document.createElement('div');
                        previewContainer.className = 'mt-3';
                        input.parentNode.appendChild(previewContainer);
                    }
                    
                    previewContainer.innerHTML = `
                        <small class="text-muted d-block mb-2">Ảnh mới được chọn:</small>
                        <div class="position-relative d-inline-block">
                            <img src="${e.target.result}" style="width: 80px; height: 80px; object-fit: cover; border-radius: 50%; border: 2px solid #007bff;">
                        </div>
                    `;
                }
                
                reader.readAsDataURL(input.files[0]);
            }
        }
    </script>
</body>
</html>
