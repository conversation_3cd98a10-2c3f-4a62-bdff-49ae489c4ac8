const Order = require('../models/orderModel');
const Tour = require('../models/tourModel');
const { isPhoneVerified } = require('../controllers/otpController');
const { validatePhoneNumber } = require('../utils/otpUtil');

// Xem danh sách đơn hàng
exports.getOrdersPage = async (req, res) => {
    try {
        res.render('order', {
            title: 'Quản lý đơn hàng',
            csrfToken: res.locals.csrfToken,
            userPermissions: res.locals.userPermissions,
            messages: {
                success: req.flash('success'),
                error: req.flash('error')
            }
        });
    } catch (error) {
        console.error('Error in getOrdersPage:', error);
        req.flash('error', 'Có lỗi xảy ra khi tải trang đơn hàng');
        res.redirect('/dashboard');
    }
};

// Xem chi tiết đơn hàng
exports.getOrderDetailPage = async (req, res) => {
    try {
        const orderId = req.params.id;
        
        // Tìm đơn hàng theo ID
        const order = await Order.findById(orderId);
        
        if (!order) {
            req.flash('error', 'Không tìm thấy đơn hàng');
            return res.redirect('/orders');
        }
        
        // Lấy thông tin tour nếu có
        let tourInfo = null;
        if (order.items && order.items.length > 0 && order.items[0].tourId) {
            tourInfo = await Tour.findById(order.items[0].tourId);
        }
        
        res.render('order/orderDetail', {
            title: `Chi tiết đơn hàng #${order.orderId}`,
            order,
            tourInfo,
            csrfToken: res.locals.csrfToken,
            userPermissions: res.locals.userPermissions,
            messages: {
                success: req.flash('success'),
                error: req.flash('error')
            }
        });
    } catch (error) {
        console.error('Error in getOrderDetailPage:', error);
        req.flash('error', 'Có lỗi xảy ra khi tải chi tiết đơn hàng');
        res.redirect('/orders');
    }
};

// Lấy danh sách đơn hàng với phân trang và lọc
exports.getAllOrders = async (req, res) => {
    try {
        // Lấy tham số từ query
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const status = req.query.status || 'all';
        const search = req.query.search || '';
        
        // Tính toán skip
        const skip = (page - 1) * limit;
        
        // Xây dựng filter
        let filter = {};
        
        // Lọc theo trạng thái nếu không phải 'all'
        if (status !== 'all') {
            filter.status = status;
        }
        
        // Tìm kiếm theo tên khách hàng, email, số điện thoại, địa chỉ hoặc ID đơn hàng
        if (search) {
            filter.$or = [
                { customer: { $regex: search, $options: 'i' } },
                { email: { $regex: search, $options: 'i' } },
                { phone: { $regex: search, $options: 'i' } },
                { address: { $regex: search, $options: 'i' } },
                { orderId: { $regex: search, $options: 'i' } }
            ];
        }
        
        // Đếm tổng số document phù hợp với filter
        const total = await Order.countDocuments(filter);
        
        // Lấy đơn hàng với phân trang
        const orders = await Order.find(filter)
            .sort({ createdAt: -1 }) // Mới nhất trước
            .skip(skip)
            .limit(limit);
        
        // Tính toán tổng số trang
        const totalPages = Math.ceil(total / limit);
        
        res.status(200).json({
            success: true,
            orders,
            currentPage: page,
            totalPages,
            totalOrders: total
        });
    } catch (error) {
        console.error('Error in getAllOrders:', error);
        res.status(500).json({
            success: false,
            message: 'Có lỗi xảy ra khi lấy danh sách đơn hàng',
            error: error.message
        });
    }
};

// Lấy đơn hàng theo ID
exports.getOrderById = async (req, res) => {
    try {
        const order = await Order.findById(req.params.id);
        
        if (!order) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy đơn hàng'
            });
        }
        
        res.status(200).json({
            success: true,
            order
        });
    } catch (error) {
        console.error('Error in getOrderById:', error);
        res.status(500).json({
            success: false,
            message: 'Có lỗi xảy ra khi lấy thông tin đơn hàng',
            error: error.message
        });
    }
};

/**
 * Get orders for the authenticated user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getUserOrders = async (req, res) => {
    try {
        const userId = req.user._id;
        
        // Find orders for this user
        // Adjust query according to your schema (e.g., userId or customer field)
        const orders = await Order.find({ 
            // You may need to adjust this query based on your schema
            $or: [
                { userId: userId },
                { 'customer.userId': userId },
                { 'customerInfo.userId': userId }
            ]
        }).sort({ createdAt: -1 });
        
        res.status(200).json({
            success: true,
            count: orders.length,
            orders
        });
    } catch (error) {
        console.error('Error fetching user orders:', error);
        res.status(500).json({
            success: false,
            message: 'Có lỗi xảy ra khi lấy danh sách đơn hàng',
            error: error.message
        });
    }
};

// Tạo đơn hàng mới
exports.createOrder = async (req, res) => {
    try {
        const {
            customer,
            email,
            phone,
            address,
            totalAmount,
            items,
            paymentMethod,
            notes
        } = req.body;
        
        // Validate required fields
        const validationErrors = {};
        
        if (!customer || customer.trim() === '') {
            validationErrors.customer = "Tên khách hàng không được để trống";
        }
        
        if (!email || email.trim() === '') {
            validationErrors.email = "Email không được để trống";
        }
        
        if (!phone || phone.trim() === '') {
            validationErrors.phone = "Số điện thoại không được để trống";
        } else if (!validatePhoneNumber(phone)) {
            validationErrors.phone = "Số điện thoại không hợp lệ";
        }
        
        if (!address || address.trim() === '') {
            validationErrors.address = "Địa chỉ không được để trống";
        }
        
        if (!items || !Array.isArray(items) || items.length === 0) {
            validationErrors.items = "Đơn hàng phải có ít nhất một sản phẩm";
        }
        
        // Return validation errors if any
        if (Object.keys(validationErrors).length > 0) {
            return res.status(400).json({
                success: false,
                message: "Thông tin đặt tour không hợp lệ",
                validationErrors
            });
        }
        
        // Check if phone is verified
        const phoneVerified = await isPhoneVerified(phone);
        
        if (!phoneVerified) {
            return res.status(403).json({
                success: false,
                message: "Số điện thoại chưa được xác minh",
                requirePhoneVerification: true,
                validationErrors: {
                    phone: "Số điện thoại phải được xác minh trước khi đặt tour"
                }
            });
        }
        
        // Tạo đơn hàng mới
        const order = new Order({
            customer,
            email,
            phone,
            address,
            totalAmount,
            items,
            paymentMethod,
            notes,
            createdBy: req.user ? req.user.fullName || req.user.email : 'System'
        });
        
        // Lưu đơn hàng
        await order.save();
        
        res.status(201).json({
            success: true,
            message: 'Đặt tour thành công',
            order
        });
    } catch (error) {
        console.error('Error in createOrder:', error);
        res.status(500).json({
            success: false,
            message: 'Có lỗi xảy ra khi đặt tour',
            error: error.message
        });
    }
};

// Cập nhật trạng thái đơn hàng
exports.updateOrderStatus = async (req, res) => {
    try {
        const { status, paymentStatus } = req.body;
        
        // Tìm đơn hàng
        const order = await Order.findById(req.params.id);
        
        if (!order) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy đơn hàng'
            });
        }
        
        // Cập nhật trạng thái
        if (status) order.status = status;
        if (paymentStatus) order.paymentStatus = paymentStatus;
        order.updatedBy = req.user ? req.user.fullName || req.user.email : 'System';
        
        // Lưu đơn hàng đã cập nhật
        await order.save();
        
        res.status(200).json({
            success: true,
            message: 'Cập nhật trạng thái đơn hàng thành công',
            order
        });
    } catch (error) {
        console.error('Error in updateOrderStatus:', error);
        res.status(500).json({
            success: false,
            message: 'Có lỗi xảy ra khi cập nhật trạng thái đơn hàng',
            error: error.message
        });
    }
};

// Xóa đơn hàng
exports.deleteOrder = async (req, res) => {
    try {
        // Vì lý do bảo mật, chúng tôi đã vô hiệu hóa việc xóa thực tế
        return res.status(403).json({
            success: false,
            message: 'Chức năng xóa đơn hàng đã bị vô hiệu hóa'
        });
        
        /* Original implementation kept for reference
        const order = await Order.findByIdAndDelete(req.params.id);
        
        if (!order) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy đơn hàng'
            });
        }
        
        res.status(200).json({
            success: true,
            message: 'Xóa đơn hàng thành công'
        });
        */
    } catch (error) {
        console.error('Error in deleteOrder:', error);
        res.status(500).json({
            success: false,
            message: 'Có lỗi xảy ra khi xóa đơn hàng',
            error: error.message
        });
    }
};
