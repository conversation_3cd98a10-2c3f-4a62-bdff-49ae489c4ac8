<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chỉnh sửa điểm khởi hành</title>
    <!-- Embed Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
        href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap"
        rel="stylesheet"
    />
    <!-- Bootstrap CSS -->
    <link
        href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"
        rel="stylesheet"
    />
    <!-- Font Awesome -->
    <link
        rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />        <!-- Select2 CSS -->
        <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
        <!-- CSS -->
        <link rel="stylesheet" href="/css/main.css" />
    </head>
<body>
    <div class="dashboard">
        <div class="container_fuild">
            <div class="dashboard__inner">
                <%- include('../partials/sidebar') %>
                <div class="departure">
                    <div class="departure-form">
                        <div class="departure-form__header">
                            <h3>Chỉnh sửa điểm khởi hành</h3>
                        </div>
                        
                        <!-- Notification Messages -->
                        <% if (message && message.length > 0) { %>
                        <div
                            class="modal-notify modal-notify--active modal-notify--success"
                        >
                            <div class="modal-notify__content">
                                <span class="modal-notify__message">
                                    <%= message[0] %>
                                </span>
                                <button
                                    class="modal-notify__close"
                                    onclick="hideToastNotification(this.closest('.modal-notify'))"
                                >
                                    ×
                                </button>
                            </div>
                        </div>
                        <% } %> 
                        <% if (error && error.length > 0) { %>
                        <div
                            class="modal-notify modal-notify--active modal-notify--error"
                        >
                            <div class="modal-notify__content">
                                <span class="modal-notify__message">
                                    <%= error[0] %>
                                </span>
                                <button
                                    class="modal-notify__close"
                                    onclick="hideToastNotification(this.closest('.modal-notify'))"
                                >
                                    ×
                                </button>
                            </div>
                        </div>
                        <% } %>
                        <div class="departure-form__body">
                        <% if (userPermissions && userPermissions.includes('UPDATE_DEPARTURE')) { %>
                            <form method="POST" action="/departure/edit/<%= departure._id %>">
                                <%- include('../partials/csrf-token') %>
                                <div class="mb-4">
                                    <label for="name" class="form-label">
                                        Tên điểm khởi hành
                                    </label>
                                    <input type="text" class="form-control" id="name" name="name" value="<%= departure.name %>" placeholder="Nhập tên điểm khởi hành..." required>
                                </div>
                                <div class="mb-4">
                                    <label for="description" class="form-label">
                                        Mô tả
                                    </label>
                                    <textarea class="form-control" id="description" name="description" rows="4" placeholder="Nhập mô tả điểm khởi hành..."><%= departure.description %></textarea>
                                </div>
                                <div class="mb-4">
                                    <label for="status" class="form-label">
                                        Trạng thái
                                    </label>
                                    <select class="form-select select2" id="status" name="status" required>
                                        <option value="Hoạt động" <%= departure.status === 'Hoạt động' ? 'selected' : '' %>>
                                            <i class="fas fa-check-circle"></i> Hoạt động
                                        </option>
                                        <option value="Không hoạt động" <%= departure.status === 'Không hoạt động' ? 'selected' : '' %>>
                                                <i class="fas fa-pause-circle"></i> Tạm dừng
                                        </option>
                                    </select>
                                </div>
                                <div class="d-flex justify-content-between gap-3">
                                    <a href="/departure" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Quay lại
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        Cập nhật
                                    </button>
                                </div>
                            </form>
                            <% } else { %>
                            <div class="alert alert-danger mt-3">Bạn không có quyền chỉnh sửa điểm khởi hành.</div>
                            <% } %>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/toast-auto-hide.js?v=<%= Date.now() %>"></script>
    <script src="/js/departure.js?v=<%= Date.now() %>"></script>
    <script src="/js/selectEdit.js?v=<%= Date.now() %>"></script>
</body>
</html>
