const express = require("express");
const { getHomepage } = require("../controllers/homeController");
const { ensureAuthenticated } = require("../middleware/authMiddleware");
const { loadUserPermissions } = require("../middleware/permissionMiddleware");
const router = express.Router();

// Import permission routes
const permissionRoutes = require("./permissionRoute");

// Middleware để authentication cho tất cả routes
router.use(ensureAuthenticated);

// Chuyển hướng đến trang dashboard
router.get("/", (req, res) => { res.redirect("/dashboard"); });

// Trang dashboard
router.get("/dashboard", loadUserPermissions, getHomepage);

// Quản lý quyền
router.use("/permissions", permissionRoutes);

module.exports = router;
