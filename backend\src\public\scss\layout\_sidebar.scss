@use "../abstracts" as *;

// Sidebar
.sidebar {
    width: 220px;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
    transition: transform 0.3s ease;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);

    &__header {
        padding: 1.5rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    &__title {
        font-size: 1.8rem;
        font-weight: 600;
        margin: 0;
        color: white;

        i {
            margin-right: 0.5rem;
            color: #3498db;
        }
    }

    &__toggle {
        background: none;
        border: none;
        color: white;
        font-size: 1.2rem;
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 0.25rem;
        transition: background-color 0.2s;

        &:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }
    }

    &__nav {
        padding: 1rem 0;
    }

    &__list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    &__item {
        margin-bottom: 1rem;
        border-radius: 0 25px 25px 0;
        margin-right: 10px;
        overflow: hidden;
        transition: all 0.3s ease;

        &:hover {
            margin-right: 5px;
            background: rgba(255, 255, 255, 0.05);
        }
    }

    &__link {
        display: flex;
        align-items: center;
        padding: 0.75rem 1.5rem;
        color: rgba(255, 255, 255, 0.8);
        text-decoration: none;
        transition: all 0.3s ease;
        border-left: 3px solid transparent;
        position: relative;

        &:hover {
            background-color: rgba(255, 255, 255, 0.15);
            color: white;
            text-decoration: none;
            transform: translateX(2px);

            .sidebar__icon {
                color: #3498db;
                transform: scale(1.1);
            }
        }

        &:focus {
            outline: none;
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        &--active {
            background-color: rgba(52, 152, 219, 0.3);
            color: white;
            border-left-color: #3498db;
            box-shadow: inset 0 0 10px rgba(52, 152, 219, 0.2);

            .sidebar__icon {
                color: #3498db;
                transform: scale(1.05);
            }

            &:hover {
                background-color: rgba(52, 152, 219, 0.4);
                transform: translateX(0);
            }
        }

        &:active {
            transform: translateX(1px);
            background-color: rgba(52, 152, 219, 0.25);
        }
    }

    &__icon {
        width: 1.25rem;
        margin-right: 0.75rem;
        color: inherit;
        font-size: 1.1rem;
        transition: all 0.3s ease;
    }

    &__text {
        font-weight: 500;
        font-size: 1.4rem;
        transition: all 0.3s ease;
    }
}

// Mobile Toggle
.mobile-toggle {
    position: fixed;
    top: 1rem;
    left: 1rem;
    z-index: 1001;
    background: #2c3e50;
    color: white;
    border: none;
    border-radius: 0.375rem;
    padding: 0.75rem;
    font-size: 1.1rem;
    cursor: pointer;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.2s ease;

    &:hover {
        background: #34495e;
        transform: translateY(-1px);
        box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
    }

    @media (min-width: 992px) {
        display: none !important;
    }
}

// Overlay
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;

    &--active {
        opacity: 1;
        visibility: visible;
    }
}

// Mobile Sidebar
@media (max-width: 991.98px) {
    .sidebar {
        transform: translateX(-100%);

        &--open {
            transform: translateX(0);
        }
    }
}

// Ripple effect for sidebar links
.sidebar__link {
    position: relative;
    overflow: hidden;
    padding: 1.5rem;

    &::before {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: width 0.6s, height 0.6s;
        z-index: 0;
    }

    &:active::before {
        width: 200px;
        height: 200px;
    }

    * {
        position: relative;
        z-index: 1;
    }
}
// Pulse animation for active link
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(52, 152, 219, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(52, 152, 219, 0);
    }
}

.sidebar__link--active {
    animation: pulse 2s infinite;
}

