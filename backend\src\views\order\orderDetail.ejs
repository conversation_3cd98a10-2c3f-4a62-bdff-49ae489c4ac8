<% var userPermissions = typeof userPermissions !== 'undefined' ? userPermissions : []; %>
<!DOCTYPE html>
<html lang="vi">
    <head>
        <meta charset="UTF-8" />
        <title><%= title %></title>
        <!-- Embed Font -->
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
        <link
            href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap"
            rel="stylesheet"
        />
        <!-- Bootstrap CSS -->
        <link
            href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"
            rel="stylesheet"
        />
        <!-- Font Awesome -->
        <link
            rel="stylesheet"
            href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
        />
        <!-- CSS -->
        <link rel="stylesheet" href="/css/main.css" />
    </head>
    <body>
        <div class="dashboard">
            <div class="container_fuild">
                <div class="dashboard__inner">
                    <%- include('../partials/sidebar') %>
                    <div class="order">
                        <div class="order__header">
                            <h2 class="order__title">Chi tiết đơn hàng</h2>
                            <a href="/orders" class="order__btn order__btn--outline">
                                <i class="fas fa-arrow-left me-2"></i>Quay lại
                            </a>
                        </div>
                        
                        <!-- Notification Messages -->
                        <% if (messages.success && messages.success.length > 0) { %>
                        <div class="modal-notify modal-notify--active modal-notify--success">
                            <div class="modal-notify__content">
                                <span class="modal-notify__message">
                                    <i class="fas fa-check-circle me-2"></i><%= messages.success %>
                                </span>
                                <button class="modal-notify__close" onclick="this.parentElement.parentElement.style.display='none'">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <% } %>
                        
                        <% if (messages.error && messages.error.length > 0) { %>
                        <div class="modal-notify modal-notify--active modal-notify--error">
                            <div class="modal-notify__content">
                                <span class="modal-notify__message">
                                    <i class="fas fa-exclamation-circle me-2"></i><%= messages.error %>
                                </span>
                                <button class="modal-notify__close" onclick="this.parentElement.parentElement.style.display='none'">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <% } %>
                        
                        <div class="order-detail">
                            <!-- Thông tin người đặt hàng -->
                            <div class="order-detail__section">
                                <h3 class="order-detail__section-title">Thông tin người đặt hàng</h3>
                                <div class="order-detail__grid">
                                    <div class="order-detail__row">
                                        <div class="order-detail__label">Họ và tên:</div>
                                        <div class="order-detail__value"><%= order.customer %></div>
                                    </div>
                                    <div class="order-detail__row">
                                        <div class="order-detail__label">Email:</div>
                                        <div class="order-detail__value"><%= order.email %></div>
                                    </div>
                                    <div class="order-detail__row">
                                        <div class="order-detail__label">Số điện thoại:</div>
                                        <div class="order-detail__value"><%= order.phone %></div>
                                    </div>
                                    <div class="order-detail__row">
                                        <div class="order-detail__label">Địa chỉ:</div>
                                        <div class="order-detail__value"><%= order.address %></div>
                                    </div>
                                    <div class="order-detail__row">
                                        <div class="order-detail__label">Trạng thái:</div>
                                        <div class="order-detail__value">
                                            <% if (order.status === 'pending') { %>
                                                <span class="order__badge order__badge--pending">Đang chờ</span>
                                            <% } else if (order.status === 'confirmed') { %>
                                                <span class="order__badge order__badge--confirmed">Đã xác nhận</span>
                                            <% } else if (order.status === 'cancelled') { %>
                                                <span class="order__badge order__badge--cancelled">Đã hủy</span>
                                            <% } %>
                                        </div>
                                    </div>
                                    <div class="order-detail__row">
                                        <div class="order-detail__label">Ngày đặt:</div>
                                        <div class="order-detail__value">
                                            <%= new Date(order.createdAt).toLocaleString('vi-VN') %>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Thông tin thanh toán -->
                            <div class="order-detail__section">
                                <h3 class="order-detail__section-title">Thông tin thanh toán</h3>
                                <div class="order-detail__grid">
                                    <div class="order-detail__row">
                                        <div class="order-detail__label">Mã giao dịch:</div>
                                        <div class="order-detail__value"><%= order.orderId %></div>
                                    </div>
                                    <div class="order-detail__row">
                                        <div class="order-detail__label">Số tiền:</div>
                                        <div class="order-detail__value">
                                            <%= new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(order.totalAmount) %>
                                        </div>
                                    </div>
                                    <div class="order-detail__row">
                                        <div class="order-detail__label">Phương thức thanh toán:</div>
                                        <div class="order-detail__value">
                                            <% 
                                            let paymentMethod = '';
                                            switch(order.paymentMethod) {
                                                case 'cash': paymentMethod = 'Tiền mặt'; break;
                                                case 'bank_transfer': paymentMethod = 'Chuyển khoản'; break;
                                                case 'credit_card': paymentMethod = 'Thẻ tín dụng'; break;
                                                case 'momo': paymentMethod = 'MoMo'; break;
                                                case 'zalo_pay': paymentMethod = 'ZaloPay'; break;
                                                default: paymentMethod = order.paymentMethod;
                                            }
                                            %>
                                            <%= paymentMethod %>
                                        </div>
                                    </div>
                                    <div class="order-detail__row">
                                        <div class="order-detail__label">Trạng thái thanh toán:</div>
                                        <div class="order-detail__value">
                                            <% if (order.paymentStatus === 'pending') { %>
                                                <span class="order__badge order__badge--unpaid">Chưa thanh toán</span>
                                            <% } else if (order.paymentStatus === 'completed') { %>
                                                <span class="order__badge order__badge--completed">Đã thanh toán</span>
                                            <% } %>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Thông tin đơn hàng -->
                            <div class="order-detail__section">
                                <h3 class="order-detail__section-title">Thông tin đơn hàng</h3>
                                <div class="order-detail__grid">
                                    <div class="order-detail__row">
                                        <div class="order-detail__label">Mã đơn hàng:</div>
                                        <div class="order-detail__value"><%= order.orderId %></div>
                                    </div>
                                    <div class="order-detail__row">
                                        <div class="order-detail__label">Số lượng người lớn:</div>
                                        <div class="order-detail__value">
                                            <% 
                                            let totalAdults = 0;
                                            if (order.items && order.items.length > 0) {
                                                order.items.forEach(item => {
                                                    totalAdults += item.adults || 0;
                                                });
                                            }
                                            %>
                                            <%= totalAdults %>
                                        </div>
                                    </div>
                                    <div class="order-detail__row">
                                        <div class="order-detail__label">Giá người lớn:</div>
                                        <div class="order-detail__value">
                                            <% 
                                            let adultPrice = 0;
                                            if (order.items && order.items.length > 0 && order.items[0].price) {
                                                adultPrice = order.items[0].price;
                                            }
                                            %>
                                            <%= new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(adultPrice) %>
                                        </div>
                                    </div>
                                    <div class="order-detail__row">
                                        <div class="order-detail__label">Số lượng trẻ em:</div>
                                        <div class="order-detail__value">
                                            <% 
                                            let totalChildren = 0;
                                            if (order.items && order.items.length > 0) {
                                                order.items.forEach(item => {
                                                    totalChildren += item.children || 0;
                                                });
                                            }
                                            %>
                                            <%= totalChildren %>
                                        </div>
                                    </div>
                                    <div class="order-detail__row">
                                        <div class="order-detail__label">Giá trẻ em:</div>
                                        <div class="order-detail__value">
                                            <% 
                                            let childPrice = 0;
                                            if (order.items && order.items.length > 0) {
                                                // Giả sử giá trẻ em là một nửa giá người lớn
                                                childPrice = order.items[0].price * 0.5;
                                            }
                                            %>
                                            <%= new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(childPrice) %>
                                        </div>
                                    </div>
                                    <div class="order-detail__row">
                                        <div class="order-detail__label">Số lượng trẻ nhỏ:</div>
                                        <div class="order-detail__value">0</div>
                                    </div>
                                    <div class="order-detail__row">
                                        <div class="order-detail__label">Giá trẻ nhỏ:</div>
                                        <div class="order-detail__value">0 VND</div>
                                    </div>
                                    <div class="order-detail__row">
                                        <div class="order-detail__label">Số lượng em bé:</div>
                                        <div class="order-detail__value">0</div>
                                    </div>
                                    <div class="order-detail__row">
                                        <div class="order-detail__label">Giá em bé:</div>
                                        <div class="order-detail__value">0 VND</div>
                                    </div>
                                    <div class="order-detail__row">
                                        <div class="order-detail__label">Ghi chú:</div>
                                        <div class="order-detail__value"><%= order.notes || 'Không có ghi chú' %></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Thông tin tour -->
                            <% if (order.items && order.items.length > 0 && order.items[0].tourId) { %>
                            <div class="order-detail__section">
                                <h3 class="order-detail__section-title">Thông tin tour</h3>
                                <div class="order-detail__tour">
                                    <div class="order-detail__tour-info">
                                        <div class="order-detail__row">
                                            <div class="order-detail__label">Tên tour:</div>
                                            <div class="order-detail__value"><%= order.items[0].name || 'Không có thông tin' %></div>
                                        </div>
                                        <div class="order-detail__row">
                                            <div class="order-detail__label">Mã tour:</div>
                                            <div class="order-detail__value"><%= tourInfo ? tourInfo.code : 'Không có thông tin' %></div>
                                        </div>
                                        <div class="order-detail__row">
                                            <div class="order-detail__label">Trạng thái:</div>
                                            <div class="order-detail__value">
                                                <% if (tourInfo && tourInfo.status) { %>
                                                    <span class="order__badge order__badge--confirmed">Đang hoạt động</span>
                                                <% } else { %>
                                                    <span class="order__badge order__badge--cancelled">Không hoạt động</span>
                                                <% } %>
                                            </div>
                                        </div>
                                    </div>
                                    <% if (tourInfo && tourInfo.image) { %>
                                    <div class="order-detail__tour-image">
                                        <img src="<%= tourInfo.image %>" alt="<%= order.items[0].name %>">
                                    </div>
                                    <% } %>
                                </div>
                            </div>
                            <% } %>

                            <!-- Nút hành động -->
                            <div class="order-detail__actions">
                                <a href="/orders" class="order__btn order__btn--outline">
                                    <i class="fas fa-arrow-left me-2"></i>Quay lại
                                </a>
                                <button class="order__btn order__btn--primary" onclick="printOrder()">
                                    <i class="fas fa-print me-2"></i>In đơn hàng
                                </button>
                                <% if (order.status === 'pending') { %>
                                <button class="order__btn order__btn--success" onclick="updateOrderStatus('<%= order._id %>', 'confirmed')">
                                    <i class="fas fa-check me-2"></i>Xác nhận đơn
                                </button>
                                <button class="order__btn order__btn--danger" onclick="updateOrderStatus('<%= order._id %>', 'cancelled')">
                                    <i class="fas fa-times me-2"></i>Hủy đơn
                                </button>
                                <% } %>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Thêm CSRF token cho JavaScript -->
        <script>
            window.csrfToken = "<%= csrfToken %>";
            
            // Hàm in đơn hàng
            function printOrder() {
                window.print();
            }
            
            // Hàm cập nhật trạng thái đơn hàng
            function updateOrderStatus(orderId, status) {
                if (!confirm('Bạn có chắc chắn muốn ' + (status === 'confirmed' ? 'xác nhận' : 'hủy') + ' đơn hàng này?')) {
                    return;
                }
                
                fetch(`/api/orders/${orderId}/status`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': window.csrfToken
                    },
                    body: JSON.stringify({
                        status: status,
                        _csrf: window.csrfToken
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Hiển thị thông báo thành công
                        alert('Cập nhật trạng thái đơn hàng thành công');
                        // Tải lại trang
                        window.location.reload();
                    } else {
                        alert(data.message || 'Có lỗi xảy ra khi cập nhật trạng thái đơn hàng');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Có lỗi xảy ra khi cập nhật trạng thái đơn hàng');
                });
            }
        </script>

        <!-- Scripts -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
        <!-- Session Security -->
        <script src="/js/session-security.js?v=<%= Date.now() %>"></script>
    </body>
</html>
