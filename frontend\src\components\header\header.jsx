import { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { PhoneIcon, Bars3Icon } from '@heroicons/react/24/outline'
import userImg from '../../assets/images/user.png'
import iconImg from '../../assets/icons/icon_lang_vi.png'

const Header = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen)
  }

  return (
    <nav className="header bg-white shadow-sm">
      <div className="header--wrapper">
        {/* Sub Header */}
        <div className="subHeader bg-blue-100 border-b border-gray-200">
          <div className="subHeader--wrapper section-container max-w-7xl mx-auto px-4">
            <div className="flex justify-between items-center">
              {/* Left side - Phone */}
              <div className="subHeader-left flex items-center space-x-4">
                <div className="subHeader-left--phone flex items-center space-x-2">
                  <PhoneIcon className="w-5 h-5 text-gray-600" />
                  <span>  
                    <a 
                      className="phone-font-weight text-red-600 font-bold" 
                      href="tel:0972222555"
                    >
                      0972 222 555
                    </a>
                  </span>
                </div>
                <small className="text-black text-sm hidden sm:block">
                  - Từ 8:00 - 23:00 hằng ngày
                </small>
              </div>

              {/* Right side - Language & User */}
              <div className="subHeader-right flex items-center space-x-4">
                {/* Language & Currency */}
                <button className="navLangCur flex items-center space-x-2 py-1 rounded">
                  <img
                    src={iconImg}
                    alt="Tiếng Việt"
                    className="w-5 h-5"
                    onError={(e) => {
                      e.target.style.display = 'none'
                    }}
                  />
                  <div className="hasDivider h-rem-2 w-px bg-gray-300"></div>
                  <p className="text-sm font-medium">VND</p>
                </button>

                {/* User Menu */}
                <div className="navMenu">
                  <Link
                    to="/login"
                    title="Đăng nhập tài khoản"
                    aria-label="Đăng nhập tài khoản"
                    className="flex items-center p-2 rounded transition-colors"
                  >
                    <img
                      alt="user"
                      loading="lazy"
                      width="22"
                      height="22"
                      className="w-5 h-5"
                      src={userImg}
                      onError={(e) => {
                        e.target.style.display = 'none'
                        e.target.parentNode.innerHTML = '<svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path></svg>'
                      }}
                    />
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Header */}
        <div className="mainHeader bg-white">
          <div className="mainHeader--wrapper section-container max-w-7xl mx-auto px-4">
            <div className="flex justify-between items-center py-4">
              {/* Logo */}
              <div className="mainHeader-left">
                <Link to="/" className="logo">
                  <img 
                    alt="Vietravel Logo" 
                    width="195" 
                    height="40" 
                    className="h-10 w-auto"
                    src="https://media.travel.com.vn/logos/vtv-logo.jpg"
                    onError={(e) => {
                      e.target.src = '/images/logo-fallback.png'
                    }}
                  />
                </Link>
              </div>

              {/* Desktop Navigation */}
              <div className="mainHeader-right hidden lg:block">
                <ul className="mainHeader-navigate--list flex items-center space-x-8">
                  <li>
                    <div className="navItem__container navItem__container__hoverOnly group">
                      <p className="navItem__url false cursor-pointer text-gray-700 hover:text-red-600 font-medium transition-colors">
                        Điểm đến
                      </p>
                    </div>
                  </li>
                  <li>
                    <div className="navItem__container navItem__container__hoverOnly group">
                      <a
                        className="navItem__url text-gray-700 hover:text-red-600 font-medium transition-colors"
                        href="https://vietravelmice.com/"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        Vietravel MICE
                      </a>
                    </div>
                  </li>
                  <li>
                    <div className="navItem__container navItem__container__hoverOnly group">
                      <a
                        className="navItem__url text-gray-700 hover:text-red-600 font-medium transition-colors"
                        href="https://vietravelplus.com/"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        Vietravel Loyalty
                      </a>
                    </div>
                  </li>
                  <li>
                    <div className="navItem__container navItem__container__hoverOnly group">
                      <a
                        className="navItem__url text-gray-700 hover:text-red-600 font-medium transition-colors"
                        href="/lien-he.aspx"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        Liên hệ
                      </a>
                    </div>
                  </li>
                  <li>
                    <div className="navItem__container navItem__container__hoverOnly group">
                      <p className="navItem__url false cursor-pointer text-gray-700 hover:text-red-600 transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none">
                          <path fill="#000" d="M5 6.947C5 6.424 5.409 6 5.913 6h12.174c.504 0 .913.424.913.947 0 .524-.409.948-.913.948H5.913C5.409 7.895 5 7.47 5 6.947M5 12c0-.523.409-.947.913-.947h12.174c.504 0 .913.424.913.947s-.409.947-.913.947H5.913C5.409 12.947 5 12.523 5 12M5.913 16.105c-.504 0-.913.424-.913.948 0 .523.409.947.913.947h12.174c.504 0 .913-.424.913-.947 0-.524-.409-.948-.913-.948z"></path>
                        </svg>
                      </p>
                    </div>
                  </li>
                </ul>
              </div>

              {/* Mobile Menu Button */}
              <div className="lg:hidden">
                <button
                  onClick={toggleMobileMenu}
                  className="p-2 rounded-md text-gray-700 hover:text-red-600 hover:bg-gray-100 transition-colors"
                >
                  <Bars3Icon className="w-6 h-6" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="lg:hidden bg-white border-t border-gray-200 shadow-lg">
            <div className="px-4 py-4 space-y-4">
              <a href="#" className="block text-gray-700 hover:text-red-600 font-medium transition-colors">
                Điểm đến
              </a>
              <a 
                href="https://vietravelmice.com/" 
                target="_blank" 
                rel="noopener noreferrer"
                className="block text-gray-700 hover:text-red-600 font-medium transition-colors"
              >
                Vietravel MICE
              </a>
              <a 
                href="https://vietravelplus.com/" 
                target="_blank" 
                rel="noopener noreferrer"
                className="block text-gray-700 hover:text-red-600 font-medium transition-colors"
              >
                Vietravel Loyalty
              </a>
              <a
                href="/lien-he.aspx"
                target="_blank"
                rel="noopener noreferrer"
                className="block text-gray-700 hover:text-red-600 font-medium transition-colors"
              >
                Liên hệ
              </a>
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}

export default Header
