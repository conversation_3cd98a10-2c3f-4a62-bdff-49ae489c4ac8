import { useState } from 'react'
import { Link, useNavigate, useLocation } from 'react-router-dom'
import ReCAPTCHA from 'react-google-recaptcha'
import { useAuth } from '../../hooks/useAuth'
import { EyeIcon, EyeSlashIcon, UserIcon } from '@heroicons/react/24/outline'
import Loading from '../../components/common/Loading/Loading'
import logoImg from '../../assets/images/vtv-logo.png'

const Login = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  })
  const [errors, setErrors] = useState({})
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [recaptchaValue, setRecaptchaValue] = useState(null)
  
  const { login } = useAuth()
  const navigate = useNavigate()
  const location = useLocation()

  // <PERSON><PERSON>n thị thông báo từ register page
  const successMessage = location.state?.message
  const errorMessage = location.state?.error

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  const validateForm = () => {
    const newErrors = {}
    
    if (!formData.username.trim()) {
      newErrors.username = 'Vui lòng nhập số điện thoại hoặc email'
    } else {
      const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.username)
      const isPhone = /^[0-9]{10,11}$/.test(formData.username)
      
      if (!isEmail && !isPhone) {
        newErrors.username = 'Vui lòng nhập email hợp lệ hoặc số điện thoại 10-11 số'
      }
    }
    
    if (!formData.password) {
      newErrors.password = 'Vui lòng nhập mật khẩu'
    }

    if (!recaptchaValue) {
      newErrors.recaptcha = 'Vui lòng xác thực reCAPTCHA'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!validateForm()) return
    
    setIsLoading(true)
    try {
      await login({
        username: formData.username.trim(),
        password: formData.password,
        recaptcha: recaptchaValue
      })
      
      // Redirect to home page after successful login
      const from = location.state?.from?.pathname || '/'
      navigate(from)
    } catch (error) {
      setErrors({
        submit: error.message || 'Đăng nhập thất bại'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSocialLogin = (provider) => {
    window.location.href = `${import.meta.env.VITE_API_URL}/auth/${provider}`
  }

  const onRecaptchaChange = (value) => {
    setRecaptchaValue(value)
    if (errors.recaptcha) {
      setErrors(prev => ({ ...prev, recaptcha: '' }))
    }
  }

  if (isLoading) {
    return <Loading text="Đang đăng nhập..." />
  }

  return (
    <>
    {/* Logo Vietravel */}
    <div className="text-center">
      <div className="flex items-center justify-center">
        <img className='w-80 h-auto mt-4' src={logoImg} alt="Viet travel" />
      </div>
    </div>
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4 mb-16">
      <div className="bg-white rounded-2xl shadow-xl w-full max-w-lg p-10 animate-fade-in">
        {/* Title */}
        <h1 className="text-4xl font-semibold text-center text-sky-800 mb-8">Đăng nhập</h1>

        {/* Success/Error Messages */}
        {successMessage && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-6">
            {successMessage}
          </div>
        )}
        
        {errorMessage && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
            {errorMessage}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {errors.submit && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
              {errors.submit}
            </div>
          )}

          {/* Username Field */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Số điện thoại hoặc email <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <input
                type="text"
                name="username"
                value={formData.username}
                onChange={handleChange}
                className={`w-full px-4 py-3 pr-10 bg-gray-50 border-0 border-b-2 border-gray-200 focus:border-blue-500 focus:bg-white focus:outline-none transition-all duration-300 ${
                  errors.username ? 'border-red-400 bg-red-50' : ''
                }`}
                placeholder="Số điện thoại hoặc email"
              />
              <UserIcon className="absolute right-3 top-3 h-5 w-5 text-gray-400" />
            </div>
            {errors.username && (
              <p className="text-red-500 text-sm mt-1">{errors.username}</p>
            )}
          </div>

          {/* Password Field */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Mật khẩu <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <input
                type={showPassword ? 'text' : 'password'}
                name="password"
                value={formData.password}
                onChange={handleChange}
                className={`w-full px-4 py-3 pr-10 bg-gray-50 border-0 border-b-2 border-gray-200 focus:border-blue-500 focus:bg-white focus:outline-none transition-all duration-300 ${
                  errors.password ? 'border-red-400 bg-red-50' : ''
                }`}
                placeholder="Nhập mật khẩu"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-3 text-gray-400 hover:text-gray-600 transition-colors"
              >
                {showPassword ? (
                  <EyeSlashIcon className="h-5 w-5" />
                ) : (
                  <EyeIcon className="h-5 w-5" />
                )}
              </button>
            </div>
            {errors.password && (
              <p className="text-red-500 text-sm mt-1">{errors.password}</p>
            )}
          </div>

          {/* Register Link */}
          <div className="text-center">
            <Link 
              to="/register" 
              className="text-sm"
            >
              Chưa là thành viên? <span className="text-sky-800 underline font-medium italic">Đăng ký ngay</span>
            </Link>
          </div>

          {/* reCAPTCHA */}
          <div className="flex justify-center">
            <ReCAPTCHA
              sitekey={import.meta.env.VITE_RECAPTCHA_SITE_KEY || "6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI"}
              onChange={onRecaptchaChange}
              theme="light"
            />
          </div>
          {errors.recaptcha && (
            <p className="text-red-500 text-sm text-center">{errors.recaptcha}</p>
          )}

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isLoading}
            className="relative left-1/2 -translate-x-1/2 w-full max-w-3xs bg-red-600 text-white font-bold py-3 px-4 rounded-2xl"
          >
            {isLoading ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                ĐANG ĐĂNG NHẬP...
              </div>
            ) : (
              'ĐĂNG NHẬP'
            )}
          </button>

          {/* Divider */}
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white font-bold">Hoặc</span>
            </div>
          </div>

          {/* Social Login Buttons */}
          <div className="space-y-3">
            {/* Facebook Login */}
            <button
              type="button"
              onClick={() => handleSocialLogin('facebook')}
              className="relative left-1/2 -translate-x-1/2 w-full max-w-3xs bg-sky-700 hover:bg-sky-800 text-white font-medium py-3 px-4 rounded-2xl flex items-center justify-center space-x-3"
            >
              <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
              </svg>
              <span>Đăng nhập với Facebook</span>
            </button>

            {/* Google Login */}
            <button
              type="button"
              onClick={() => handleSocialLogin('google')}
              className="relative left-1/2 -translate-x-1/2 w-full max-w-3xs bg-white hover:bg-gray-50 text-gray-700 font-medium py-3 px-4 rounded-2xl border border-gray-300 flex items-center justify-center space-x-3"
            >
              <svg className="w-5 h-5" viewBox="0 0 24 24">
                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              <span>Đăng nhập với Google</span>
            </button>
          </div>
        </form>
      </div>
    </div>
    </>
  )
}

export default Login