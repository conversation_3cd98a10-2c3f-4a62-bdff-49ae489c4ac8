const express = require('express');
const router = express.Router();
const settingsController = require('../controllers/settingsController');
const { ensureAuthenticated } = require('../middleware/authMiddleware');
const { loadUserPermissions, checkPermission } = require('../middleware/permissionMiddleware');

// T<PERSON><PERSON> cả routes của settings yêu cầu đăng nhập và load quyền
router.use(ensureAuthenticated, loadUserPermissions);

// Xem trang cài đặt
router.get('/', checkPermission("READ_SETTINGS"), settingsController.getSettings);

// Cập nhật cài đặt
router.post('/update', checkPermission("UPDATE_SETTINGS"), settingsController.updateSettings);

module.exports = router;
