import React, { useEffect, useState, useCallback } from "react";
import {
  useNavigate,
  useParams,
  useSearchParams,
  useLocation,
} from "react-router-dom";
import Code from "../../assets/images/code.png";
import Vitri from "../../assets/images/vitri.png";
import Time from "../../assets/images/time.png";
import Flight from "../../assets/images/flight.png";
import <PERSON><PERSON><PERSON> from "../../assets/images/celanda.png";
import { message, Select, Calendar, theme } from "antd";
import { tourApi } from "../../api/tourApi";
import moment from "moment";

const { Option } = Select;

function Tour() {
  const { slug } = useParams();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [showCalendar, setShowCalendar] = useState(false);
  const [calendarSearch, setCalendarSearch] = useState("");
  const [tours, setTours] = useState([]);
  const [departures, setDepartures] = useState([]);
  const [destinations, setDestinations] = useState([]);
  const [transportations, setTransportations] = useState([]);
  const [categories, setCategories] = useState([]);
  const [selectedBudget, setSelectedBudget] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [selectedTransportation, setSelectedTransportation] = useState(null);
  const location = useLocation();

  const fetchData = async () => {
    try {
      const query = Object.fromEntries(searchParams.entries());
      if (slug == "du-lich") {
        const response = await tourApi.getAllTours(query);
        setTours(response || []);
      } else {
        const response = await tourApi.getToursBySlug(slug, query);
        setTours(response || []);
      }
      const [
        departuresData,
        destinationsData,
        transportationsData,
        categoriesData,
      ] = await Promise.all([
        tourApi.getDepartures(),
        tourApi.getDestinations(),
        tourApi.getTransportations(),
        tourApi.getCategories(),
      ]);

      // Helper function để xử lý destination tree
      const getChildren = (data) => {
        if (!data || !Array.isArray(data)) return [];
        return data.flatMap(item => 
          item.children ? [item, ...item.children] : [item]
        );
      };

      const destinationChildrens = getChildren(destinationsData);

      setDepartures(departuresData || []);
      setDestinations(destinationChildrens || []);
      setTransportations(transportationsData || []);
      setCategories(categoriesData || []);
    } catch (error) {
      message.error("Lỗi khi tải dữ liệu tour!");
    }
  };

  useEffect(() => {
    fetchData();
    const params = new URLSearchParams(location.search);
    const budgetId = params.get("budgetId");
    const tourLine = params.get("tourLine");
    const transTypeId = params.get("transTypeId");
    const departureFrom = params.get("departureFrom");
    setSelectedBudget(budgetId);
    setSelectedCategory(tourLine);
    setSelectedTransportation(transTypeId);
  }, [slug, searchParams]);

  const { token } = theme.useToken();
  const wrapperStyle = {
    width: 400,
    border: `1px solid ${token.colorBorderSecondary}`,
    borderRadius: token.borderRadiusLG,
  };

  const formatDate = (date) => {
    const dateFormat = date.toLocaleString("vi-VN", {
      timeZone: "Asia/Ho_Chi_Minh",
      weekday: "long",
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
    });
    return dateFormat;
  };

  useEffect(() => {
    const now = new Date();
    now.setDate(now.getDate() + 1);
    const tomorrow = formatDate(now);
    if (tomorrow) {
      setCalendarSearch(tomorrow);
    }
  }, []);

  const handleClickCalendar = useCallback(() => {
    setShowCalendar((prev) => !prev);
  }, []);

  const handleChangeCalendar = (value) => {
    setShowCalendar(false);
    const date = formatDate(new Date(value));
    const formattedDate = date.split(", ")[1].split("/").reverse().join("-");
    setSearchParams((prev) => ({
      ...prev,
      fromDate: formattedDate,
    }));
    setCalendarSearch(date);
  };

  const handleAddSearchParam = (key, value) => {
    setSearchParams((prev) => {
      const params = new URLSearchParams(prev);
      params.set(key, value);
      return params;
    });
  };

  return (
    <div className="w-full relative flex flex-col justify-start items-center">
      {/* Header */}
      <div className="w-full flex flex-col items-center justify-center">
        <div className="w-[85%] my-8 mb-4 text-justify flex items-center justify-center flex-col">
          <div className="w-full overflow-hidden text-ellipsis line-clamp-1">
            <p
              className="text-lg font-semibold text-gray-900 text-justify cursor-pointer inline"
              onClick={() => navigate("/")}
            >
              Điểm đến /
            </p>
            <p className="text-[#0b5da7] pointer-events-none text-lg no-underline font-bold text-justify inline">
              {slug}
            </p>
          </div>
          <div className="my-5 w-4/5 flex items-center justify-center flex-col gap-12">
            <h1 className="uppercase text-4xl font-bold m-0 leading-normal w-4/5 flex items-center justify-center flex-col gap-12 text-[#0b5da7]">
              {slug}
            </h1>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="w-full flex flex-col items-center justify-center">
        <div className="py-8 flex flex-row justify-between items-stretch h-full gap-8 w-[85%]">
          {/* Filter Sidebar */}
          <div className="flex-1 flex flex-col justify-start items-start relative w-[30%]">
            <p className="uppercase font-bold text-2xl text-gray-900 my-2 text-left">
              Bộ lọc tìm kiếm
            </p>
            <div className="p-4 overflow-auto max-h-[95vh] top-4 sticky bg-gray-100 rounded-2xl w-[94%] flex flex-col justify-start items-start gap-6">
              {/* Budget Filter */}
              <div className="flex flex-col gap-2 w-full">
                <div className="flex flex-row justify-between mb-2">
                  <p className="text-lg font-semibold">Ngân sách:</p>
                </div>
                <div className="m-0 overflow-hidden grid grid-cols-2 gap-3 p-0">
                  {[
                    { id: 1, label: "Dưới 5 triệu" },
                    { id: 2, label: "Từ 5 đến 10 triệu" },
                    { id: 3, label: "Từ 10 - 20 triệu" },
                    { id: 4, label: "Trên 20 triệu" },
                  ].map((item) => (
                    <div
                      key={item.id}
                      className={`flex flex-col justify-center items-center text-base text-gray-600 font-semibold bg-white p-1 px-2 border border-gray-300 rounded w-[87%] cursor-pointer hover:border-[#025ab9] hover:bg-[#6ab2fe] ${
                        selectedBudget == item.id ? "!bg-[#0b5da7] !text-white" : ""
                      }`}
                      onClick={() => {
                        handleAddSearchParam("budgetId", item.id);
                        setSelectedBudget(item.id);
                      }}
                    >
                      {item.label}
                    </div>
                  ))}
                </div>
              </div>

              {/* Departure Filter */}
              <div className="w-full">
                <div className="flex flex-row justify-between mb-2">
                  <p className="text-lg font-semibold">Điểm khởi hành</p>
                </div>
                <div className="w-full my-2 relative">
                  <Select
                    className="w-full flex h-2.5 bg-white py-3 px-5 text-base font-bold border border-gray-300 rounded items-center justify-between cursor-pointer outline-none"
                    placeholder="Chọn điểm khởi hành"
                    onChange={(value) => {
                      handleAddSearchParam("departureFrom", value);
                    }}
                  >
                    <Option value="">Tất cả</Option>
                    {departures.map((departure) => (
                      <Option key={departure.id} value={departure.id}>
                        {departure.title}
                      </Option>
                    ))}
                  </Select>
                </div>
              </div>

              {/* Destination Filter */}
              <div className="w-full">
                <div className="flex flex-row justify-between mb-2">
                  <p className="text-lg font-semibold">Điểm đến</p>
                </div>
                <div className="w-full my-2 relative">
                  <Select
                    className="w-full flex h-2.5 bg-white py-3 px-5 text-base font-bold border border-gray-300 rounded items-center justify-between cursor-pointer outline-none"
                    placeholder="Chọn điểm khởi hành"
                    onChange={(value) => {
                      navigate(`/tours/${value}`);
                    }}
                  >
                    <Option value="du-lich">Tất cả</Option>
                    {destinations.map((destination) => (
                      <Option key={destination.id} value={destination.slug}>
                        {destination.title}
                      </Option>
                    ))}
                  </Select>
                </div>
              </div>

              {/* Calendar Filter */}
              <div className="relative w-full flex flex-col justify-start items-start gap-2">
                <div className="text-lg font-semibold">Ngày đi</div>
                <div className="w-full relative flex flex-col">
                  <div
                    className="bg-white border border-gray-300 rounded-lg font-semibold py-3 px-4 cursor-pointer hover:border-[#025ab9] hover:bg-[#6ab2fe]"
                    onClick={handleClickCalendar}
                  >
                    <span>{calendarSearch}</span>
                  </div>
                  {showCalendar && (
                    <div style={wrapperStyle} className="absolute top-full mt-2 z-10">
                      <Calendar
                        fullscreen={false}
                        onChange={handleChangeCalendar}
                        disabledDate={(current) =>
                          current && current <= moment().endOf("day")
                        }
                      />
                    </div>
                  )}
                </div>
              </div>

              {/* Tour Category Filter */}
              <div className="w-full">
                <div className="flex flex-row justify-between mb-2">
                  <p className="text-lg font-semibold text-gray-900">Dòng tour</p>
                </div>
                <div className="mx-auto overflow-hidden grid grid-cols-2 gap-4">
                  {categories &&
                    categories.map((item) => (
                      <button
                        key={item.id}
                        className={`border border-gray-300 rounded py-2 px-0 text-gray-600 text-base font-semibold bg-white cursor-pointer hover:border-[#025ab9] hover:bg-[#6ab2fe] ${
                          selectedCategory == item.id ? "!bg-[#0b5da7] !text-white" : ""
                        }`}
                        onClick={() => {
                          navigate(`/tours/du-lich?tourLine=${item.id}`);
                          setSelectedCategory(item.id);
                        }}
                      >
                        {item.title}
                      </button>
                    ))}
                </div>
              </div>

              {/* Transportation Filter */}
              <div className="w-full">
                <div className="flex flex-row justify-between mb-2">
                  <p className="text-lg font-semibold text-gray-900">Phương tiện</p>
                </div>
                <div className="mx-auto overflow-hidden grid grid-cols-2 gap-4">
                  {transportations &&
                    transportations.map((item) => (
                      <button
                        key={item.id}
                        className={`border border-gray-300 rounded py-2 px-0 text-gray-600 text-base font-semibold bg-white cursor-pointer hover:border-[#025ab9] hover:bg-[#6ab2fe] ${
                          selectedTransportation == item.id ? "!bg-[#0b5da7] !text-white" : ""
                        }`}
                        onClick={() => {
                          handleAddSearchParam("transTypeId", item.id);
                          setSelectedTransportation(item.id);
                        }}
                      >
                        {item.title}
                      </button>
                    ))}
                </div>
              </div>

              {/* Clear Filter Button */}
              <button
                className="bg-[#0b5da7] text-white border border-[#0b5da7] py-2 px-4 rounded-lg cursor-pointer transition-all duration-300 no-underline w-full text-base hover:bg-[#083e6e]"
                onClick={() => {
                  setSearchParams({});
                  setSelectedBudget(null);
                  setSelectedCategory(null);
                  setSelectedTransportation(null);
                }}
              >
                Xóa
              </button>
            </div>
          </div>

          {/* Tours List */}
          <div className="w-[70%] flex-[3_1] flex flex-col justify-start items-start gap-8">
            {/* List Header */}
            <div className="w-full flex flex-row justify-between pb-8 border-b border-gray-300">
              <div className="text-gray-900 flex flex-row items-center justify-between">
                <p className="font-semibold text-xl">
                  Chúng tôi tìm thấy <span className="text-2xl font-bold text-[#0b5da7]">{tours.length}</span> chương trình
                  cho quý khách
                </p>
              </div>
              <div className="w-[45%] flex flex-row items-center justify-between gap-4">
                <span className="w-[35%] text-2xl font-medium">Sắp xếp theo: </span>
                <div className="w-[65%]">
                  <div className="w-full my-2 relative">
                    <Select
                      style={{ width: 200, marginRight: 10 }}
                      placeholder="Tất cả"
                    >
                      <Option value="">Tất cả</Option>
                      <Option value="">Giá từ cao đến thấp</Option>
                      <Option value="">Giá từ thấp đến cao</Option>
                    </Select>
                  </div>
                </div>
              </div>
            </div>

            {/* Tours List Main */}
            <div className="w-full gap-8 flex flex-col justify-start items-start">
              {tours.map((item, key) => (
                <div
                  key={key}
                  className="border border-gray-300 w-full rounded-lg max-h-[30rem] h-72 flex flex-row justify-between items-stretch cursor-pointer hover:shadow-lg"
                  onClick={() => navigate(`/tour-details/${item.slug}`)}
                >
                  {/* Tour Image */}
                  <div className="w-[40%] relative">
                    <img
                      src={item.image}
                      alt=""
                      className="w-full h-full object-cover rounded-l-lg absolute top-0 z-[1]"
                    />
                    <div className="absolute z-[9] left-2.5 bottom-2.5 bg-[#025ab9] rounded-lg text-white font-medium py-2 px-2.5">
                      <span>{item.category}</span>
                    </div>
                  </div>

                  {/* Tour Content */}
                  <div className="w-[60%] p-6 flex flex-col justify-between items-start gap-4">
                    <div className="w-full flex flex-col justify-start items-start gap-4">
                      <div className="w-full gap-6 flex flex-col justify-start items-start">
                        <div className="w-full">
                          <p className="text-2xl leading-8 no-underline text-gray-900 font-bold">
                            {item.title}
                          </p>
                        </div>
                        <div className="flex flex-col justify-between items-stretch gap-4 w-full">
                          <div className="flex flex-row justify-start items-stretch">
                            <div className="flex flex-row justify-start items-center flex-1 gap-3">
                              <div className="flex flex-row justify-start items-start gap-2">
                                <img src={Code} alt="" className="w-[22px]" />
                                <label className="font-medium text-xl">Mã tour:</label>
                              </div>
                              <p className="text-gray-900 font-bold m-0 text-lg">{item.code}</p>
                            </div>
                            <div className="flex flex-row justify-start items-center flex-1 gap-3">
                              <div className="flex flex-row justify-start items-start gap-2">
                                <img src={Vitri} alt="" className="w-[22px]" />
                                <label className="font-medium text-xl">Khởi hành:</label>
                              </div>
                              <p className="text-gray-900 font-bold m-0 text-lg">{item.departure}</p>
                            </div>
                          </div>
                          <div className="flex flex-row justify-start items-stretch">
                            <div className="flex flex-row justify-start items-center flex-1 gap-3">
                              <div className="flex flex-row justify-start items-start gap-2">
                                <img src={Time} alt="" className="w-[22px]" />
                                <label className="font-medium text-xl">Thời gian: </label>
                              </div>
                              <p className="text-gray-900 font-bold m-0 text-lg">5N4Đ</p>
                            </div>
                            <div className="flex flex-row justify-start items-center flex-1 gap-3">
                              <div className="flex flex-row justify-start items-start gap-2">
                                <img src={Flight} alt="" className="w-[22px]" />
                                <label className="font-medium text-xl">Phương tiện:</label>
                              </div>
                              <p className="text-gray-900 font-bold m-0 text-lg">{item.transportation}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="w-full gap-2 flex flex-row justify-between items-end">
                      <div className="flex flex-col justify-start items-start">
                        <div>
                          <p className="text-lg font-semibold">Giá từ:</p>
                        </div>
                        <div>
                          <p className="m-0 text-2xl font-bold text-[#e01600]">
                            <span>{item.price.toLocaleString()} </span>VNĐ
                          </p>
                        </div>
                      </div>
                      <div
                        className="flex flex-row justify-end items-end"
                        onClick={() => navigate(`/tour-details/${item.slug}`)}
                      >
                        <button className="border border-gray-400 rounded-lg m-0 font-semibold bg-[#0b5da7] text-white cursor-pointer transition-all duration-300 no-underline py-4 px-6 hover:bg-[#103d65]">
                          Xem chi tiết
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Tour;