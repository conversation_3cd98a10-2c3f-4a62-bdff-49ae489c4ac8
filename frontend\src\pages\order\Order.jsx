import React, { useEffect, useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import Code from '../../assets/images/code.png'
import Vitri from '../../assets/images/vitri.png'
import Time from '../../assets/images/time.png'
import { message } from 'antd'
import { userApi } from '../../api/userApi'
import { orderApi } from '../../api/orderApi'

function Order() {
    const location = useLocation();
    const tourDetails = location.state?.tourDetails;
    const [user, setUser] = useState([]);
    const [userTT, setUserTT] = useState({});
    const [adultQuantity, setAdultQuantity] = useState(0);
    const [childrenQuantity, setChildrenQuantity] = useState(0);
    const [babyQuantity, setBabyQuantity] = useState(0);
    const [note, setNote] = useState('');
    const [paymentMethod, setPaymentMethod] = useState('');

    useEffect(() => {
        const fetchData = async () => {
            try {
                const response = await userApi.getProfile();
                setUser(response);
            } catch (error) {
                console.error('Error fetching user profile:', error);
                message.error('Không thể tải thông tin người dùng');
            }
        }
        fetchData();
    }, []);

    const handleOrder = async () => {
        const orderData = {
            fullName: user?.user?.fullName,
            email: user?.user?.email,
            phoneNumber: user?.user?.phoneNumber,
            address: user?.user?.address,
            adultPrice: tourDetails?.tour?.tourDetail?.[0]?.adultPrice || 0,
            adultQuantity,
            childrenPrice: tourDetails?.tour?.tourDetail?.[0]?.childrenPrice || 0 || 0,
            childrenQuantity,
            babyPrice: tourDetails?.tour?.tourDetail?.[0]?.babyPrice || 0 || 0,
            babyQuantity,
            singleRoomSupplementPrice: 500000,
            singleRoomSupplementQuantity: 1,
            note,
            paymentMethod,
            tourDetailId: tourDetails?.tour?.tourDetail?.[0]?.id,
            userId: user?.user?.id,
        };
        console.log(orderData)

        try {
            const response = await orderApi.bookTour(orderData);
            if (response) {
                message.success('Đặt tour thành công!');
                navigate('/');
            }
        } catch (error) {
            console.error('Lỗi khi đặt tour:', error);
            message.error('Đặt tour thất bại! Vui lòng thử lại')
        }
    };

    const navigate = useNavigate();

    return (
        <div className='flex flex-col justify-start items-center relative'>
            {/* Header */}
            <div className="w-full flex flex-col items-center justify-center">
                <div className="w-[85%]">
                    <button 
                        className="bg-transparent border-none flex items-center justify-center gap-4 cursor-pointer"
                        onClick={() => navigate(`/tour-details/${tourDetails?.tour?.slug}`)}
                    >
                        <i className="fa-solid fa-arrow-left"></i>
                        <span className="text-lg">Quay lại</span>
                    </button>
                    <h1 className="text-5xl font-bold uppercase text-center text-[#0b5da7]">
                        Đặt tour
                    </h1>
                </div>
            </div>

            {/* Page Order */}
            <div className="w-full flex flex-col items-center justify-center">
                <div className="grid grid-cols-[1.5fr_1fr] gap-8 items-stretch pb-32 w-[85%]">
                    {/* Left Side */}
                    <div className="w-full flex justify-start items-start flex-col">
                        {/* Contact Information */}
                        <div className="h-[245px]">
                            <h3 className="text-2xl font-bold uppercase text-center m-0 mb-6">
                                Thông tin liên lạc
                            </h3>
                            <form className="w-full">
                                <div className="w-full h-1/2 flex flex-row justify-between mb-4">
                                    <div className="w-[49%] p-2.5 border-r border-gray-300">
                                        <label className="text-lg font-bold">
                                            Full name <span className="text-red-500">*</span>:
                                        </label>
                                        <p className="text-sm">{user?.user?.fullName}</p>
                                    </div>
                                    <div className="w-[49%] p-2.5">
                                        <label className="text-lg font-bold">
                                            Điện thoại <span className="text-red-500">*</span>:
                                        </label>
                                        <p className="text-sm">{user?.user?.phoneNumber}</p>
                                    </div>
                                </div>
                                <div className="w-full h-1/2 flex flex-row justify-between mb-4">
                                    <div className="w-[49%] p-2.5 border-r border-gray-300">
                                        <label className="text-lg font-bold">
                                            Email <span className="text-red-500">*</span>:
                                        </label>
                                        <p className="text-sm">{user?.user?.email}</p>
                                    </div>
                                    <div className="w-[49%] p-2.5">
                                        <label className="text-lg font-bold">
                                            Địa chỉ <span className="text-red-500">*</span>:
                                        </label>
                                        <p className="text-sm">{user?.user?.address}</p>
                                    </div>
                                </div>
                            </form>
                        </div>

                        {/* Passengers */}
                        <div className="w-full flex justify-start items-start flex-col m-0 mb-9">
                            <h3 className="text-2xl font-bold uppercase text-center m-0 mb-6 ml-[5.5rem]">
                                Hành khách
                            </h3>
                            <div className="w-full">
                                <div className="booking-form grid grid-cols-2 justify-start items-start gap-8">
                                    <div className="border border-gray-900 rounded-lg p-2 pl-5 w-full">
                                        <div className="items-stretch w-full flex flex-row justify-start gap-8">
                                            <div className="text-lg w-1/2">
                                                <p>Người lớn</p>
                                                <small>Từ 12 trở lên</small>
                                            </div>
                                            <div className="w-1/2 flex justify-between items-center">
                                                <input 
                                                    type="number" 
                                                    className="w-3/5 text-center bg-transparent"
                                                    onChange={(e) => setAdultQuantity(Number(e.target.value))} 
                                                />
                                            </div>
                                        </div>
                                    </div>
                                    <div className="border border-gray-900 rounded-lg p-2 pl-5 w-full">
                                        <div className="items-stretch w-full flex flex-row justify-start gap-8">
                                            <div className="text-lg w-1/2">
                                                <p>Trẻ em</p>
                                                <small>Từ 2-11 tuổi</small>
                                            </div>
                                            <div className="w-1/2 flex justify-between items-center">
                                                <input 
                                                    type="number" 
                                                    className="w-3/5 text-center bg-transparent"
                                                    onChange={(e) => setChildrenQuantity(Number(e.target.value))} 
                                                />
                                            </div>
                                        </div>
                                    </div>
                                    <div className="border border-gray-900 rounded-lg p-2 pl-5 w-full">
                                        <div className="items-stretch w-full flex flex-row justify-start gap-8">
                                            <div className="text-lg w-1/2">
                                                <p>Em bé</p>
                                                <small>Dưới 2 tuổi</small>
                                            </div>
                                            <div className="w-1/2 flex justify-between items-center">
                                                <input 
                                                    type="number" 
                                                    className="w-3/5 text-center bg-transparent"
                                                    onChange={(e) => setBabyQuantity(Number(e.target.value))} 
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Note */}
                        <div className="order-note">
                            <h3 className="text-2xl font-bold uppercase text-center m-0 mb-6">Ghi chú</h3>
                            <span>Quý khách có ghi chú lưu ý gì, hãy nói với chúng tôi</span>
                            <div className="booking-note">
                                <textarea 
                                    placeholder='Vui lòng nhập nội dung' 
                                    className="w-full p-3 border border-gray-300 rounded-lg"
                                    onChange={(e) => setNote(e.target.value)}
                                />
                            </div>
                        </div>

                        {/* Payment Method */}
                        <div className="booking-item">
                            <h3 className="text-2xl font-bold uppercase text-center m-0 mb-6">
                                Các hình thức thanh toán
                            </h3>
                            <input 
                                type="text" 
                                className="w-full p-3 border border-gray-300 rounded-lg"
                                onChange={(e) => setPaymentMethod(e.target.value)} 
                            />
                        </div>
                    </div>

                    {/* Right Side */}
                    <div className="w-full h-full flex justify-start items-start flex-col relative">
                        <div className="flex flex-col justify-between sticky top-5 items-start">
                            <h3 className="text-2xl font-bold uppercase text-center m-0 mb-6">
                                Tóm tắt chuyến đi
                            </h3>
                            <div className="bg-gray-100 max-h-[90%] p-6 rounded-2xl items-stretch overflow-hidden relative flex flex-col justify-between">
                                {/* Card Header */}
                                <div className="w-full flex flex-row justify-between items-stretch gap-4">
                                    <div className="w-[30%] object-cover rounded-xl">
                                        <img 
                                            src={tourDetails?.tour?.images?.[0]?.source} 
                                            alt="" 
                                            className="w-full h-full rounded-xl"
                                        />
                                    </div>
                                    <div className="text-justify w-[70%]">
                                        <div className="flex flex-col justify-between items-start h-full gap-1">
                                            <h4>{tourDetails?.tour?.title}</h4>
                                            <div className="text-2xl font-normal gap-1 flex flex-row items-center">
                                                <img src={Code} alt="" />
                                                <span>Mã tour : </span>
                                                <span>HN004</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <hr className="my-3 border border-gray-300" />
                                
                                {/* Preview */}
                                <div className="flex items-center">
                                    <div className="w-1/2 flex justify-center items-center">
                                        <img src={Vitri} alt="" />
                                        Khởi hành
                                        <span>Hà Nội</span>
                                    </div>
                                    <div className="w-1/2 flex justify-center items-center">
                                        <img src={Time} alt="" />
                                        Thời gian:
                                        <span>5N4Đ</span>
                                    </div>
                                </div>
                                
                                <hr className="my-3 border border-gray-300" />
                                
                                {/* Card Footer */}
                                <div className="flex justify-between sticky bottom-0 flex-col items-stretch gap-4">
                                    <div className="w-full flex flex-row justify-start items-start gap-8">
                                        <div className="flex-1 text-2xl font-bold">Tổng tiền</div>
                                        <div className="flex-[2_1] text-right text-3xl font-bold text-[#e01600]">
                                            14.990.000đ
                                        </div>
                                    </div>
                                    <button 
                                        className="normal-case text-lg font-bold py-4 w-full rounded-lg bg-[#e01600] text-white border border-gray-400 cursor-pointer hover:bg-[#6e1c13]"
                                        onClick={handleOrder}
                                    >
                                        Đặt tour
                                    </button>
                                </div>
                            </div>

                            {/* Contact Section */}
                            <div className="flex flex-col justify-end items-end mt-6 w-full">
                                <div className="max-w-full flex gap-2 text-lg font-bold italic">
                                    <button className='bg-[#0b5da7] text-white border-none flex flex-row justify-center items-center py-3 px-3 cursor-pointer rounded-lg no-underline not-italic hover:bg-[#0d70c6]'>
                                        <i className="fa-solid fa-phone-volume"></i>
                                        <p className="flex-[1.3_1] m-0 font-bold text-xs ml-2">
                                            Gọi miễn phí qua internet
                                        </p>
                                    </button>
                                    <button className='bg-white text-[#0b5da7] border border-[#0b5da7] flex justify-center items-center py-3 px-3 cursor-pointer rounded-lg gap-1'>
                                        <i className="fa-regular fa-envelope"></i>
                                        <p className="flex-[1.3_1] m-0 font-bold text-xs">
                                            Liên hệ tư vấn
                                        </p>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default Order